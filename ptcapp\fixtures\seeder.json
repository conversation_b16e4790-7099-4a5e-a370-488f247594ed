[{"model": "auth.user", "pk": 1, "fields": {"first_name": "Admin", "is_superuser": 1, "is_active": 1, "is_staff": 1, "username": "admin2", "password": "12345678", "email": "<EMAIL>", "date_joined": "2022-05-01 00:00:00.000000"}}, {"model": "auth.user", "pk": 2, "fields": {"first_name": "Ariel", "is_superuser": 0, "is_active": 1, "is_staff": 0, "username": "ariel2", "password": "12345678", "email": "<EMAIL>", "date_joined": "2022-05-01 00:00:00.000000"}}, {"model": "ptcapp.language", "pk": 1, "fields": {"name": "Français", "created_at": "2022-05-01 00:00:00.000000", "updated_at": "2022-05-01 00:00:00.000000"}}, {"model": "ptcapp.language", "pk": 2, "fields": {"name": "<PERSON><PERSON><PERSON>", "created_at": "2022-05-01 00:00:00.000000", "updated_at": "2022-05-01 00:00:00.000000"}}, {"model": "ptcapp.language", "pk": 3, "fields": {"name": "Fon", "created_at": "2022-05-01 00:00:00.000000", "updated_at": "2022-05-01 00:00:00.000000"}}, {"model": "ptcapp.language", "pk": 4, "fields": {"name": "<PERSON><PERSON>", "created_at": "2022-05-01 00:00:00.000000", "updated_at": "2022-05-01 00:00:00.000000"}}, {"model": "ptcapp.hospital", "pk": 1, "fields": {"name": "St Porco", "address": "Porto - Adjarra", "tel": "65656565", "created_at": "2022-05-01 00:00:00.000000", "updated_at": "2022-05-01 00:00:00.000000"}}, {"model": "ptcapp.hospital", "pk": 2, "fields": {"name": "St INSTI", "address": "Lokossa - Agniedji", "tel": "65656565", "created_at": "2022-05-01 00:00:00.000000", "updated_at": "2022-05-01 00:00:00.000000"}}, {"model": "ptcapp.service", "pk": 1, "fields": {"name": "Pediatrie", "description": "Service en charge des soins des enfants", "hospital": 1, "created_at": "2022-05-01 00:00:00.000000", "updated_at": "2022-05-01 00:00:00.000000"}}, {"model": "ptcapp.service", "pk": 2, "fields": {"name": "Gynécologie", "description": "Service en charge des *", "hospital": 1, "created_at": "2022-05-01 00:00:00.000000", "updated_at": "2022-05-01 00:00:00.000000"}}, {"model": "ptcapp.speciality", "pk": 1, "fields": {"name": "Pediatre", "description": "Medecin en charge des soins infantiles", "created_at": "2022-05-01 00:00:00.000000", "updated_at": "2022-05-01 00:00:00.000000"}}, {"model": "ptcapp.profile", "pk": 1, "fields": {"firstname": "AMOUSSA", "lastname": "<PERSON><PERSON>", "tel": "30303030", "sexe": "M", "birth_date": "2022-05-15", "address": "Porto-Novo", "user": 2, "speciality": 1, "service": 1, "language": 1, "occupation": "Medecin", "assurance": "NSIA Assurance maladie", "created_at": "2022-05-01 00:00:00.000000", "updated_at": "2022-05-01 00:00:00.000000"}}]