from django.db import models
from .profile import Profile
from .pregnancy import Pregnancy


class MapPregnancyChild(models.Model):
    pregnancy = models.ForeignKey(Pregnancy, related_name='grossesse', null = True, on_delete=models.SET_NULL)
    child = models.ForeignKey(Profile, related_name='nouveau_né', null = True, on_delete=models.SET_NULL)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)