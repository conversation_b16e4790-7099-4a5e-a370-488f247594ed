# Guide de Test Postman - API PTC Care

## 🎯 Objectif

Tester toutes les fonctionnalités de l'API PTC Care sur serveur local avec Postman pour valider l'intégration mobile.

## ⚙️ Configuration Initiale

### 1. D<PERSON>marrer le Serveur Django

```bash
# Dans le dossier ptccare-web
cd /e:/ptccare-web

# Activer l'environnement virtuel
.\env\Scripts\activate

# Démarrer le serveur
python manage.py runserver

# Le serveur sera accessible sur http://localhost:8000
```

### 2. Variables d'Environnement Postman

**Créer un environnement "PTC Care Local" :**

| Variable | Valeur | Description |
|----------|--------|-------------|
| `base_url` | `http://localhost:8000` | URL de base API |
| `auth_token` | *(vide initialement)* | Token d'authentification |
| `user_id` | *(vide initialement)* | ID utilisateur connecté |
| `admin_email` | `<EMAIL>` | Email admin test |
| `admin_password` | `admin123` | Mot de passe admin |
| `agent_email` | `<EMAIL>` | Email agent test |
| `agent_password` | `agent123` | Mot de passe agent |

## 📋 Collection de Tests Postman

### **DOSSIER 1 : Tests de Connectivité**

#### 1.1 Vérifier API Disponible
```
GET {{base_url}}/api/initial-data/
```

**Tests automatiques :**
```javascript
pm.test("API est accessible", function () {
    pm.response.to.have.status(200);
});

pm.test("Réponse contient les données de référence", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('status', 'success');
    pm.expect(jsonData).to.have.property('hospitals');
    pm.expect(jsonData).to.have.property('services');
});
```

#### 1.2 Vérifier Centres de Santé
```
GET {{base_url}}/api/health-centers/
```

### **DOSSIER 2 : Authentification**

#### 2.1 Login Admin
```
POST {{base_url}}/api/login/
Content-Type: application/json

{
  "email": "{{admin_email}}",
  "password": "{{admin_password}}"
}
```

**Tests automatiques :**
```javascript
pm.test("Login admin réussi", function () {
    pm.response.to.have.status(200);
    const jsonData = pm.response.json();
    pm.expect(jsonData.status).to.eql("success");
    pm.expect(jsonData.role).to.be.oneOf(["admin", "docteur"]);
    
    // Sauvegarder le token
    pm.environment.set("auth_token", "Bearer " + jsonData.user_id);
    pm.environment.set("user_id", jsonData.user_id);
});

pm.test("Données utilisateur complètes", function () {
    const jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('username');
    pm.expect(jsonData).to.have.property('name');
    pm.expect(jsonData).to.have.property('profile_id');
});
```

#### 2.2 Login Agent
```
POST {{base_url}}/api/login/
Content-Type: application/json

{
  "email": "{{agent_email}}",
  "password": "{{agent_password}}"
}
```

#### 2.3 Login avec Email Invalide
```
POST {{base_url}}/api/login/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "wrongpassword"
}
```

**Tests automatiques :**
```javascript
pm.test("Login invalide échoue", function () {
    pm.response.to.have.status(401);
    const jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('error');
});
```

#### 2.4 Test Login Mobile (Compatibilité)
```
POST {{base_url}}/api/login/
Content-Type: application/json
User-Agent: PTC-Care-Mobile/1.0

{
  "email": "{{admin_email}}",
  "password": "{{admin_password}}"
}
```

### **DOSSIER 3 : Gestion Utilisateurs (Admin)**

#### 3.1 Créer Agent de Santé
```
POST {{base_url}}/api/create-health-agent/
Authorization: {{auth_token}}
Content-Type: application/json

{
  "firstname": "Dr. Jean",
  "lastname": "Dupont",
  "tel": "+22912345678",
  "email": "<EMAIL>",
  "role": "docteur",
  "sexe": "M",
  "address": "123 Rue Médicale, Cotonou",
  "hospital_id": 1,
  "service_id": 1,
  "speciality_id": 1
}
```

**Tests automatiques :**
```javascript
pm.test("Agent créé avec succès", function () {
    pm.response.to.have.status(200);
    const jsonData = pm.response.json();
    pm.expect(jsonData.status).to.eql("success");
    pm.expect(jsonData).to.have.property('agent_id');
    pm.expect(jsonData).to.have.property('username');
    pm.expect(jsonData).to.have.property('password');
    pm.expect(jsonData.email_sent).to.be.a('boolean');
});

pm.test("Email automatique envoyé", function () {
    const jsonData = pm.response.json();
    if (jsonData.email_sent === false) {
        console.log("Email non envoyé:", jsonData.email_error);
    }
    // Note: email_sent peut être false en développement
});
```

#### 3.2 Créer Assistant
```
POST {{base_url}}/api/create-health-agent/
Authorization: {{auth_token}}
Content-Type: application/json

{
  "firstname": "Marie",
  "lastname": "Assistant",
  "tel": "+22987654321",
  "email": "<EMAIL>",
  "role": "assistant",
  "sexe": "F",
  "address": "456 Avenue Santé, Cotonou",
  "hospital_id": 1,
  "service_id": 1,
  "speciality_id": 1
}
```

#### 3.3 Lister les Agents
```
GET {{base_url}}/api/agents/
Authorization: {{auth_token}}
```

#### 3.4 Créer Centre de Santé
```
POST {{base_url}}/api/create-health-center/
Authorization: {{auth_token}}
Content-Type: application/json

{
  "name": "Centre de Santé Test Mobile",
  "location": "Test Location, Bénin"
}
```

### **DOSSIER 4 : Gestion Patients (Agent)**

#### 4.1 Créer Patient Simple
```
POST {{base_url}}/api/create-patient/
Authorization: {{auth_token}}
Content-Type: application/json

{
  "firstname": "Patient",
  "lastname": "Test Mobile",
  "tel": "+22911111111",
  "email": "<EMAIL>",
  "sexe": "M",
  "birth_date": "1990-01-01",
  "address": "Test Address Mobile",
  "language_id": 1,
  "occupation": "Testeur",
  "is_child": false,
  "is_pregnant": false
}
```

#### 4.2 Créer Patient Complet (4 Étapes Mobile)
```
POST {{base_url}}/api/create-patient/
Authorization: {{auth_token}}
Content-Type: application/json

{
  "firstname": "Patiente",
  "lastname": "Complete Mobile",
  "tel": "+22922222222",
  "email": "<EMAIL>",
  "sexe": "F",
  "birth_date": "1995-05-15",
  "address": "123 Rue Patient Mobile, Cotonou",
  "language_id": 1,
  "occupation": "Enseignante",
  "assurance": "CNSS",
  "is_child": false,
  "is_pregnant": true,
  "pregnancy_situation": "Normale",
  "pregnancy_description": "Grossesse évoluant bien",
  "pregnancy_term": "32 semaines",
  "pregnancy_start_date": "2024-01-01",
  "study_level": "Universitaire",
  "husband_name": "Mari Test",
  "husband_tel": "+22933333333",
  "personal_history": "Aucun antécédent",
  "family_history": "Diabète familial",
  "allergies": "Aucune",
  "vaccinations": "À jour",
  "special_marks": "RAS",
  "other_informations": {
    "mobile_created": true,
    "app_version": "1.0.0"
  }
}
```

#### 4.3 Lister les Patients
```
GET {{base_url}}/api/patients/
Authorization: {{auth_token}}
```

### **DOSSIER 5 : Synchronisation Mobile**

#### 5.1 Synchronisation Données Hors Ligne
```
POST {{base_url}}/api/sync-mobile-data/
Authorization: {{auth_token}}
Content-Type: application/json

{
  "patients": [
    {
      "mobile_id": "mobile_patient_001",
      "firstname": "Patient",
      "lastname": "Offline",
      "tel": "+22944444444",
      "sexe": "M",
      "birth_date": "1992-03-10",
      "address": "Offline Address",
      "is_child": false
    },
    {
      "mobile_id": "mobile_patient_002",
      "firstname": "Patiente",
      "lastname": "Hors Ligne",
      "tel": "+22955555555",
      "sexe": "F",
      "birth_date": "1988-07-20",
      "address": "Hors Ligne Address",
      "is_child": false
    }
  ],
  "pregnancies": [
    {
      "mobile_id": "mobile_pregnancy_001",
      "mother_mobile_id": "mobile_patient_002",
      "state": "ongoing",
      "situation": "Normal",
      "start_date": "2024-02-01"
    }
  ],
  "appointments": []
}
```

**Tests automatiques :**
```javascript
pm.test("Synchronisation réussie", function () {
    pm.response.to.have.status(200);
    const jsonData = pm.response.json();
    pm.expect(jsonData.status).to.eql("success");
    pm.expect(jsonData).to.have.property('processed');
    pm.expect(jsonData.processed).to.be.an('array');
});

pm.test("Mapping mobile_id vers server_id", function () {
    const jsonData = pm.response.json();
    jsonData.processed.forEach(item => {
        pm.expect(item).to.have.property('mobile_id');
        pm.expect(item).to.have.property('server_id');
        pm.expect(item).to.have.property('created');
    });
});
```

### **DOSSIER 6 : Changement de Mot de Passe**

#### 6.1 Changement avec Mot de Passe Actuel
```
POST {{base_url}}/api/change-password/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "current_password": "TempPassword123",
  "new_password": "NouveauMotDePasse456!"
}
```

#### 6.2 Test Changement Obligatoire
```javascript
// Ce test simule la détection du changement obligatoire
pm.test("Détection changement obligatoire", function () {
    // Supposant qu'un utilisateur nouvellement créé tente d'accéder aux données
    if (pm.response.code === 403) {
        const jsonData = pm.response.json();
        if (jsonData.error === "password_change_required") {
            console.log("Changement de mot de passe requis détecté");
            pm.expect(jsonData).to.have.property('message');
        }
    }
});
```

### **DOSSIER 7 : Tests d'Erreurs**

#### 7.1 Accès Non Autorisé
```
GET {{base_url}}/api/agents/
# Sans Authorization header
```

#### 7.2 Token Invalide
```
GET {{base_url}}/api/patients/
Authorization: Bearer 99999
```

#### 7.3 Données Manquantes
```
POST {{base_url}}/api/create-patient/
Authorization: {{auth_token}}
Content-Type: application/json

{
  "firstname": "Test",
  # lastname manquant intentionnellement
  "tel": "+22900000000"
}
```

## 🔧 Scripts de Test Globaux

### Script de Pre-request (Collection)
```javascript
// Vérifier que les variables nécessaires sont définies
if (!pm.environment.get("base_url")) {
    throw new Error("Variable base_url non définie");
}

// Logger la requête
console.log(`Exécution: ${pm.info.requestName}`);
```

### Script de Test Global (Collection)
```javascript
// Vérifier que la réponse est en JSON
pm.test("Réponse en JSON", function () {
    pm.response.to.be.json;
});

// Logger le temps de réponse
pm.test("Temps de réponse acceptable", function () {
    pm.expect(pm.response.responseTime).to.be.below(5000);
});

// Logger les erreurs
if (pm.response.code >= 400) {
    console.log("Erreur détectée:", pm.response.json());
}
```

## 📊 Rapport de Tests

### Tests de Validation Mobile

**Checklist de Compatibilité :**

- [ ] **Connectivité API** : Serveur accessible depuis mobile
- [ ] **Authentification** : Login email/password fonctionne
- [ ] **Création Utilisateurs** : Agents et patients créés avec emails
- [ ] **Rôles et Permissions** : Admin vs Agent vs Patient
- [ ] **Synchronisation** : Données hors ligne synchronisées
- [ ] **Changement Mot de Passe** : Obligatoire pour nouveaux utilisateurs
- [ ] **Gestion Erreurs** : Codes de statut corrects
- [ ] **Performance** : Temps de réponse < 5 secondes

### Scripts d'Automatisation

**Exécution en Ligne de Commande :**
```bash
# Installer Newman (CLI Postman)
npm install -g newman

# Exécuter la collection
newman run PTC_Care_Mobile_Tests.postman_collection.json \
  -e PTC_Care_Local.postman_environment.json \
  --reporters cli,html \
  --reporter-html-export rapport-tests.html
```

## 🚀 Utilisation

1. **Importer** la collection et l'environnement dans Postman
2. **Démarrer** le serveur Django local
3. **Exécuter** les tests dans l'ordre :
   - Connectivité → Authentification → Gestion → Synchronisation
4. **Valider** que tous les tests passent au vert
5. **Intégrer** les endpoints validés dans votre app Flutter

Cette collection Postman vous permet de valider complètement l'API avant l'intégration mobile !
