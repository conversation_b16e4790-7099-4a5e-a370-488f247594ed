from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib import messages
from django.core.exceptions import ValidationError
from django.contrib.auth.password_validation import validate_password
from ptcapp.models.password_reset import PasswordResetToken, UserPasswordStatus
from ptcapp.services.email_service import EmailNotificationService
import json
import logging
import secrets
from datetime import datetime, timedelta
from django.utils import timezone
from django.core.cache import cache
from django.db import transaction

logger = logging.getLogger(__name__)


@login_required
def change_password_view(request):
    """
    Vue pour le changement de mot de passe obligatoire.
    """
    context = {
        'user': request.user,
        'force_change': True
    }
    
    if request.method == 'POST':
        current_password = request.POST.get('current_password')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')
        
        # Validation
        if not current_password or not new_password or not confirm_password:
            messages.error(request, 'Tous les champs sont obligatoires.')
            return render(request, 'auth/change_password.html', context)
        
        if new_password != confirm_password:
            messages.error(request, 'Les nouveaux mots de passe ne correspondent pas.')
            return render(request, 'auth/change_password.html', context)
        
        if not request.user.check_password(current_password):
            messages.error(request, 'Le mot de passe actuel est incorrect.')
            return render(request, 'auth/change_password.html', context)
        
        # Validation de la complexité du mot de passe
        try:
            validate_password(new_password, request.user)
        except ValidationError as e:
            for error in e.messages:
                messages.error(request, error)
            return render(request, 'auth/change_password.html', context)
        
        # Changer le mot de passe
        request.user.set_password(new_password)
        request.user.save()
        
        # Mettre à jour le statut
        try:
            password_status = UserPasswordStatus.objects.get(user=request.user)
            password_status.mark_password_changed()
        except UserPasswordStatus.DoesNotExist:
            UserPasswordStatus.objects.create(
                user=request.user,
                must_change_password=False,
                first_login_completed=True
            )
        
        # Marquer les tokens comme utilisés
        PasswordResetToken.objects.filter(user=request.user, is_used=False).update(is_used=True)
        
        # Envoyer email de confirmation
        EmailNotificationService.send_password_changed_confirmation(request.user)
        
        # Reconnecter l'utilisateur avec le nouveau mot de passe
        user = authenticate(email=request.user.email, password=new_password)
        if user:
            login(request, user)
        
        messages.success(request, 'Votre mot de passe a été changé avec succès.')
        
        # Rediriger vers la page d'accueil appropriée
        from ptcapp.helpers.authentification_helper import go_home
        return go_home(request.user)
    
    return render(request, 'auth/change_password.html', context)


def change_password_with_token(request, token):
    """
    Vue pour changer le mot de passe avec un token (lien email).
    """
    # Récupérer le token
    reset_token = get_object_or_404(PasswordResetToken, token=token)
    
    if not reset_token.is_valid():
        messages.error(request, 'Ce lien a expiré ou a déjà été utilisé.')
        return redirect('index')
    
    user = reset_token.user
    context = {
        'user': user,
        'token': token,
        'reset_token': reset_token,
        'token_change': True
    }
    
    if request.method == 'POST':
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')
        
        # Validation
        if not new_password or not confirm_password:
            messages.error(request, 'Tous les champs sont obligatoires.')
            return render(request, 'auth/change_password_token.html', context)
        
        if new_password != confirm_password:
            messages.error(request, 'Les mots de passe ne correspondent pas.')
            return render(request, 'auth/change_password_token.html', context)
        
        # Validation de la complexité
        try:
            validate_password(new_password, user)
        except ValidationError as e:
            for error in e.messages:
                messages.error(request, error)
            return render(request, 'auth/change_password_token.html', context)
        
        # Changer le mot de passe
        user.set_password(new_password)
        user.save()
        
        # Mettre à jour le statut
        try:
            password_status = UserPasswordStatus.objects.get(user=user)
            password_status.mark_password_changed()
        except UserPasswordStatus.DoesNotExist:
            UserPasswordStatus.objects.create(
                user=user,
                must_change_password=False,
                first_login_completed=True
            )
        
        # Marquer le token comme utilisé
        reset_token.mark_as_used()
        
        # Envoyer email de confirmation
        EmailNotificationService.send_password_changed_confirmation(user)
        
        # Connecter l'utilisateur
        user = authenticate(email=user.email, password=new_password)
        if user:
            login(request, user)
        
        messages.success(request, 'Votre mot de passe a été changé avec succès. Vous êtes maintenant connecté.')
        
        # Rediriger vers la page d'accueil appropriée
        from ptcapp.helpers.authentification_helper import go_home
        return go_home(user)
    
    return render(request, 'auth/change_password_token.html', context)


@csrf_exempt
@require_http_methods(["POST"])
def change_password_api(request):
    """
    API pour changer le mot de passe.
    """
    try:
        data = json.loads(request.body)
        
        # Authentification pour l'API
        email = data.get('email')
        current_password = data.get('current_password')
        new_password = data.get('new_password')
        token = data.get('token')  # Optionnel pour changement avec token
        
        if not email or not new_password:
            return JsonResponse({
                'error': 'Email et nouveau mot de passe requis'
            }, status=400)
        
        # Récupérer l'utilisateur
        try:
            user = User.objects.get(email__iexact=email)
        except User.DoesNotExist:
            return JsonResponse({
                'error': 'Utilisateur non trouvé'
            }, status=404)
        
        # Vérification avec token ou mot de passe actuel
        if token:
            # Changement avec token
            try:
                reset_token = PasswordResetToken.objects.get(token=token, user=user)
                if not reset_token.is_valid():
                    return JsonResponse({
                        'error': 'Token invalide ou expiré'
                    }, status=400)
            except PasswordResetToken.DoesNotExist:
                return JsonResponse({
                    'error': 'Token non trouvé'
                }, status=404)
        else:
            # Changement avec mot de passe actuel
            if not current_password:
                return JsonResponse({
                    'error': 'Mot de passe actuel requis'
                }, status=400)
            
            if not user.check_password(current_password):
                return JsonResponse({
                    'error': 'Mot de passe actuel incorrect'
                }, status=400)
        
        # Validation du nouveau mot de passe
        try:
            validate_password(new_password, user)
        except ValidationError as e:
            return JsonResponse({
                'error': 'Mot de passe non valide',
                'details': e.messages
            }, status=400)
        
        # Changer le mot de passe
        user.set_password(new_password)
        user.save()
        
        # Mettre à jour le statut
        try:
            password_status = UserPasswordStatus.objects.get(user=user)
            password_status.mark_password_changed()
        except UserPasswordStatus.DoesNotExist:
            UserPasswordStatus.objects.create(
                user=user,
                must_change_password=False,
                first_login_completed=True
            )
        
        # Marquer les tokens comme utilisés
        if token:
            reset_token.mark_as_used()
        else:
            PasswordResetToken.objects.filter(user=user, is_used=False).update(is_used=True)
        
        # Envoyer email de confirmation
        EmailNotificationService.send_password_changed_confirmation(user)
        
        logger.info(f"Mot de passe changé avec succès pour {user.email}")
        
        return JsonResponse({
            'status': 'success',
            'message': 'Mot de passe changé avec succès'
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Données JSON invalides'
        }, status=400)
    except Exception as e:
        logger.error(f"Erreur lors du changement de mot de passe API: {str(e)}")
        return JsonResponse({
            'error': 'Erreur interne du serveur'
        }, status=500)


def generate_secure_token():
    """
    Génère un token sécurisé de 64 caractères.
    """
    return secrets.token_urlsafe(48)  # 48 bytes = 64 caractères en base64url


def check_rate_limit(email):
    """
    Vérifie le rate limiting pour les demandes de réinitialisation.
    Maximum 3 demandes par email par heure.
    """
    cache_key = f"password_reset_rate_limit_{email.lower()}"
    current_count = cache.get(cache_key, 0)

    if current_count >= 3:
        return False

    # Incrémenter le compteur avec expiration d'1 heure
    cache.set(cache_key, current_count + 1, 3600)
    return True


@csrf_exempt
@require_http_methods(["POST"])
def request_password_reset_api(request):
    """
    API pour demander une réinitialisation de mot de passe.
    Génère un token sécurisé et envoie un email de réinitialisation.
    """
    try:
        data = json.loads(request.body)
        email = data.get('email', '').strip().lower()

        if not email:
            return JsonResponse({
                'error': 'Adresse email requise'
            }, status=400)

        # Validation du format email basique
        if '@' not in email or '.' not in email.split('@')[-1]:
            return JsonResponse({
                'error': 'Format d\'adresse email invalide'
            }, status=400)

        # Vérifier le rate limiting
        if not check_rate_limit(email):
            logger.warning(f"Rate limit dépassé pour l'email: {email}")
            return JsonResponse({
                'status': 'success',
                'message': 'Si cette adresse email existe, un lien de réinitialisation a été envoyé'
            })

        # Toujours retourner le même message pour éviter l'énumération d'emails
        success_message = 'Si cette adresse email existe, un lien de réinitialisation a été envoyé'

        try:
            # Rechercher l'utilisateur (insensible à la casse)
            user = User.objects.get(email__iexact=email)

            with transaction.atomic():
                # Invalider tous les tokens existants pour cet utilisateur
                PasswordResetToken.objects.filter(
                    user=user,
                    is_used=False,
                    expires_at__gt=timezone.now()
                ).update(is_used=True)

                # Générer un nouveau token sécurisé
                token = generate_secure_token()

                # Créer le token de réinitialisation
                reset_token = PasswordResetToken.objects.create(
                    user=user,
                    token=token,
                    expires_at=timezone.now() + timedelta(hours=48),
                    is_used=False
                )

                # Envoyer l'email de réinitialisation
                try:
                    email_sent = EmailNotificationService.send_password_reset_email(
                        user=user,
                        reset_token=reset_token
                    )

                    if email_sent:
                        logger.info(f"Email de réinitialisation envoyé avec succès pour {email}")
                    else:
                        logger.error(f"Échec de l'envoi de l'email de réinitialisation pour {email}")

                except Exception as email_error:
                    logger.error(f"Erreur lors de l'envoi de l'email de réinitialisation pour {email}: {str(email_error)}")

        except User.DoesNotExist:
            # L'utilisateur n'existe pas, mais on ne le révèle pas
            logger.info(f"Tentative de réinitialisation pour un email inexistant: {email}")

        # Toujours retourner le même message de succès
        return JsonResponse({
            'status': 'success',
            'message': success_message
        })

    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Données JSON invalides'
        }, status=400)
    except Exception as e:
        logger.error(f"Erreur lors de la demande de réinitialisation de mot de passe: {str(e)}")
        return JsonResponse({
            'error': 'Erreur interne du serveur'
        }, status=500)
