from django import template
from ptcapp.models.appointment import Appointment
from ptcapp.models.profile import Profile
import datetime as dt

register = template.Library()

@register.filter(name='auth_fullname')
def auth_fullname(user):
    # <PERSON><PERSON><PERSON> le cas où user est None
    if user is None:
        return "Utilisateur inconnu"

    # Vérifier si l'utilisateur est un superuser
    if not user.is_superuser:
        try:
            profile = Profile.objects.filter(user=user).get()
            return profile.lastname + " " + profile.firstname
        except Profile.DoesNotExist:
            # Si le profil n'existe pas, retourner le nom d'utilisateur
            return user.get_full_name() or user.username
    else:
        return "Super Admin"

@register.filter(name='profile_pic')
def profile_pic(user):
    # <PERSON><PERSON><PERSON> le cas où user est None
    if user is None:
        return "images/default-profile.png"

    if not user.is_superuser:
        try:
            profile = Profile.objects.filter(user=user).get()
            return profile.picture if profile.picture else "images/default-profile.png"
        except Profile.DoesNotExist:
            return "images/default-profile.png"
    else:
        return "images/admin-profile.png"
    
        
@register.filter(name='user_group')
def user_group(user):
    # <PERSON><PERSON><PERSON> le cas où user est None
    if user is None:
        return "unknown"

    if user.groups.filter(name='mother'):
        return "patient"
    if user.groups.filter(name='patient'):
        return "patient"
    if user.groups.filter(name='assistant'):
        return "assistant"
    if user.groups.filter(name='docteur'):
        return "docteur"
    if user.groups.filter(name='admin') or user.is_staff == 1:
        return "admin"

    return "unknown"


@register.filter(name='toInt')
def toInt(num):
    return int(num)


@register.filter(name='typeOf')
def typeOf(num):
    return type(num)

@register.filter(name='last_appointment')
def last_appointment(patient) :
    consul = Appointment.objects.filter(patient=patient, state="done").last()
    if(consul):
        consul_date=consul.consul_date.strftime('%d-%m-%Y')
        consul_hour=consul.consul_hour.strftime('%H:%M')
        return consul_date+" "+consul_hour
    else:
        return ""

@register.filter(name='next_appointment')
def next_appointment(patient) :
    consul = Appointment.objects.filter(patient=patient, state="pending").first()
    if(consul):
        consul_date=consul.consul_date.strftime('%d-%m-%Y')
        consul_hour=consul.consul_hour.strftime('%H:%M')
        return consul_date+" "+consul_hour
    else:
        return ""

@register.filter(name='img_user')
def img_user(user):
    # Gérer le cas où user est None
    if user is None:
        return "images/default-profile.png"

    profile = Profile.objects.filter(user=user).first()
    if profile and profile.picture:
        return profile.picture
    else:
        return "images/default-profile.png"