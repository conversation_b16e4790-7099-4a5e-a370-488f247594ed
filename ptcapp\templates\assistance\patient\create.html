{% extends 'assistance/layout.html' %} 
{% load static %}
{% load layout %}

{% block up-style %}
    <!-- DataTables -->
    <link href="{% static 'libs/select2/css/select2.min.css' %}" rel="stylesheet" type="text/css" />
    <style>
        fieldset{
            border: 1px solid !important;
            padding: 10px !important;
            height: 100% !important;
        }
        fieldset div label {
            width: 50% !important;
        }
        legend{
            padding-right: 10px !important;
            padding-left: 10px !important;
            display: inline-block;
            position: relative;
            bottom: 30px;
            background-color: white;
            width: fit-content !important;
        }

        label.required::after{
            content: "*";
            color: red;
            padding-left: 3px
        }
    </style>
{% endblock up-style %}

{% block content %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white text-left">
                    <span class="h2 text-capitalize">Création de compte pour patient</span>
                </div>
                <div class="card-body">
                    <form action="{% url 'profile.patient.create' %}" method="POST">
                        {% csrf_token %}
                        <div class="row">
                            <div class="d-flex  justify-content-end">
                                <div class="form-check mx-2">
                                    <label for="mother" class="form-check-label required">Mère</label>
                                    <input class="form-check-input" type="radio" name="group" value="mother" id="mother-radio" checked>
                                </div>
                                <div class="form-check mx-2">
                                    <label for="child" class="form-check-label required">Enfant</label>
                                    <input class="form-check-input" type="radio" name="group" value="child" id="child-radio">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md- col-sm-12 mt-4">
                                <fieldset class="h-100" style="border: none !important">
                                    <legend></legend>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="lastname" class="form-label required">Docteur du patient</label>
                                        <select class="form-control select2" {% if request.user|user_group == 'docteur' %} disabled {% endif %} name="doctor" id="doctor" required>
                                            {% for docteur in docteurs %}
                                                <option value="{{docteur.pk}}" {% if request.user.id == docteur.id %} selected {% endif %}>{{docteur.user|auth_fullname}}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </fieldset>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-12 mt-4">
                                <fieldset class="h-100">
                                    <legend>Informations Générales</legend>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="lastname" class="form-label required">Nom</label>
                                        <input class="form-control" type="text" name="lastname" placeholder="Nom" id="lastname" required="required">
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="firstname" class="form-label required">Prénom</label>
                                        <input class="form-control" type="text" name="firstname" placeholder="Prénom(s)" id="firstname" required="required">
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="sexe" class="form-label required">Sexe</label>
                                        <div class="form-check mx-2">
                                            <label for="masculin" class="form-check-label w-100">Masculin</label>
                                            <input class="form-check-input" type="radio" name="sexe" value="MAN" id="masculin" checked>
                                        </div>
                                        <div class="form-check mx-2">
                                            <label for="feminin" class="form-check-label w-100">Féminin</label>
                                            <input class="form-check-input" type="radio" name="sexe" value="WOMAN" id="feminin">
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="address" class="form-label required">Adresse</label>
                                        <input class="form-control" type="text" name="address" placeholder="Adresse" id="address" required="required">
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="tel" class="form-label required">Téléphone</label>
                                        <input class="form-control" type="tel" name="tel" placeholder="Téléphone" id="tel" required="required">
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="occupation" class="form-label">Profession</label>
                                        <input class="form-control" type="tel" name="occupation" placeholder="Profession" id="occupation" >
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="email" class="form-label">Adresse email</label>
                                        <input class="form-control" type="email" name="email" placeholder="Email" id="email" >
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="assurance" class="form-label">Assurance</label>
                                        <input class="form-control" type="text" name="assurance" placeholder="Assurance" id="assurance" >
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="language" class="form-label required">Langage</label>
                                        <select class="form-control select2" name="language" id="language" required>
                                            {% for language in languages %}
                                                <option value="{{language.pk}}" >{{language.name}}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="birth_date" class="form-label required">Date de naissance</label>
                                        <input class="form-control" type="date" name="birth_date" placeholder="Dernière Consultation" id="birth_date" required>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="col-md-6 col-sm-12 mt-4">
                                <fieldset>
                                    <legend>Informations Spécifiques</legend>
                                    
                                    <div class="d-flex align-items-center my-3" >
                                        <label for="special_marks" class="form-label d-none" id="mother_area_label">Mère</label>
                                        <select class="form-control d-none" name="mother" id="mother" disabled="disabled">
                                            {% for mother in mothers %}
                                                <option value="{{mother.pk}}" >{{mother.user|auth_fullname}}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="d-flex align-items-center my-3" id="pregnancy_state_area">
                                        <label for="pregnancy_state" class="form-label">En gestation ?</label>
                                        <input type="checkbox" name="pregnancy_state" id="pregnancy_state" switch="primary" />
                                        <label for="pregnancy_state" style="width: 56px !important;" data-on-label="Oui" data-off-label="Non"></label>
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="special_marks" class="form-label">Signes Particuliers</label>
                                        <input class="form-control" type="text" name="special_marks" placeholder="Signes Particuliers" id="special_marks">
                                    </div>
                                    <div class="d-flex align-items-start my-3">
                                        <label class="mb-1">Allergies / intolérances médicamenteuses</label>
                                        <textarea id="textarea" class="form-control" maxlength="" name="allergies" rows="3" placeholder="Allergies / intolérances médicamenteuses"></textarea>
                                    </div>
                                    <div class="d-flex align-items-start my-3">
                                        <label class="mb-1">Antécédents Personnels</label>
                                        <textarea id="textarea" class="form-control" maxlength="" name="personal_history" rows="3" placeholder="Antécédents Personnels"></textarea>
                                    </div>
                                    <div class="d-flex align-items-start my-3">
                                        <label class="mb-1">Antécédents Familiaux</label>
                                        <textarea id="textarea" class="form-control" maxlength="" name="family_history" rows="3" placeholder="Antécédents Familiaux"></textarea>
                                    </div>
                                    <div class="d-flex align-items-start my-3">
                                        <label class="mb-1">Vaccinations et autres actions de prévention et de dépistage</label>
                                        <textarea id="textarea" class="form-control" maxlength="" name="vaccinations" rows="3" placeholder="Vaccinations et autres actions de prévention et de dépistage"></textarea>
                                    </div>
                                </fieldset>
                            </div>
                        </div>
                        <div class="row mt-4">
                            <div class="col-md-6 col-sm-12">
                                <fieldset class="">
                                    <legend>Autres Informations</legend>
                                    <div class="row">
                                        <button class="btn btn-success w-25 mx-3 add">Ajouter</button>
                                        <button class="btn btn-danger w-25 mx-3 remove">Supprimer</button>
                                    </div>
                                    <table class="table">
                                        <thead class="">
                                            <tr>
                                                <th>Identification</th>
                                                <th>Correspondance</th>
                                            </tr>
                                        </thead>
                                        <tbody class="other-informations-body">
                                            <tr>
                                                <td>
                                                    <input class="form-control" type="text" name="keys[]" placeholder="" id="" >
                                                </td>
                                                <td>
                                                    <input class="form-control" type="text" name="values[]" placeholder="" id="" >
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </fieldset>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <fieldset class="">
                                    <legend>Informations Connexions</legend>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="identifiant" class="form-label">Identifiant</label>
                                        <input class="form-control" type="tel" name="text" value="PAT-XXXXXX" id="identifiant" disabled>
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="password" class="form-label">Mot de passe</label>
                                        <input class="form-control" type="passsword" name="passsword" value="*********" id="passsword" disabled>
                                    </div>
                                </fieldset>
                            </div>
                        </div>
                        <div class="col-md-12 col-sm-12 mt-4 d-none" id="pregnancy_area">
                            <fieldset class="">
                                <legend>Informations sur la grossesse</legend>
                                <div class="row">
                                    <div class="col-md-4 col-sm-12">
                                        <label for="identifiant" class="form-label">Priorité de surveillance</label>
                                        <select class="form-control select2" name="pregnancy_priority" id="pregnancy_priority">
                                            <option value="high">Haute</option>
                                            <option value="middle">Moyenne</option>
                                            <option value="low">Bas</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 col-sm-12">
                                        <label for="pregnancy_start" class="form-label">Début de la grossesse</label>
                                        <input class="form-control" type="date" name="pregnancy_start" placeholder="Dernière Consultation" id="pregnancy_start">
                                        
                                    </div>

                                    <div class="col-md-4 col-sm-12">
                                        <label for="pregnancy_start" class="form-label">Autres information sur la grossesse</label>
                                        <textarea name="pregnancy_other_information" id="pregnancy_other_information" style="width:100% !important" rows="4"></textarea>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                        <div class="row mt-2">
                            <div class="col">
                                <input class="btn btn-success w-auto" type="submit" value="Sauvegarder">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}

{% block down-script %}
    <script src="{% static 'libs/select2/js/select2.min.js'%}"></script>
    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        $('.select2').select2()
        $('.add').on('click', function(e){
            e.preventDefault()
            const row=`<tr>
                <td>
                    <input class="form-control" type="text" name="keys[]" placeholder="" id="" >
                </td>
                <td>
                    <input class="form-control" type="text" name="values[]" placeholder="" id="" >
                </td>
            </tr>`
            $('.other-informations-body').append(row)
        })
        $('.remove').on('click', function(e){
            e.preventDefault()
            $('.other-informations-body').children().last().remove();
        })
        $('#child-radio').on('change', function (e){
            if($(this)[0].checked){
                $('#mother_area_label').addClass('required')
                $('#mother_area_label').removeClass('d-none')
                $('#mother').removeClass('d-none')
                $('#mother').select2()
                $('#pregnancy_state_area').addClass('d-none')
                $('#mother').attr('disabled', false)
            }
        })
        $('#mother-radio').on('change', function (e){
            if($(this)[0].checked){
                $('#mother_area_label').removeClass('required')
                $('#mother_area_label').addClass('d-none')
                $('#mother').select2("destroy")
                $('#mother').addClass('d-none')
                $('#mother').attr('disabled', 'disabled')
                $('#pregnancy_state_area').removeClass('d-none')
            }
        })
        $('#pregnancy_state').on('change', function (e){
            if($(this)[0].checked){
                $('#pregnancy_area').removeClass('d-none')
            }else{
                $('#pregnancy_area').addClass('d-none')
            }
        })
    </script>
{% endblock down-script %}