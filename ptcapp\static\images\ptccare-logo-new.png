<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle with gradient -->
  <defs>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4285f4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1a73e8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ea4335;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d33b2c;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Main heart/stethoscope shape -->
  <path d="M 200 80 
           C 160 40, 100 40, 100 100
           C 100 140, 140 180, 200 240
           C 260 180, 300 140, 300 100
           C 300 40, 240 40, 200 80 Z" 
        fill="none" 
        stroke="url(#blueGradient)" 
        stroke-width="12" 
        stroke-linecap="round"/>
  
  <!-- Stethoscope circles -->
  <circle cx="320" cy="120" r="18" fill="url(#blueGradient)"/>
  <circle cx="320" cy="220" r="22" fill="url(#blueGradient)"/>
  
  <!-- Stethoscope tube -->
  <path d="M 300 100 Q 320 110, 320 120" 
        fill="none" 
        stroke="url(#blueGradient)" 
        stroke-width="8" 
        stroke-linecap="round"/>
  
  <path d="M 320 138 Q 320 180, 320 198" 
        fill="none" 
        stroke="url(#blueGradient)" 
        stroke-width="8" 
        stroke-linecap="round"/>
  
  <!-- Medical cross inside heart -->
  <g transform="translate(200, 140)">
    <!-- Vertical bar of cross -->
    <rect x="-8" y="-25" width="16" height="50" fill="url(#redGradient)" rx="2"/>
    <!-- Horizontal bar of cross -->
    <rect x="-25" y="-8" width="50" height="16" fill="url(#redGradient)" rx="2"/>
  </g>
  
  <!-- PTC CARE text -->
  <text x="200" y="270" 
        font-family="Arial, sans-serif" 
        font-size="36" 
        font-weight="bold" 
        text-anchor="middle" 
        fill="url(#redGradient)">PTCCARE</text>
  
  <!-- BENIN text -->
  <text x="200" y="290" 
        font-family="Arial, sans-serif" 
        font-size="14" 
        font-weight="normal" 
        text-anchor="middle" 
        fill="#333333" 
        letter-spacing="2px">BENIN</text>
</svg>
