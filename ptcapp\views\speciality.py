from django.shortcuts import redirect, render,get_object_or_404,HttpResponseRedirect
from django.http import JsonResponse

# relative import of forms
from ptcapp.models import Speciality
from ptcapp.forms.speciality import SpecialityForm


def create_view(request):
    if request.method == 'POST':
        name = request.POST['name']
        description = request.POST['description']
        speciality = Speciality.objects.create(name=name,description = description)
        speciality.save()
        return JsonResponse({'speciality':int(speciality.id), 'success' : True})
    return JsonResponse({'speciality':int(speciality.id), 'success' : False})


def update_view(request, id):
    obj = get_object_or_404(Speciality, id = id)
    
    if request.method == 'POST':
        obj.name = request.POST['name']
        obj.description = request.POST['description']
        obj.save()
        return JsonResponse({'speciality':int(obj.id), 'success' : True})
    return JsonResponse({'speciality':int(obj.id), 'success' : False})
    

def delete_view(request, id):
    obj = get_object_or_404(Speciality, id = id)
    if request.method =="POST":
        obj.delete()
        return JsonResponse({'success' : True})
    return JsonResponse({'speciality':int(obj.id), 'success' : False})
