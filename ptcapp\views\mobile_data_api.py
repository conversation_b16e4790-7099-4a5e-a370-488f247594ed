"""
API de données pour l'application mobile PTCCare
Endpoints pour récupérer les données nécessaires au fonctionnement hors ligne
"""

import json
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.models import User, Group
from django.db.models import Q
from django.utils import timezone

from ..models import (
    Profile, Hospital, Service, Speciality, Language,
    Pregnancy, Appointment, Record, MotherPregnancy,
    MapDoctorPatient, MapMotherChild, MapPregnancyChild
)
from ..middleware.mobile_auth_middleware import mobile_auth_required, admin_required, doctor_or_admin_required
from ..decorators.cors_decorators import mobile_api_cors

@csrf_exempt
@mobile_api_cors
def mobile_initial_data(request):
    """
    Récupérer les données initiales pour l'application mobile
    GET /api/mobile/initial-data
    """
    if request.method != 'GET':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        # Données de référence (publiques)
        hospitals = list(Hospital.objects.values('id', 'name', 'address', 'tel'))
        services = list(Service.objects.select_related('hospital').values(
            'id', 'name', 'description', 'hospital_id', 'hospital__name'
        ))
        specialities = list(Speciality.objects.values('id', 'name', 'description'))
        languages = list(Language.objects.values('id', 'name', 'path'))

        return JsonResponse({
            'status': 'success',
            'data': {
                'hospitals': hospitals,
                'services': services,
                'specialities': specialities,
                'languages': languages,
                'app_version': '1.0.0',
                'api_version': '1.0.0',
                'server_time': timezone.now().isoformat()
            }
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Erreur serveur: {str(e)}',
            'code': 'SERVER_ERROR'
        }, status=500)

@csrf_exempt
@mobile_api_cors
@mobile_auth_required
def mobile_user_data(request):
    """
    Récupérer les données spécifiques à l'utilisateur connecté
    GET /api/mobile/user-data
    """
    if request.method != 'GET':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        user = request.mobile_user
        profile = request.mobile_profile
        user_role = user.groups.first().name if user.groups.exists() else None

        # Données de base de l'utilisateur
        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'profile': {
                'id': profile.id,
                'firstname': profile.firstname,
                'lastname': profile.lastname,
                'tel': profile.tel,
                'sexe': profile.sexe,
                'birth_date': profile.birth_date.isoformat() if profile.birth_date else None,
                'address': profile.address,
                'occupation': profile.occupation,
                'hospital_id': profile.hospital.id if profile.hospital else None,
                'service_id': profile.service.id if profile.service else None,
                'speciality_id': profile.speciality.id if profile.speciality else None,
                'language_id': profile.language.id if profile.language else None,
            },
            'role': user_role,
            'permissions': get_user_permissions(user_role)
        }

        # Données spécifiques selon le rôle
        role_data = {}
        
        if user_role == 'admin':
            # Administrateur : accès à tous les agents et établissements
            role_data = get_admin_data()

        elif user_role == 'docteur':
            # Médecin : ses patients et rendez-vous
            role_data = get_doctor_data(profile)

        elif user_role == 'assistant':
            # Assistant : patients de son service/hôpital
            role_data = get_assistant_data(profile)

        return JsonResponse({
            'status': 'success',
            'data': {
                'user': user_data,
                'role_data': role_data
            }
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Erreur serveur: {str(e)}',
            'code': 'SERVER_ERROR'
        }, status=500)

def get_user_permissions(role):
        """Récupérer les permissions selon le rôle"""
        permissions = {
            'admin': [
                'create_agent', 'edit_agent', 'delete_agent',
                'create_hospital', 'edit_hospital',
                'view_all_patients', 'view_all_appointments',
                'manage_system'
            ],
            'docteur': [
                'create_patient', 'edit_patient',
                'create_appointment', 'edit_appointment',
                'create_pregnancy', 'edit_pregnancy',
                'view_own_patients', 'manage_consultations'
            ],
            'assistant': [
                'create_patient', 'edit_patient',
                'create_appointment', 'edit_appointment',
                'view_assigned_patients', 'assist_consultations'
            ]
        }
        return permissions.get(role, [])

def get_admin_data():
        """Données pour administrateur"""
        # Tous les agents de santé
        doctors = Profile.objects.filter(user__groups__name='docteur').select_related('user', 'hospital', 'service')
        assistants = Profile.objects.filter(user__groups__name='assistant').select_related('user', 'hospital', 'service')
        
        agents = []
        for doctor in doctors:
            agents.append({
                'id': doctor.id,
                'user_id': doctor.user.id,
                'username': doctor.user.username,
                'name': f"{doctor.firstname} {doctor.lastname}",
                'role': 'docteur',
                'hospital_id': doctor.hospital.id if doctor.hospital else None,
                'service_id': doctor.service.id if doctor.service else None,
                'tel': doctor.tel,
                'email': doctor.user.email
            })
            
        for assistant in assistants:
            agents.append({
                'id': assistant.id,
                'user_id': assistant.user.id,
                'username': assistant.user.username,
                'name': f"{assistant.firstname} {assistant.lastname}",
                'role': 'assistant',
                'hospital_id': assistant.hospital.id if assistant.hospital else None,
                'service_id': assistant.service.id if assistant.service else None,
                'tel': assistant.tel,
                'email': assistant.user.email
            })

        return {
            'agents': agents,
            'total_agents': len(agents),
            'total_doctors': doctors.count(),
            'total_assistants': assistants.count()
        }

def get_doctor_data(profile):
        """Données pour médecin"""
        # Patients du médecin
        doctor_patients = MapDoctorPatient.objects.filter(doctor=profile).select_related('patient__user')
        
        patients = []
        for dp in doctor_patients:
            patient = dp.patient
            # Récupérer la grossesse active
            active_pregnancy = MotherPregnancy.objects.filter(
                mother=patient,
                pregnancy__state='En cours'
            ).select_related('pregnancy').first()
            
            patients.append({
                'id': patient.id,
                'user_id': patient.user.id,
                'username': patient.user.username,
                'name': f"{patient.firstname} {patient.lastname}",
                'tel': patient.tel,
                'birth_date': patient.birth_date.isoformat() if patient.birth_date else None,
                'address': patient.address,
                'assurance': patient.assurance,
                'husband_name': patient.husband_name,
                'husband_tel': patient.husband_tel,
                'active_pregnancy': {
                    'id': active_pregnancy.pregnancy.id,
                    'term': active_pregnancy.pregnancy.term,
                    'state': active_pregnancy.pregnancy.state,
                    'start_date': active_pregnancy.pregnancy.start_date.isoformat() if active_pregnancy.pregnancy.start_date else None
                } if active_pregnancy else None
            })

        # Rendez-vous du médecin
        appointments = Appointment.objects.filter(doctor=profile).select_related('patient')
        appointments_data = []
        for appointment in appointments:
            appointments_data.append({
                'id': appointment.id,
                'patient_id': appointment.patient.id,
                'patient_name': f"{appointment.patient.firstname} {appointment.patient.lastname}",
                'consul_date': appointment.consul_date.isoformat(),
                'consul_hour': appointment.consul_hour,
                'appointment_type': appointment.appointment_type,
                'state': appointment.state,
                'consul_data': appointment.consul_data
            })

        return {
            'patients': patients,
            'appointments': appointments_data,
            'total_patients': len(patients),
            'total_appointments': len(appointments_data)
        }

def get_assistant_data(profile):
        """Données pour assistant"""
        # Patients du même hôpital/service
        patients = Profile.objects.filter(
            user__groups__name='patient',
            hospital=profile.hospital
        ).select_related('user')
        
        patients_data = []
        for patient in patients:
            patients_data.append({
                'id': patient.id,
                'user_id': patient.user.id,
                'username': patient.user.username,
                'name': f"{patient.firstname} {patient.lastname}",
                'tel': patient.tel,
                'birth_date': patient.birth_date.isoformat() if patient.birth_date else None,
                'address': patient.address,
                'assurance': patient.assurance
            })

        return {
            'patients': patients_data,
            'total_patients': len(patients_data)
        }
