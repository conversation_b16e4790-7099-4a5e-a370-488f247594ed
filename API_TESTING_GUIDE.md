# Guide de Test API - PTC Care

## 🚀 Configuration Initiale

### 1. <PERSON><PERSON><PERSON>quis
- **Postman** installé (ou Insomnia/Swagger UI)
- **Serveur PTC Care** en cours d'exécution sur `http://localhost:8000`
- **Environnement virtuel** activé
- **Configuration email** opérationnelle

### 2. Démarrage du Serveur
```bash
# Activer l'environnement virtuel
.\env\Scripts\activate

# Démarrer le serveur Django
python manage.py runserver

# Vérifier que le serveur répond
curl http://localhost:8000/api/initial-data/
```

### 3. Import dans Postman

#### Importer la Collection
1. Ouvrir Postman
2. Cliquer sur **Import**
3. Sélectionner `PTC_Care_API_Collection.postman_collection.json`
4. Confirmer l'import

#### Importer l'Environnement
1. Cliquer sur **Import**
2. Sélectionner `PTC_Care_Environment.postman_environment.json`
3. Sélectionner l'environnement "PTC Care - Development"

## 🧪 Plan de Test Séquentiel

### Phase 1: Tests de Base (Sans Authentification)

#### 1.1 Test des Données Initiales
```
GET /api/initial-data/
```
**Objectif** : Vérifier que l'API répond et retourne les données de référence

**Résultat attendu** :
- Status: 200 OK
- Contient: hospitals, services, specialities, languages

#### 1.2 Test des Centres de Santé
```
GET /api/health-centers/
```
**Objectif** : Récupérer la liste des centres de santé

**Résultat attendu** :
- Status: 200 OK
- Contient: health_centers array

### Phase 2: Tests d'Authentification

#### 2.1 Connexion avec Email
```
POST /api/login/
Body: {
  "email": "<EMAIL>",
  "password": "admin123"
}
```
**Objectif** : Tester la connexion avec email

**Résultat attendu** :
- Status: 200 OK
- Contient: user_id, username, role
- Variables automatiquement sauvegardées

#### 2.2 Connexion avec Username
```
POST /api/login/
Body: {
  "email": "ADM-12345678",
  "password": "admin123"
}
```
**Objectif** : Tester la rétrocompatibilité avec username

### Phase 3: Tests de Création d'Utilisateurs

#### 3.1 Création d'un Médecin
```
POST /api/create-health-agent/
Authorization: Bearer {{user_id}}
Body: {
  "firstname": "Dr. Jean",
  "lastname": "Dupont",
  "email": "<EMAIL>",
  "role": "docteur",
  ...
}
```
**Objectif** : Créer un médecin avec notification email

**Résultat attendu** :
- Status: 200 OK
- email_sent: true
- Username format: DOC-XXXXXXXX
- Email reçu dans la boîte configurée

#### 3.2 Création d'un Patient
```
POST /api/create-patient/
Authorization: Bearer {{user_id}}
Body: {
  "firstname": "Marie",
  "lastname": "Kouassi",
  "email": "<EMAIL>",
  ...
}
```
**Objectif** : Créer un patient avec notification email

**Résultat attendu** :
- Status: 200 OK
- email_sent: true
- Username format: PAT-XXXXXXXX

### Phase 4: Tests de Récupération de Données

#### 4.1 Liste des Agents
```
GET /api/agents/
Authorization: Bearer {{user_id}}
```
**Objectif** : Récupérer la liste des agents selon le rôle

#### 4.2 Liste des Patients
```
GET /api/patients/
Authorization: Bearer {{user_id}}
```
**Objectif** : Récupérer la liste des patients selon le rôle

### Phase 5: Tests de Sécurité

#### 5.1 Test Accès Non Autorisé
```
POST /api/create-health-agent/
(Sans Authorization header)
```
**Résultat attendu** : Status 401

#### 5.2 Test Changement de Mot de Passe Obligatoire
```
GET /api/patients/
Authorization: Bearer {new_user_id}
```
**Résultat attendu** : Status 403 avec error: "password_change_required"

### Phase 6: Tests de Changement de Mot de Passe

#### 6.1 Changement avec Mot de Passe Actuel
```
POST /api/change-password/
Body: {
  "email": "<EMAIL>",
  "current_password": "TempPassword123",
  "new_password": "NewSecurePassword123!"
}
```

#### 6.2 Changement avec Token
```
POST /api/change-password/
Body: {
  "email": "<EMAIL>",
  "token": "secure-token-from-email",
  "new_password": "NewSecurePassword123!"
}
```

### Phase 7: Tests Mobile

#### 7.1 Synchronisation de Données
```
POST /api/sync-mobile-data/
Authorization: Bearer {{user_id}}
Body: {
  "patients": [...],
  "pregnancies": [...],
  "appointments": [...]
}
```

## 📊 Checklist de Validation

### ✅ Tests de Base
- [ ] Données initiales récupérées
- [ ] Centres de santé listés
- [ ] API répond correctement

### ✅ Tests d'Authentification
- [ ] Connexion par email fonctionne
- [ ] Connexion par username fonctionne
- [ ] Variables d'authentification sauvegardées

### ✅ Tests de Création avec Email
- [ ] Médecin créé avec email envoyé
- [ ] Assistant créé avec email envoyé
- [ ] Patient créé avec email envoyé
- [ ] Patiente enceinte créée avec email envoyé
- [ ] Emails reçus dans la boîte configurée

### ✅ Tests de Sécurité
- [ ] Accès non autorisé bloqué (401)
- [ ] Changement mot de passe forcé (403)
- [ ] Validation des rôles fonctionnelle

### ✅ Tests de Données
- [ ] Liste agents récupérée
- [ ] Liste patients récupérée
- [ ] Données filtrées selon le rôle

### ✅ Tests Mobile
- [ ] Synchronisation hors ligne fonctionne
- [ ] Mapping mobile_id → server_id correct
- [ ] Gestion des erreurs appropriée

## 🔧 Dépannage

### Problèmes Courants

#### Serveur non accessible
```bash
# Vérifier que le serveur tourne
python manage.py runserver
# Tester avec curl
curl http://localhost:8000/api/initial-data/
```

#### Erreur d'authentification
- Vérifier que user_id est défini dans les variables
- Tester d'abord la connexion
- Vérifier le format du token: "Bearer {user_id}"

#### Emails non envoyés
- Vérifier la configuration SMTP dans settings.py
- Tester l'envoi simple avec le script de test
- Vérifier les logs Django pour les erreurs

#### Erreur 403 inattendue
- Vérifier que l'utilisateur n'a pas besoin de changer son mot de passe
- Tester avec un utilisateur admin confirmé

## 📧 Validation des Emails

### Vérifications à Effectuer
1. **Email de bienvenue reçu** dans `<EMAIL>`
2. **Contenu correct** : identifiants + lien de changement
3. **Lien fonctionnel** : redirection vers page de changement
4. **Email de confirmation** après changement de mot de passe

### Format des Emails
- **Sujet** : "Bienvenue sur PTC Care - Vos identifiants de connexion"
- **Contenu** : HTML + version texte
- **Lien** : `http://localhost:8000/change-password/{token}/`

## 📱 Tests pour Intégration Mobile

### Headers Requis
```
Content-Type: application/json
Authorization: Bearer {user_id}
User-Agent: PTC-Care-Mobile/1.0
Accept: application/json
```

### Gestion des Erreurs
- **401** : Token invalide → Rediriger vers login
- **403** : Changement mot de passe requis → Rediriger vers changement
- **400** : Données invalides → Afficher erreur utilisateur

### Synchronisation Hors Ligne
- Stocker données localement avec mobile_id
- Synchroniser périodiquement avec /api/sync-mobile-data/
- Mapper les IDs retournés pour les références futures

---

**Durée estimée des tests** : 30-45 minutes  
**Prérequis** : Serveur en cours d'exécution + configuration email  
**Résultat attendu** : Tous les endpoints fonctionnels avec notifications email
