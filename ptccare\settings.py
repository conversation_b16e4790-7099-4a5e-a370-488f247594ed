"""
Django settings for ptccare project.

Generated by 'django-admin startproject' using Django 4.0.3.

For more information on this file, see
https://docs.djangoproject.com/en/4.0/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/4.0/ref/settings/
"""

from pathlib import Path
import os

# Configuration temporaire des variables d'environnement pour les tests
# En production, utilisez de vraies variables d'environnement
if not os.environ.get('EMAIL_HOST_USER'):
    os.environ['EMAIL_HOST_USER'] = '<EMAIL>'
    os.environ['EMAIL_HOST_PASSWORD'] = 'otluwykxqiyytzki'
    os.environ['PTCCARE_BASE_URL'] = 'http://localhost:8000'

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/4.0/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-kn#zc^ieh#g*b!fm@k!#qjb#a8@&!b58#8%f+2ky&ht++q9&&='

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = [
    'testserver',
    'localhost',
    '127.0.0.1',
    '.herokuapp.com',
    'ptccare-web.herokuapp.com',
    'ptccare-web-ae382d4ad8cc.herokuapp.com',
    '*'  # Temporaire pour résoudre le problème
]


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'corsheaders',  # Support CORS pour API mobile
    'ptcapp'
]

MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware',  # CORS doit être en premier
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'ptcapp.middleware.password_change_middleware.ForcePasswordChangeMiddleware',
    'ptcapp.middleware.mobile_auth_middleware.MobileAuthMiddleware',  # Authentification mobile
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

# Backend d'authentification personnalisé pour permettre la connexion par email
AUTHENTICATION_BACKENDS = [
    'ptcapp.backends.EmailBackend',
    'django.contrib.auth.backends.ModelBackend',  # Fallback pour l'admin Django
]

ROOT_URLCONF = 'ptccare.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'ptccare.wsgi.application'


# Database
# https://docs.djangoproject.com/en/4.0/ref/settings/#databases

# Configuration MySQL avec WAMP
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'ptccare',
        'USER': 'root',
        'PASSWORD': '',  # Mot de passe vide par défaut avec WAMP
        'HOST': '127.0.0.1',
        'PORT': '3306',
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    }
}


# Password validation
# https://docs.djangoproject.com/en/4.0/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/4.0/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Africa/Porto-Novo'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/4.0/howto/static-files/

STATIC_URL = '/static/'

# Default primary key field type
# https://docs.djangoproject.com/en/4.0/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Configuration CORS pour l'API mobile
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # React/Web
    "http://127.0.0.1:3000",
    "http://localhost:8080",  # Flutter web debug
    "http://127.0.0.1:8080",
]

# Permettre toutes les origines en développement (à restreindre en production)
CORS_ALLOW_ALL_ORIGINS = True

# Headers autorisés pour les requêtes CORS
CORS_ALLOW_HEADERS = [
    'accept',
    'accept-encoding',
    'authorization',
    'content-type',
    'dnt',
    'origin',
    'user-agent',
    'x-csrftoken',
    'x-requested-with',
]

# Méthodes HTTP autorisées
CORS_ALLOW_METHODS = [
    'DELETE',
    'GET',
    'OPTIONS',
    'PATCH',
    'POST',
    'PUT',
]

# Permettre les credentials (cookies, authorization headers)
CORS_ALLOW_CREDENTIALS = True

# Préflight cache (en secondes)
CORS_PREFLIGHT_MAX_AGE = 86400

CELERY_BROKER_URL = 'amqp://localhost'
CELERY_TIMEZONE = 'Africa/Porto-Novo'

# CELERY ROUTES
CELERY_ROUTES = {
    'core.tasks.call-one': {'queue': 'call-one'},
    'core.tasks.quick_task': {'queue': 'quick_queue'},
}

# Configuration Email
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'  # Ou votre serveur SMTP
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '<EMAIL>')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = 'PTC Care <<EMAIL>>'
SERVER_EMAIL = DEFAULT_FROM_EMAIL

# Configuration pour les notifications utilisateurs
PTCCARE_BASE_URL = os.environ.get('PTCCARE_BASE_URL', 'http://localhost:8000')
PASSWORD_RESET_TOKEN_EXPIRY_HOURS = 48

FIXTURE_DIRS = [
    
]

MEDIA_URL = '/ptcapp/uploads/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'ptcapp/uploads/')