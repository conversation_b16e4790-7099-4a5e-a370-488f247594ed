"""
URLs pour l'API mobile PTCCare Flutter
Configuration des endpoints d'authentification et de données pour mobile
"""

from django.urls import path
from ..views import mobile_auth_api, mobile_data_api
# from ..views import mobile_sync_api  # Temporairement désactivé

# URLs d'authentification mobile
auth_urlpatterns = [
    # Authentification
    path('auth/login', mobile_auth_api.mobile_login, name='mobile_login'),
    path('auth/refresh', mobile_auth_api.mobile_refresh_token, name='mobile_refresh_token'),
    path('auth/logout', mobile_auth_api.mobile_logout, name='mobile_logout'),
    path('auth/verify', mobile_auth_api.mobile_verify_token, name='mobile_verify_token'),
]

# URLs de données mobile
data_urlpatterns = [
    # Données initiales (publiques)
    path('initial-data', mobile_data_api.mobile_initial_data, name='mobile_initial_data'),
    
    # Données utilisateur (authentifiées)
    path('user-data', mobile_data_api.mobile_user_data, name='mobile_user_data'),
]

# URLs de synchronisation mobile (temporairement désactivées)
sync_urlpatterns = [
    # # Statut de synchronisation
    # path('sync/status', mobile_sync_api.sync_status, name='mobile_sync_status'),

    # # Synchronisation des patients
    # path('sync/patients', mobile_sync_api.sync_patients, name='mobile_sync_patients'),

    # # Synchronisation des rendez-vous
    # path('sync/appointments', mobile_sync_api.sync_appointments, name='mobile_sync_appointments'),

    # # Synchronisation des grossesses
    # path('sync/pregnancies', mobile_sync_api.sync_pregnancies, name='mobile_sync_pregnancies'),
]

# Toutes les URLs mobile
urlpatterns = auth_urlpatterns + data_urlpatterns  # + sync_urlpatterns
