"""ptccare URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf.urls.static import static
from django.conf import settings

urlpatterns = [
    path('super-admin/', admin.site.urls),
    path('appointment/', include('ptcapp.urls.appointment')),
    path('alert/', include('ptcapp.urls.alert')),
    path('records/', include('ptcapp.urls.record')),
    path('hospital/', include('ptcapp.urls.hospital')),
    path('pregnancy/', include('ptcapp.urls.pregnancy')),
    path('admin/personnel/profile/', include('ptcapp.urls.profile')),
    path('service/', include('ptcapp.urls.service')),
    path('speciality/', include('ptcapp.urls.speciality')),
    path('', include('ptcapp.urls.urls')),
]

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)