# Documentation API - PTC Care

## 📋 Informations Générales

- **Base URL** : `http://localhost:8000` (développement) / `https://your-domain.com` (production)
- **Content-Type** : `application/json`
- **Authentification** : <PERSON><PERSON> dans l'en-tête `Authorization`
- **Format des réponses** : JSON
- **Gestion CSRF** : Désactivée pour les endpoints API (`@csrf_exempt`)

## 🔐 Authentification

### 1. Connexion (Login)

**Endpoint** : `POST /api/login/`

**Description** : Authentifie un utilisateur et retourne ses informations de profil.

**Paramètres requis** :
```json
{
  "email": "string",        // Email ou username de l'utilisateur
  "password": "string"      // Mot de passe
}
```

**Réponse de succès** (200) :
```json
{
  "status": "success",
  "user_id": 123,
  "username": "DOC-12345678",
  "profile_id": 456,
  "role": "agent",          // "admin", "agent", ou "patient"
  "name": "<PERSON>. <PERSON>",
  "hospital_id": 1,
  "service_id": 2,
  "speciality_id": 3
}
```

**Réponses d'erreur** :
- `400` : Données manquantes ou JSON invalide
- `401` : Identifiants incorrects
- `404` : Profil introuvable

**Exemple cURL** :
```bash
curl -X POST http://localhost:8000/api/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "password123"}'
```

### 2. Changement de Mot de Passe

**Endpoint** : `POST /api/change-password/`

**Description** : Change le mot de passe d'un utilisateur (avec mot de passe actuel ou token).

**Paramètres requis** :
```json
{
  "email": "string",              // Email de l'utilisateur
  "new_password": "string",       // Nouveau mot de passe
  "current_password": "string",   // Mot de passe actuel (optionnel si token fourni)
  "token": "string"              // Token de changement (optionnel)
}
```

**Réponse de succès** (200) :
```json
{
  "status": "success",
  "message": "Mot de passe changé avec succès"
}
```

**Réponses d'erreur** :
- `400` : Données manquantes, mot de passe invalide
- `404` : Utilisateur ou token non trouvé

## 👥 Gestion des Utilisateurs

### 3. Création d'Agent de Santé

**Endpoint** : `POST /api/create-health-agent/`

**Description** : Crée un nouvel agent de santé (médecin ou assistant) avec envoi automatique d'email.

**Authentification** : Requise (Admin uniquement)

**En-têtes requis** :
```
Authorization: Bearer {user_id}
Content-Type: application/json
```

**Paramètres requis** :
```json
{
  "firstname": "string",
  "lastname": "string",
  "tel": "string",
  "email": "string",
  "role": "docteur|assistant",
  "sexe": "M|F",
  "address": "string",
  "hospital_id": 1,
  "service_id": 2,
  "speciality_id": 3
}
```

**Réponse de succès** (200) :
```json
{
  "status": "success",
  "message": "Agent créé avec succès",
  "agent_id": 789,
  "username": "DOC-12345678",
  "password": "TempPassword123",
  "email_sent": true,
  "email_error": null
}
```

**Réponses d'erreur** :
- `401` : Non autorisé (token manquant/invalide)
- `403` : Accès refusé (non admin)
- `400` : Données manquantes ou rôle invalide

### 4. Création de Patient

**Endpoint** : `POST /api/create-patient/`

**Description** : Crée un nouveau patient avec envoi automatique d'email.

**Authentification** : Requise (Médecin, Assistant ou Admin)

**Paramètres requis** :
```json
{
  "firstname": "string",
  "lastname": "string",
  "tel": "string",
  "email": "string",
  "sexe": "M|F",
  "birth_date": "YYYY-MM-DD",
  "address": "string",
  "language_id": 1,
  "occupation": "string",
  "assurance": "string",
  "special_marks": "string",
  "personal_history": "string",
  "family_history": "string",
  "allergies": "string",
  "vaccinations": "string",
  "is_child": false,
  "study_level": "string",
  "husband_name": "string",
  "husband_tel": "string",
  "is_pregnant": false,
  "pregnancy_situation": "string",
  "pregnancy_description": "string",
  "pregnancy_term": "string",
  "pregnancy_start_date": "YYYY-MM-DD",
  "mother_state": "string",
  "mother_id": 123,
  "other_informations": {}
}
```

**Réponse de succès** (200) :
```json
{
  "status": "success",
  "message": "Patient créé avec succès",
  "patient_id": 456,
  "username": "PAT-87654321",
  "password": "TempPassword456",
  "email_sent": true,
  "email_error": null
}
```

## 📊 Récupération de Données

### 5. Données Initiales

**Endpoint** : `GET /api/initial-data/`

**Description** : Récupère les données de référence pour l'initialisation de l'application.

**Authentification** : Non requise

**Réponse de succès** (200) :
```json
{
  "status": "success",
  "hospitals": [
    {"id": 1, "name": "Hôpital Central", "location": "Cotonou"}
  ],
  "services": [
    {"id": 1, "name": "Cardiologie", "description": "Service de cardiologie"}
  ],
  "specialities": [
    {"id": 1, "name": "Cardiologue", "description": "Spécialiste du cœur"}
  ],
  "languages": [
    {"id": 1, "name": "Français", "path": "fr"}
  ]
}
```

### 6. Liste des Centres de Santé

**Endpoint** : `GET /api/health-centers/`

**Description** : Récupère la liste de tous les centres de santé.

**Réponse de succès** (200) :
```json
{
  "status": "success",
  "health_centers": [
    {
      "id": 1,
      "name": "Hôpital Central de Cotonou",
      "location": "Cotonou, Bénin"
    }
  ]
}
```

### 7. Création de Centre de Santé

**Endpoint** : `POST /api/create-health-center/`

**Description** : Crée un nouveau centre de santé.

**Authentification** : Requise (Admin uniquement)

**Paramètres requis** :
```json
{
  "name": "string",
  "location": "string"
}
```

**Réponse de succès** (200) :
```json
{
  "status": "success",
  "message": "Centre de santé créé avec succès",
  "health_center_id": 123
}
```

### 8. Liste des Agents

**Endpoint** : `GET /api/agents/`

**Description** : Récupère la liste des agents de santé selon le rôle de l'utilisateur connecté.

**Authentification** : Requise

**Réponse de succès** (200) :
```json
{
  "status": "success",
  "agents": [
    {
      "id": 123,
      "firstname": "Dr. Jean",
      "lastname": "Dupont",
      "tel": "+22912345678",
      "sexe": "M",
      "role": "docteur",
      "hospital_name": "Hôpital Central",
      "service_name": "Cardiologie",
      "speciality_name": "Cardiologue",
      "created_at": "2024-01-15 10:30:00"
    }
  ]
}
```

### 9. Liste des Patients

**Endpoint** : `GET /api/patients/`

**Description** : Récupère la liste des patients selon le rôle de l'utilisateur connecté.

**Authentification** : Requise

**Réponse de succès** (200) :
```json
{
  "status": "success",
  "patients": [
    {
      "id": 456,
      "firstname": "Marie",
      "lastname": "Kouassi",
      "tel": "+22987654321",
      "sexe": "F",
      "birth_date": "1990-05-15",
      "address": "123 Rue de la Paix, Cotonou",
      "is_child": false,
      "created_at": "2024-01-20 14:45:00"
    }
  ]
}
```

## 🔄 Synchronisation Mobile

### 10. Synchronisation des Données

**Endpoint** : `POST /api/sync-mobile-data/`

**Description** : Synchronise les données collectées hors ligne depuis l'application mobile.

**Authentification** : Requise (Médecin, Assistant ou Admin)

**Paramètres requis** :
```json
{
  "patients": [
    {
      "mobile_id": "local_123",
      "firstname": "string",
      "lastname": "string",
      "tel": "string",
      "sexe": "M|F",
      "birth_date": "YYYY-MM-DD",
      "address": "string",
      "is_child": false
    }
  ],
  "pregnancies": [
    {
      "mobile_id": "preg_456",
      "mother_mobile_id": "local_123",
      "state": "ongoing",
      "situation": "Normal",
      "start_date": "YYYY-MM-DD"
    }
  ],
  "appointments": [
    {
      "mobile_id": "app_789",
      "patient_id": 123,
      "doctor_id": 456,
      "consul_date": "YYYY-MM-DD",
      "consul_hour": "HH:MM",
      "appointment_type": "string",
      "state": "EN ATTENTE"
    }
  ]
}
```

**Réponse de succès** (200) :
```json
{
  "status": "success",
  "message": "Données synchronisées avec succès",
  "processed": {
    "patients": [
      {"mobile_id": "local_123", "server_id": 789, "created": true}
    ],
    "pregnancies": [
      {"mobile_id": "preg_456", "server_id": 101, "created": true}
    ],
    "appointments": [
      {"mobile_id": "app_789", "server_id": 202, "created": false}
    ]
  }
}
```

## 🔒 Sécurité et Authentification

### Système d'Authentification

1. **Token Bearer** : Utilisez l'ID utilisateur comme token après connexion
   ```
   Authorization: Bearer {user_id}
   ```

2. **Changement de Mot de Passe Obligatoire** :
   - Les nouveaux utilisateurs doivent changer leur mot de passe à la première connexion
   - Le middleware redirige automatiquement vers `/change-password/`
   - Les APIs retournent une erreur 403 avec `password_change_required`

3. **Validation des Rôles** :
   - **Admin** : Peut créer des agents et centres de santé
   - **Médecin/Assistant** : Peut créer des patients et synchroniser des données
   - **Patient** : Accès limité aux données personnelles

### Gestion des Erreurs de Sécurité

**Changement de mot de passe requis** (403) :
```json
{
  "error": "password_change_required",
  "message": "Vous devez changer votre mot de passe avant de continuer",
  "redirect_url": "/change-password/"
}
```

**Token invalide** (401) :
```json
{
  "error": "Token invalide"
}
```

**Accès refusé** (403) :
```json
{
  "error": "Accès refusé"
}
```

## 🧪 Exemples de Test

### Test avec cURL

#### 1. Connexion
```bash
curl -X POST http://localhost:8000/api/login/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "admin123"
  }'
```

#### 2. Création d'un médecin
```bash
curl -X POST http://localhost:8000/api/create-health-agent/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 1" \
  -d '{
    "firstname": "Dr. Jean",
    "lastname": "Dupont",
    "tel": "+22912345678",
    "email": "<EMAIL>",
    "role": "docteur",
    "sexe": "M",
    "address": "123 Rue Médicale, Cotonou",
    "hospital_id": 1,
    "service_id": 1,
    "speciality_id": 1
  }'
```

#### 3. Création d'un patient
```bash
curl -X POST http://localhost:8000/api/create-patient/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer 2" \
  -d '{
    "firstname": "Marie",
    "lastname": "Kouassi",
    "tel": "+22987654321",
    "email": "<EMAIL>",
    "sexe": "F",
    "birth_date": "1990-05-15",
    "address": "456 Avenue de la Paix, Cotonou",
    "language_id": 1,
    "occupation": "Enseignante",
    "is_child": false,
    "is_pregnant": false
  }'
```

#### 4. Changement de mot de passe
```bash
curl -X POST http://localhost:8000/api/change-password/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "current_password": "TempPassword123",
    "new_password": "MonNouveauMotDePasse123!"
  }'
```

### Test avec Postman

#### Collection Postman
Créez une collection avec les variables suivantes :
- `base_url` : `http://localhost:8000`
- `auth_token` : `Bearer {user_id}` (à définir après connexion)

#### Tests automatisés
Ajoutez ces scripts de test dans Postman :

**Pour la connexion** :
```javascript
pm.test("Login successful", function () {
    pm.response.to.have.status(200);
    var jsonData = pm.response.json();
    pm.expect(jsonData.status).to.eql("success");
    pm.environment.set("user_id", jsonData.user_id);
    pm.environment.set("auth_token", "Bearer " + jsonData.user_id);
});
```

**Pour la création d'utilisateur** :
```javascript
pm.test("User created with email sent", function () {
    pm.response.to.have.status(200);
    var jsonData = pm.response.json();
    pm.expect(jsonData.status).to.eql("success");
    pm.expect(jsonData.email_sent).to.eql(true);
    pm.expect(jsonData.username).to.match(/^[A-Z]{3}-\d{8}$/);
});
```

## 📱 Intégration Mobile

### Headers recommandés pour mobile
```
Content-Type: application/json
Authorization: Bearer {user_id}
User-Agent: PTC-Care-Mobile/1.0
Accept: application/json
```

### Gestion hors ligne
- Utilisez `/api/sync-mobile-data/` pour synchroniser les données collectées hors ligne
- Stockez les données localement avec des IDs temporaires (`mobile_id`)
- La synchronisation retourne les correspondances `mobile_id` → `server_id`

## 🔧 Configuration de Développement

### Variables d'environnement
```bash
# .env
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
PTCCARE_BASE_URL=http://localhost:8000
PASSWORD_RESET_TOKEN_EXPIRY_HOURS=48
```

### Serveur de développement
```bash
# Activer l'environnement virtuel
.\env\Scripts\activate

# Démarrer le serveur
python manage.py runserver

# API disponible sur http://localhost:8000/api/
```

## 📞 Support

- **Documentation** : Ce fichier
- **Logs d'erreur** : Vérifiez les logs Django pour les détails des erreurs
- **Email de test** : Les emails sont envoyés à l'adresse configurée dans `EMAIL_HOST_USER`

---

**Version** : 1.0
**Dernière mise à jour** : 2024-06-14
**Statut** : ✅ Opérationnel avec notifications email automatiques
