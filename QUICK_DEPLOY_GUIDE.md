# 🚀 Guide de Déploiement Rapide PTCCare sur Heroku

## ⚠️ Note importante
Le test local des paramètres de production peut échouer car certaines dépendances (comme `dj-database-url`) sont spécifiques à Heroku. C'est normal ! Les fichiers sont configurés correctement pour fonctionner sur Heroku.

## 📋 Prérequis
1. **Compte Heroku** : [Créer un compte](https://signup.heroku.com/)
2. **Heroku CLI** : [Installer Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli)
3. **Git** : Installé et configuré

## 🚀 Déploiement en 5 étapes

### Étape 1 : Connexion à Heroku
```bash
heroku login
```

### Étape 2 : Création de l'application
```bash
# Remplacez "ptccare-app-2025" par le nom de votre choix
heroku create ptccare-app-2025
```

### Étape 3 : Ajout des add-ons
```bash
# Base de données PostgreSQL
heroku addons:create heroku-postgresql:essential-0 -a ptccare-app-2025

# Cache Redis pour Celery
heroku addons:create heroku-redis:mini -a ptccare-app-2025
```

### Étape 4 : Configuration des variables d'environnement
```bash
# Variables essentielles
heroku config:set SECRET_KEY="$(openssl rand -base64 32)" -a ptccare-app-2025
heroku config:set DEBUG=False -a ptccare-app-2025
heroku config:set ADMIN_EMAIL="<EMAIL>" -a ptccare-app-2025
heroku config:set ADMIN_PASSWORD="VotreMotDePasseSecurise123!" -a ptccare-app-2025
heroku config:set DEFAULT_FROM_EMAIL="<EMAIL>" -a ptccare-app-2025
heroku config:set SMS_ENABLED=False -a ptccare-app-2025
```

### Étape 5 : Déploiement
```bash
# Initialiser Git (si pas déjà fait)
git init
git add .
git commit -m "Initial commit - PTC Care application"

# Ajouter Heroku comme remote
heroku git:remote -a ptccare-app-2025

# Déployer
git push heroku main
```

## 🔧 Post-déploiement

### Migrations et configuration
```bash
# Appliquer les migrations
heroku run python manage.py migrate -a ptccare-app-2025

# Collecter les fichiers statiques
heroku run python manage.py collectstatic --noinput -a ptccare-app-2025

# Charger les données de test (optionnel)
heroku run python populate_test_data.py -a ptccare-app-2025
```

### Démarrer les workers
```bash
# Démarrer les dynos
heroku ps:scale web=1 worker=1 -a ptccare-app-2025
```

## 🌐 Vérification

### Ouvrir l'application
```bash
heroku open -a ptccare-app-2025
```

### Tester l'API mobile
```bash
# Remplacez "ptccare-app-2025" par votre nom d'app
curl -X POST https://ptccare-app-2025.herokuapp.com/api/mobile/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"VotreMotDePasseSecurise123!"}'
```

## 📊 URLs de l'application

- **Interface web** : `https://ptccare-app-2025.herokuapp.com/`
- **Admin Django** : `https://ptccare-app-2025.herokuapp.com/super-admin/`
- **API mobile** : `https://ptccare-app-2025.herokuapp.com/api/mobile/`

## 🔍 Surveillance

### Voir les logs
```bash
heroku logs --tail -a ptccare-app-2025
```

### Vérifier le statut
```bash
heroku ps -a ptccare-app-2025
```

## 🚨 Dépannage

### Si les migrations échouent
```bash
heroku run python manage.py migrate --fake-initial -a ptccare-app-2025
```

### Si les fichiers statiques ne se chargent pas
```bash
heroku run python manage.py collectstatic --clear --noinput -a ptccare-app-2025
```

### Redémarrer l'application
```bash
heroku restart -a ptccare-app-2025
```

## 🎉 Félicitations !

Votre application PTCCare est maintenant déployée sur Heroku avec :
- ✅ Base de données PostgreSQL
- ✅ Cache Redis
- ✅ API mobile sécurisée
- ✅ Interface d'administration
- ✅ Gestion des fichiers statiques

L'application est prête pour l'intégration avec votre application mobile Flutter ! 📱
