# Guide Complet d'Intégration Mobile - PTC Care APIs

## 📋 Vue d'Ensemble

Ce guide complet fournit toutes les informations nécessaires pour intégrer les APIs PTC Care dans une application mobile Android/iOS, avec support complet des notifications email automatiques, du changement de mot de passe obligatoire, et de la synchronisation hors ligne.

## 📁 Structure de la Documentation

### 🚀 **Guides Principaux**
1. **`MOBILE_INTEGRATION_GUIDE.md`** - Architecture et authentification
2. **`MOBILE_INTEGRATION_PART2.md`** - Récupération de données et synchronisation
3. **`MOBILE_INTEGRATION_PART3.md`** - Considérations mobiles et optimisation
4. **`MOBILE_QUICK_START_GUIDE.md`** - Guide de démarrage rapide (4-6h)

### 🔧 **Fichiers de Configuration**
5. **`PTC_Care_API_Collection.postman_collection.json`** - Collection Postman complète
6. **`PTC_Care_Environment.postman_environment.json`** - Environnement de test
7. **`API_TESTING_GUIDE.md`** - Guide de test des APIs

## 🏗️ Architecture Recommandée

### Composants Principaux

```
┌─────────────────────────────────────────────────────────┐
│                    Mobile Application                    │
├─────────────────┬─────────────────┬─────────────────────┤
│ Authentication  │   Data Layer    │   Sync Manager      │
│ Manager         │                 │                     │
│ • Login/Logout  │ • Local Cache   │ • Offline Queue     │
│ • Token Mgmt    │ • API Client    │ • Conflict Res.     │
│ • Password      │ • Error Handle  │ • Background Sync   │
│   Change        │                 │                     │
└─────────────────┴─────────────────┴─────────────────────┘
                            │
                    HTTPS/TLS + Bearer Token
                            │
┌─────────────────────────────────────────────────────────┐
│                    PTC Care API                         │
├─────────────────┬─────────────────┬─────────────────────┤
│ Authentication  │   User Mgmt     │   Email Service     │
│ • Login         │ • Create Agent  │ • Welcome Email     │
│ • Password      │ • Create Patient│ • Password Reset    │
│   Change        │ • Role Mgmt     │ • Confirmation      │
└─────────────────┴─────────────────┴─────────────────────┘
```

## 🔐 Flux d'Authentification Complet

### 1. Connexion Utilisateur
```
User Input (Email/Username + Password)
        ↓
POST /api/login/ 
        ↓
Success (200) → Save Token → Main App
        ↓
Error (401) → Show Error Message
```

### 2. Gestion du Changement de Mot de Passe Obligatoire
```
API Call with Token
        ↓
Response 403 + "password_change_required"
        ↓
Redirect to Password Change Screen
        ↓
POST /api/change-password/
        ↓
Success → Email Confirmation → Continue
```

### 3. Création d'Utilisateur avec Email
```
Create User Form
        ↓
POST /api/create-health-agent/ or /api/create-patient/
        ↓
Success + email_sent: true
        ↓
Show Success Message + Email Status
        ↓
User receives welcome email with credentials
```

## 📱 Implémentation par Plateforme

### Android (Kotlin + Compose)

#### Dépendances Clés
```kotlin
// Networking
implementation "com.squareup.retrofit2:retrofit:2.9.0"
implementation "com.squareup.okhttp3:okhttp:4.11.0"

// Sécurité
implementation "androidx.security:security-crypto:1.1.0-alpha06"

// Base de données
implementation "androidx.room:room-runtime:2.5.0"
implementation "androidx.room:room-ktx:2.5.0"

// Injection de dépendances
implementation "com.google.dagger:hilt-android:2.47"
```

#### Configuration de Base
```kotlin
@HiltAndroidApp
class PTCCareApplication : Application()

object ApiConfig {
    const val BASE_URL = "http://********:8000" // Développement
    const val TIMEOUT_SECONDS = 30L
}
```

### iOS (Swift + SwiftUI)

#### Dépendances Clés
```swift
dependencies: [
    .package(url: "https://github.com/Alamofire/Alamofire.git", from: "5.8.0"),
    .package(url: "https://github.com/kishikawakatsumi/KeychainAccess.git", from: "4.2.2"),
    .package(url: "https://github.com/realm/realm-swift.git", from: "10.42.0")
]
```

## 🔄 Synchronisation Hors Ligne

### Stratégie de Synchronisation

#### 1. Stockage Local
- **Android** : Room Database (SQLite)
- **iOS** : Core Data ou Realm
- **Structure** : mobile_id (UUID) + server_id (Int)

#### 2. Queue de Synchronisation
```
Action Hors Ligne → Local Database (needsSync = true)
        ↓
Connexion Détectée → Background Sync
        ↓
POST /api/sync-mobile-data/
        ↓
Response → Update mobile_id → server_id mapping
```

#### 3. Résolution des Conflits
- **Priorité serveur** : Les données serveur écrasent les données locales
- **Horodatage** : Utiliser `updated_at` pour résoudre les conflits
- **Validation** : Vérifier l'intégrité des données avant sync

## 📧 Intégration des Notifications Email

### Validation Côté Mobile

#### 1. Création d'Utilisateur
```kotlin
// Android
val response = apiService.createPatient(patientData)
if (response.emailSent) {
    showMessage("Patient créé avec succès. Email envoyé à ${patientData.email}")
} else {
    showWarning("Patient créé mais email non envoyé: ${response.emailError}")
}
```

#### 2. Changement de Mot de Passe
```kotlin
// Intercepteur pour détecter le changement obligatoire
if (response.code == 403 && errorBody.error == "password_change_required") {
    navigateToPasswordChange()
}
```

### Types d'Emails Automatiques
1. **Email de bienvenue** : Identifiants + lien de changement de mot de passe
2. **Email de confirmation** : Après changement de mot de passe réussi
3. **Format** : HTML responsive + version texte en français

## 🔒 Sécurité et Bonnes Pratiques

### 1. Stockage Sécurisé
- **Android** : EncryptedSharedPreferences pour les tokens
- **iOS** : Keychain avec `whenUnlockedThisDeviceOnly`
- **Chiffrement** : AES-256 pour les données sensibles

### 2. Communication Réseau
- **HTTPS obligatoire** en production
- **Certificate Pinning** pour prévenir les attaques MITM
- **Timeout appropriés** : 30s requêtes, 60s uploads

### 3. Gestion des Tokens
- **Format** : `Bearer {user_id}`
- **Stockage** : Chiffré localement
- **Expiration** : Gestion automatique avec refresh

## 🧪 Tests et Validation

### Tests Essentiels

#### 1. Tests d'Authentification
- [ ] Connexion avec email
- [ ] Connexion avec username
- [ ] Gestion du changement de mot de passe obligatoire
- [ ] Déconnexion et nettoyage des données

#### 2. Tests de Création d'Utilisateurs
- [ ] Création d'agent de santé avec email
- [ ] Création de patient avec email
- [ ] Validation des champs obligatoires
- [ ] Gestion des erreurs de validation

#### 3. Tests de Synchronisation
- [ ] Création de données hors ligne
- [ ] Synchronisation automatique en ligne
- [ ] Mapping mobile_id → server_id
- [ ] Gestion des conflits

#### 4. Tests de Sécurité
- [ ] Stockage sécurisé des tokens
- [ ] Validation des certificats SSL
- [ ] Gestion des erreurs d'authentification

### Outils de Test
- **Postman** : Collection complète fournie
- **Tests unitaires** : JUnit (Android), XCTest (iOS)
- **Tests d'intégration** : Avec serveur de développement

## 📊 Métriques et Monitoring

### Analytics Recommandées
```kotlin
// Exemple Android
class AnalyticsManager {
    fun trackApiCall(endpoint: String, success: Boolean, responseTime: Long)
    fun trackSyncEvent(itemCount: Int, success: Boolean)
    fun trackEmailNotification(type: String, sent: Boolean)
    fun trackPasswordChange(forced: Boolean, success: Boolean)
}
```

### Métriques Clés
- **Taux de succès des APIs** : > 95%
- **Temps de réponse moyen** : < 3 secondes
- **Taux de livraison email** : > 90%
- **Synchronisation hors ligne** : < 30 secondes

## 🚀 Déploiement et Configuration

### Environnements

#### Développement
- **URL** : `http://localhost:8000` ou `http://********:8000`
- **Logs** : Niveau DEBUG activé
- **Email** : Configuration de test

#### Production
- **URL** : `https://api.ptccare.com`
- **Logs** : Niveau ERROR uniquement
- **Email** : Configuration SMTP production
- **Sécurité** : Certificate pinning activé

### Variables de Configuration
```kotlin
// Android
buildConfigField "String", "API_BASE_URL", "\"https://api.ptccare.com\""
buildConfigField "String", "EMAIL_SUPPORT", "\"<EMAIL>\""
buildConfigField "boolean", "DEBUG_MODE", "false"
```

## 📋 Checklist de Livraison

### Phase 1: Configuration (1-2 heures)
- [ ] Dépendances installées
- [ ] Configuration réseau (Retrofit/Alamofire)
- [ ] Stockage sécurisé configuré
- [ ] Base de données locale créée

### Phase 2: Authentification (2-3 heures)
- [ ] Écran de connexion fonctionnel
- [ ] Gestion des tokens
- [ ] Changement de mot de passe obligatoire
- [ ] Déconnexion sécurisée

### Phase 3: Fonctionnalités Principales (3-4 heures)
- [ ] Création d'utilisateurs avec email
- [ ] Récupération des données
- [ ] Affichage des listes (patients, agents)
- [ ] Gestion des erreurs

### Phase 4: Synchronisation (2-3 heures)
- [ ] Stockage hors ligne
- [ ] Queue de synchronisation
- [ ] Sync en arrière-plan
- [ ] Résolution des conflits

### Phase 5: Tests et Optimisation (2-3 heures)
- [ ] Tests unitaires
- [ ] Tests d'intégration
- [ ] Optimisation des performances
- [ ] Validation de sécurité

## 🎯 Résultats Attendus

### Fonctionnalités Complètes
- ✅ **Authentification robuste** avec gestion du changement de mot de passe
- ✅ **Notifications email automatiques** pour tous les nouveaux utilisateurs
- ✅ **Synchronisation hors ligne** avec résolution de conflits
- ✅ **Sécurité renforcée** avec chiffrement et HTTPS
- ✅ **Interface utilisateur** intuitive et responsive

### Performance
- **Temps de connexion** : < 2 secondes
- **Synchronisation** : < 30 secondes pour 100 éléments
- **Utilisation batterie** : Optimisée avec sync intelligente
- **Taille cache** : < 50 MB pour usage normal

---

**Temps total d'implémentation** : 10-15 heures  
**Niveau de difficulté** : Intermédiaire  
**Prérequis** : Connaissance Android/iOS + APIs REST  
**Support** : Documentation complète + exemples de code fournis
