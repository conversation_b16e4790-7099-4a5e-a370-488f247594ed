# Guide d'Intégration Authentification & Synchronisation - App Mobile Existante

## 🎯 Objectif

Intégrer l'authentification et la synchronisation avec les APIs PTC Care dans votre application mobile déjà fonctionnelle.

## 🔗 Configuration API de Base

### 1. Endpoints à Utiliser

```kotlin
// Configuration des endpoints pour votre app existante
object PTCCareEndpoints {
    const val BASE_URL = "http://your-server.com" // Remplacez par votre serveur
    
    // Authentification
    const val LOGIN = "/api/login/"
    const val CHANGE_PASSWORD = "/api/change-password/"
    
    // Données de référence
    const val INITIAL_DATA = "/api/initial-data/"
    const val HEALTH_CENTERS = "/api/health-centers/"
    
    // Gestion utilisateurs (Admin)
    const val CREATE_HEALTH_AGENT = "/api/create-health-agent/"
    const val CREATE_HEALTH_CENTER = "/api/create-health-center/"
    const val GET_AGENTS = "/api/agents/"
    
    // Gestion patients (Agent)
    const val CREATE_PATIENT = "/api/create-patient/"
    const val GET_PATIENTS = "/api/patients/"
    
    // Synchronisation
    const val SYNC_MOBILE_DATA = "/api/sync-mobile-data/"
}
```

### 2. Adapter Votre Service HTTP Existant

```kotlin
// Ajoutez ces méthodes à votre service HTTP existant
interface YourExistingApiService {
    
    // AUTHENTIFICATION
    @POST(PTCCareEndpoints.LOGIN)
    suspend fun loginPTCCare(@Body request: PTCLoginRequest): Response<PTCLoginResponse>
    
    @POST(PTCCareEndpoints.CHANGE_PASSWORD)
    suspend fun changePassword(@Body request: PTCChangePasswordRequest): Response<PTCApiResponse>
    
    // SYNCHRONISATION
    @POST(PTCCareEndpoints.SYNC_MOBILE_DATA)
    suspend fun syncData(
        @Header("Authorization") token: String,
        @Body request: PTCSyncRequest
    ): Response<PTCSyncResponse>
    
    // GESTION ADMIN
    @POST(PTCCareEndpoints.CREATE_HEALTH_AGENT)
    suspend fun createAgent(
        @Header("Authorization") token: String,
        @Body request: PTCCreateAgentRequest
    ): Response<PTCCreateAgentResponse>
    
    @POST(PTCCareEndpoints.CREATE_HEALTH_CENTER)
    suspend fun createHealthCenter(
        @Header("Authorization") token: String,
        @Body request: PTCCreateCenterRequest
    ): Response<PTCCreateCenterResponse>
    
    // GESTION AGENT
    @POST(PTCCareEndpoints.CREATE_PATIENT)
    suspend fun createPatient(
        @Header("Authorization") token: String,
        @Body request: PTCCreatePatientRequest
    ): Response<PTCCreatePatientResponse>
    
    @GET(PTCCareEndpoints.GET_PATIENTS)
    suspend fun getPatients(@Header("Authorization") token: String): Response<PTCPatientsResponse>
    
    @GET(PTCCareEndpoints.GET_AGENTS)
    suspend fun getAgents(@Header("Authorization") token: String): Response<PTCAgentsResponse>
    
    @GET(PTCCareEndpoints.HEALTH_CENTERS)
    suspend fun getHealthCenters(): Response<PTCHealthCentersResponse>
}
```

## 🔐 Intégration Authentification

### 1. Modèles de Données PTC Care

```kotlin
// Modèles pour l'authentification PTC Care
data class PTCLoginRequest(
    val email: String,
    val password: String
)

data class PTCLoginResponse(
    val status: String,
    val user_id: Int,
    val username: String,
    val profile_id: Int,
    val role: String, // "admin", "agent", "patient"
    val name: String,
    val hospital_id: Int?,
    val service_id: Int?,
    val speciality_id: Int?
)

data class PTCChangePasswordRequest(
    val email: String,
    val current_password: String?,
    val new_password: String,
    val token: String?
)

data class PTCApiResponse(
    val status: String,
    val message: String
)

data class PTCApiError(
    val error: String,
    val message: String? = null
)
```

### 2. Adapter Votre Gestionnaire d'Authentification

```kotlin
// Ajoutez ces méthodes à votre gestionnaire d'auth existant
class YourExistingAuthManager {
    
    // Méthode d'authentification PTC Care
    suspend fun authenticateWithPTCCare(email: String, password: String): AuthResult {
        return try {
            val request = PTCLoginRequest(email, password)
            val response = yourApiService.loginPTCCare(request)
            
            if (response.isSuccessful) {
                val ptcData = response.body()!!
                
                // Mapper vers votre modèle utilisateur existant
                val user = mapPTCUserToYourModel(ptcData)
                
                // Sauvegarder dans votre système existant
                saveUserSession(user)
                
                AuthResult.Success(user)
            } else {
                val error = parseError(response.errorBody())
                AuthResult.Error(error.error)
            }
        } catch (e: Exception) {
            AuthResult.Error("Erreur de connexion: ${e.message}")
        }
    }
    
    // Mapper les données PTC vers votre modèle
    private fun mapPTCUserToYourModel(ptcData: PTCLoginResponse): YourUserModel {
        return YourUserModel(
            id = ptcData.user_id,
            username = ptcData.username,
            name = ptcData.name,
            email = "", // Vous pouvez stocker l'email utilisé pour la connexion
            role = mapPTCRoleToYourRole(ptcData.role),
            token = "Bearer ${ptcData.user_id}",
            profileId = ptcData.profile_id,
            hospitalId = ptcData.hospital_id,
            serviceId = ptcData.service_id,
            specialityId = ptcData.speciality_id,
            isPTCUser = true // Flag pour identifier les utilisateurs PTC
        )
    }
    
    // Mapper les rôles PTC vers vos rôles
    private fun mapPTCRoleToYourRole(ptcRole: String): YourRoleEnum {
        return when (ptcRole) {
            "admin", "docteur" -> YourRoleEnum.ADMINISTRATOR
            "assistant" -> YourRoleEnum.AGENT
            else -> YourRoleEnum.AGENT // Par défaut
        }
    }
    
    // Gestion du changement de mot de passe obligatoire
    suspend fun handlePasswordChangeRequired(email: String, currentPassword: String, newPassword: String): Boolean {
        return try {
            val request = PTCChangePasswordRequest(
                email = email,
                current_password = currentPassword,
                new_password = newPassword,
                token = null
            )
            
            val response = yourApiService.changePassword(request)
            response.isSuccessful
        } catch (e: Exception) {
            false
        }
    }
}

// Ajoutez ce flag à votre modèle utilisateur existant
data class YourUserModel(
    // Vos champs existants...
    val isPTCUser: Boolean = false, // Nouveau champ pour identifier les utilisateurs PTC
    val token: String? = null // Pour stocker le token PTC Care
)
```

### 3. Intercepteur pour Changement de Mot de Passe

```kotlin
// Ajoutez cet intercepteur à votre configuration HTTP existante
class PTCPasswordChangeInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val response = chain.proceed(chain.request())
        
        // Vérifier si changement de mot de passe requis
        if (response.code == 403) {
            try {
                val errorBody = response.body?.string()
                val error = Gson().fromJson(errorBody, PTCApiError::class.java)
                
                if (error.error == "password_change_required") {
                    // Envoyer un événement à votre app pour afficher l'écran de changement
                    EventBus.getDefault().post(PasswordChangeRequiredEvent())
                }
            } catch (e: Exception) {
                // Ignorer les erreurs de parsing
            }
        }
        
        return response
    }
}

// Événement pour notification
data class PasswordChangeRequiredEvent(val message: String = "Changement de mot de passe requis")
```

## 🔄 Implémentation Synchronisation

### 1. Modèles de Synchronisation

```kotlin
// Modèles pour la synchronisation PTC Care
data class PTCSyncRequest(
    val patients: List<PTCPatientSync> = emptyList(),
    val pregnancies: List<PTCPregnancySync> = emptyList(),
    val appointments: List<PTCAppointmentSync> = emptyList()
)

data class PTCPatientSync(
    val mobile_id: String, // Votre ID local
    val firstname: String,
    val lastname: String,
    val tel: String,
    val sexe: String,
    val birth_date: String,
    val address: String,
    val is_child: Boolean
)

data class PTCSyncResponse(
    val status: String,
    val message: String,
    val processed: List<PTCProcessedItem>
)

data class PTCProcessedItem(
    val mobile_id: String,
    val server_id: Int,
    val created: Boolean
)

// Ajoutez ces champs à vos modèles existants
data class YourPatientModel(
    // Vos champs existants...
    val mobileId: String = UUID.randomUUID().toString(), // ID unique pour sync
    val serverId: Int? = null, // ID du serveur après sync
    val syncStatus: SyncStatus = SyncStatus.PENDING,
    val needsSync: Boolean = true
)

enum class SyncStatus {
    PENDING, SYNCED, ERROR
}
```

### 2. Gestionnaire de Synchronisation

```kotlin
// Ajoutez ce gestionnaire à votre architecture existante
class PTCSyncManager(
    private val apiService: YourExistingApiService,
    private val localDatabase: YourLocalDatabase,
    private val authManager: YourExistingAuthManager
) {
    
    suspend fun syncAllData(): SyncResult {
        return try {
            val user = authManager.getCurrentUser()
            if (user?.isPTCUser != true || user.token == null) {
                return SyncResult.Error("Utilisateur PTC non connecté")
            }
            
            // Récupérer les données à synchroniser
            val pendingPatients = localDatabase.getPatientsNeedingSync()
            val pendingPregnancies = localDatabase.getPregnanciesNeedingSync()
            val pendingAppointments = localDatabase.getAppointmentsNeedingSync()
            
            if (pendingPatients.isEmpty() && pendingPregnancies.isEmpty() && pendingAppointments.isEmpty()) {
                return SyncResult.Success(0, "Aucune donnée à synchroniser")
            }
            
            // Préparer la requête de synchronisation
            val syncRequest = PTCSyncRequest(
                patients = pendingPatients.map { mapToSyncModel(it) },
                pregnancies = pendingPregnancies.map { mapToSyncModel(it) },
                appointments = pendingAppointments.map { mapToSyncModel(it) }
            )
            
            // Envoyer au serveur
            val response = apiService.syncData(user.token!!, syncRequest)
            
            if (response.isSuccessful) {
                val result = response.body()!!
                
                // Mettre à jour les IDs locaux avec les IDs serveur
                updateLocalRecordsWithServerIds(result.processed)
                
                SyncResult.Success(result.processed.size, "Synchronisation réussie")
            } else {
                SyncResult.Error("Erreur serveur: ${response.code()}")
            }
            
        } catch (e: Exception) {
            SyncResult.Error("Erreur de synchronisation: ${e.message}")
        }
    }
    
    private fun mapToSyncModel(patient: YourPatientModel): PTCPatientSync {
        return PTCPatientSync(
            mobile_id = patient.mobileId,
            firstname = patient.firstname,
            lastname = patient.lastname,
            tel = patient.tel,
            sexe = patient.sexe,
            birth_date = patient.birthDate,
            address = patient.address,
            is_child = patient.isChild
        )
    }
    
    private suspend fun updateLocalRecordsWithServerIds(processed: List<PTCProcessedItem>) {
        processed.forEach { item ->
            when {
                item.mobile_id.startsWith("patient_") -> {
                    localDatabase.updatePatientServerId(item.mobile_id, item.server_id)
                }
                item.mobile_id.startsWith("pregnancy_") -> {
                    localDatabase.updatePregnancyServerId(item.mobile_id, item.server_id)
                }
                item.mobile_id.startsWith("appointment_") -> {
                    localDatabase.updateAppointmentServerId(item.mobile_id, item.server_id)
                }
            }
        }
    }
    
    // Synchronisation automatique en arrière-plan
    suspend fun scheduleAutoSync() {
        // Utilisez votre système de tâches en arrière-plan existant
        // (WorkManager, JobScheduler, etc.)
        YourBackgroundTaskManager.schedulePeriodicTask(
            taskName = "PTCAutoSync",
            intervalMinutes = 15
        ) {
            syncAllData()
        }
    }
}

sealed class SyncResult {
    data class Success(val syncedCount: Int, val message: String) : SyncResult()
    data class Error(val message: String) : SyncResult()
}
```

### 3. Intégration dans Votre UI Existante

```kotlin
// Dans votre ViewModel de dashboard existant
class YourExistingDashboardViewModel {
    
    private val ptcSyncManager = PTCSyncManager(apiService, database, authManager)
    
    // Ajoutez ces méthodes à votre ViewModel existant
    fun getSyncStatus(): LiveData<SyncStatusInfo> {
        return liveData {
            val pendingCount = database.countRecordsNeedingSync()
            val lastSyncTime = preferences.getLastSyncTime()
            
            emit(SyncStatusInfo(
                pendingCount = pendingCount,
                lastSyncTime = lastSyncTime,
                syncNeeded = pendingCount > 0
            ))
        }
    }
    
    suspend fun performManualSync(): SyncResult {
        return ptcSyncManager.syncAllData()
    }
    
    fun startAutoSync() {
        viewModelScope.launch {
            ptcSyncManager.scheduleAutoSync()
        }
    }
}

data class SyncStatusInfo(
    val pendingCount: Int,
    val lastSyncTime: Long?,
    val syncNeeded: Boolean
)
```

## 🏥 Intégration Création d'Entités

### 1. Création d'Établissements (Admin)

```kotlin
// Ajoutez à votre gestionnaire d'établissements existant
class YourExistingHealthCenterManager {
    
    suspend fun createHealthCenterWithPTC(name: String, location: String): CreateResult {
        val user = authManager.getCurrentUser()
        
        if (user?.isPTCUser == true && user.role == YourRoleEnum.ADMINISTRATOR) {
            return try {
                val request = PTCCreateCenterRequest(name, location)
                val response = apiService.createHealthCenter(user.token!!, request)
                
                if (response.isSuccessful) {
                    val result = response.body()!!
                    
                    // Créer dans votre base locale avec l'ID serveur
                    val healthCenter = YourHealthCenterModel(
                        id = result.health_center_id,
                        name = name,
                        location = location,
                        serverId = result.health_center_id,
                        syncStatus = SyncStatus.SYNCED
                    )
                    
                    localDatabase.insertHealthCenter(healthCenter)
                    CreateResult.Success(healthCenter)
                } else {
                    CreateResult.Error("Erreur serveur")
                }
            } catch (e: Exception) {
                // Sauvegarder localement pour sync ultérieure
                val tempCenter = YourHealthCenterModel(
                    id = generateTempId(),
                    name = name,
                    location = location,
                    mobileId = UUID.randomUUID().toString(),
                    syncStatus = SyncStatus.PENDING
                )
                
                localDatabase.insertHealthCenter(tempCenter)
                CreateResult.Success(tempCenter)
            }
        } else {
            // Logique existante pour les utilisateurs non-PTC
            return createHealthCenterLocally(name, location)
        }
    }
}
```

### 2. Création d'Agents (Admin)

```kotlin
// Ajoutez à votre gestionnaire d'agents existant
class YourExistingAgentManager {
    
    suspend fun createAgentWithPTC(agentData: YourAgentCreationModel): CreateResult {
        val user = authManager.getCurrentUser()
        
        if (user?.isPTCUser == true && user.role == YourRoleEnum.ADMINISTRATOR) {
            return try {
                val ptcRequest = PTCCreateAgentRequest(
                    firstname = agentData.firstname,
                    lastname = agentData.lastname,
                    tel = agentData.tel,
                    email = agentData.email,
                    role = if (agentData.isDoctor) "docteur" else "assistant",
                    sexe = agentData.sexe,
                    address = agentData.address,
                    hospital_id = agentData.hospitalId,
                    service_id = agentData.serviceId,
                    speciality_id = agentData.specialityId
                )
                
                val response = apiService.createAgent(user.token!!, ptcRequest)
                
                if (response.isSuccessful) {
                    val result = response.body()!!
                    
                    // Créer dans votre base locale
                    val agent = YourAgentModel(
                        id = result.agent_id,
                        firstname = agentData.firstname,
                        lastname = agentData.lastname,
                        email = agentData.email,
                        username = result.username,
                        emailSent = result.email_sent,
                        serverId = result.agent_id,
                        syncStatus = SyncStatus.SYNCED
                    )
                    
                    localDatabase.insertAgent(agent)
                    CreateResult.Success(agent, "Agent créé avec succès. Email envoyé: ${result.email_sent}")
                } else {
                    CreateResult.Error("Erreur serveur")
                }
            } catch (e: Exception) {
                // Sauvegarder localement pour sync ultérieure
                val tempAgent = YourAgentModel.fromCreationData(agentData)
                tempAgent.syncStatus = SyncStatus.PENDING
                
                localDatabase.insertAgent(tempAgent)
                CreateResult.Success(tempAgent, "Agent créé localement. Sera synchronisé plus tard.")
            }
        } else {
            // Logique existante pour les utilisateurs non-PTC
            return createAgentLocally(agentData)
        }
    }
}
```

### 3. Création de Patients (Agent)

```kotlin
// Ajoutez à votre gestionnaire de patients existant
class YourExistingPatientManager {
    
    suspend fun createPatientWithPTC(patientData: YourPatientCreationModel): CreateResult {
        val user = authManager.getCurrentUser()
        
        if (user?.isPTCUser == true) {
            return try {
                val ptcRequest = mapToPTCCreatePatientRequest(patientData)
                val response = apiService.createPatient(user.token!!, ptcRequest)
                
                if (response.isSuccessful) {
                    val result = response.body()!!
                    
                    // Créer dans votre base locale avec l'ID serveur
                    val patient = YourPatientModel.fromCreationData(patientData)
                    patient.serverId = result.patient_id
                    patient.syncStatus = SyncStatus.SYNCED
                    
                    localDatabase.insertPatient(patient)
                    CreateResult.Success(patient, "Patient créé avec succès. Email envoyé: ${result.email_sent}")
                } else {
                    CreateResult.Error("Erreur serveur")
                }
            } catch (e: Exception) {
                // Sauvegarder localement pour sync ultérieure
                val tempPatient = YourPatientModel.fromCreationData(patientData)
                tempPatient.mobileId = UUID.randomUUID().toString()
                tempPatient.syncStatus = SyncStatus.PENDING
                
                localDatabase.insertPatient(tempPatient)
                CreateResult.Success(tempPatient, "Patient créé localement. Sera synchronisé plus tard.")
            }
        } else {
            // Logique existante pour les utilisateurs non-PTC
            return createPatientLocally(patientData)
        }
    }
    
    private fun mapToPTCCreatePatientRequest(patientData: YourPatientCreationModel): PTCCreatePatientRequest {
        return PTCCreatePatientRequest(
            firstname = patientData.firstname,
            lastname = patientData.lastname,
            tel = patientData.tel,
            email = patientData.email,
            sexe = patientData.sexe,
            birth_date = patientData.birthDate,
            address = patientData.address,
            language_id = patientData.languageId,
            occupation = patientData.occupation,
            assurance = patientData.assurance,
            is_child = patientData.isChild,
            is_pregnant = patientData.isPregnant,
            pregnancy_situation = patientData.pregnancyInfo?.situation,
            pregnancy_description = patientData.pregnancyInfo?.description,
            pregnancy_term = patientData.pregnancyInfo?.term,
            pregnancy_start_date = patientData.pregnancyInfo?.startDate,
            study_level = patientData.studyLevel,
            husband_name = patientData.husbandName,
            husband_tel = patientData.husbandTel,
            personal_history = patientData.personalHistory,
            family_history = patientData.familyHistory,
            allergies = patientData.allergies,
            vaccinations = patientData.vaccinations,
            special_marks = patientData.specialMarks,
            mother_id = patientData.motherId,
            other_informations = patientData.otherInformations
        )
    }
}
```

## 📱 Adaptation de Votre UI

### 1. Écran de Connexion

```kotlin
// Dans votre fragment/activity de connexion existant
class YourExistingLoginFragment {
    
    private fun handleLogin(email: String, password: String) {
        viewLifecycleOwner.lifecycleScope.launch {
            showLoading(true)
            
            // Essayer d'abord l'authentification PTC Care
            val ptcResult = authManager.authenticateWithPTCCare(email, password)
            
            when (ptcResult) {
                is AuthResult.Success -> {
                    // Connexion réussie avec PTC Care
                    navigateToDashboard(ptcResult.user)
                }
                is AuthResult.Error -> {
                    if (ptcResult.message.contains("404") || ptcResult.message.contains("incorrect")) {
                        // Utilisateur non trouvé dans PTC Care, essayer votre système local
                        val localResult = authManager.authenticateLocally(email, password)
                        handleLocalAuthResult(localResult)
                    } else {
                        showError(ptcResult.message)
                    }
                }
            }
            
            showLoading(false)
        }
    }
}
```

### 2. Indicateurs de Synchronisation

```kotlin
// Ajoutez ces éléments à vos dashboards existants
class YourExistingDashboardFragment {
    
    private fun setupSyncIndicator() {
        viewModel.getSyncStatus().observe(viewLifecycleOwner) { syncStatus ->
            binding.syncIndicator.isVisible = syncStatus.syncNeeded
            binding.syncBadge.text = syncStatus.pendingCount.toString()
            
            if (syncStatus.lastSyncTime != null) {
                binding.lastSyncText.text = "Dernière sync: ${formatTime(syncStatus.lastSyncTime)}"
            }
        }
        
        binding.syncButton.setOnClickListener {
            performManualSync()
        }
    }
    
    private fun performManualSync() {
        viewLifecycleOwner.lifecycleScope.launch {
            binding.syncProgress.isVisible = true
            
            val result = viewModel.performManualSync()
            
            when (result) {
                is SyncResult.Success -> {
                    showSnackbar("${result.syncedCount} éléments synchronisés")
                }
                is SyncResult.Error -> {
                    showSnackbar("Erreur de sync: ${result.message}")
                }
            }
            
            binding.syncProgress.isVisible = false
        }
    }
}
```

## ⚙️ Configuration et Initialisation

### 1. Initialisation de l'App

```kotlin
// Dans votre Application class existant
class YourExistingApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // Configuration PTC Care
        configurePTCCareIntegration()
        
        // Démarrer la synchronisation automatique pour les utilisateurs PTC
        startPTCSyncIfNeeded()
    }
    
    private fun configurePTCCareIntegration() {
        // Ajouter l'intercepteur de changement de mot de passe
        val existingOkHttpClient = yourExistingOkHttpClient.newBuilder()
            .addInterceptor(PTCPasswordChangeInterceptor())
            .build()
        
        // Mettre à jour votre client Retrofit
        updateRetrofitClientWithPTCSupport(existingOkHttpClient)
    }
    
    private fun startPTCSyncIfNeeded() {
        val authManager = YourExistingAuthManager.getInstance()
        val user = authManager.getCurrentUser()
        
        if (user?.isPTCUser == true) {
            PTCSyncManager.getInstance().startAutoSync()
        }
    }
}
```

### 2. Migration des Données Existantes

```kotlin
// Script de migration pour vos données existantes
class PTCDataMigration {
    
    suspend fun migrateExistingDataForPTCSync() {
        // Ajouter les champs de synchronisation à vos données existantes
        val patients = localDatabase.getAllPatients()
        patients.forEach { patient ->
            if (patient.mobileId.isEmpty()) {
                patient.mobileId = UUID.randomUUID().toString()
                patient.syncStatus = SyncStatus.PENDING
                patient.needsSync = true
                localDatabase.updatePatient(patient)
            }
        }
        
        // Faire de même pour les autres entités
        migrateExistingAgents()
        migrateExistingHealthCenters()
    }
}
```

## 🔧 Conseils d'Implémentation

### 1. **Commencez par l'Authentification**
- Intégrez d'abord l'API de login
- Testez avec un utilisateur admin et un agent
- Gérez le changement de mot de passe obligatoire

### 2. **Implémentez la Synchronisation Progressivement**
- Commencez par les patients (plus simple)
- Puis les agents et établissements
- Testez d'abord la sync manuelle, puis automatique

### 3. **Gardez Votre Logique Existante**
- Ne cassez pas votre fonctionnement actuel
- Ajoutez la logique PTC en parallèle
- Utilisez des flags pour identifier les utilisateurs PTC

### 4. **Testez Étape par Étape**
- Test de connexion
- Test de création d'entité en ligne
- Test de création hors ligne + synchronisation
- Test de gestion des erreurs

Ce guide vous permet d'intégrer progressivement l'authentification et la synchronisation PTC Care sans casser votre application existante.
