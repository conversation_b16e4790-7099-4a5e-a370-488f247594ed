# Guide d'Intégration Flutter - PTC Care API

## 🎯 Objectif

Intégrer l'authentification et la synchronisation avec les APIs PTC Care dans votre application Flutter existante.

## 📦 Packages Flutter Requis

Ajoutez ces dépendances à votre `pubspec.yaml` :

```yaml
dependencies:
  flutter:
    sdk: flutter
  
  # HTTP et API
  dio: ^5.3.2
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  
  # Base de données locale
  sqflite: ^2.3.0
  floor: ^1.4.2
  
  # Stockage sécurisé
  flutter_secure_storage: ^9.0.0
  shared_preferences: ^2.2.2
  
  # State management (choisissez selon votre préférence)
  provider: ^6.1.1
  # ou riverpod: ^2.4.6
  # ou bloc: ^8.1.2
  
  # Utilitaires
  uuid: ^4.1.0
  connectivity_plus: ^5.0.1
  workmanager: ^0.5.1

dev_dependencies:
  # Code generation
  build_runner: ^2.4.7
  retrofit_generator: ^8.0.4
  json_serializable: ^6.7.1
  floor_generator: ^1.4.2
```

## 🔗 Configuration API de Base

### 1. Modèles de Données

```dart
// lib/models/ptc_models.dart
import 'package:json_annotation/json_annotation.dart';

part 'ptc_models.g.dart';

@JsonSerializable()
class PTCLoginRequest {
  final String email;
  final String password;

  PTCLoginRequest({required this.email, required this.password});

  factory PTCLoginRequest.fromJson(Map<String, dynamic> json) =>
      _$PTCLoginRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PTCLoginRequestToJson(this);
}

@JsonSerializable()
class PTCLoginResponse {
  final String status;
  @JsonKey(name: 'user_id')
  final int userId;
  final String username;
  @JsonKey(name: 'profile_id')
  final int profileId;
  final String role;
  final String name;
  @JsonKey(name: 'hospital_id')
  final int? hospitalId;
  @JsonKey(name: 'service_id')
  final int? serviceId;
  @JsonKey(name: 'speciality_id')
  final int? specialityId;

  PTCLoginResponse({
    required this.status,
    required this.userId,
    required this.username,
    required this.profileId,
    required this.role,
    required this.name,
    this.hospitalId,
    this.serviceId,
    this.specialityId,
  });

  factory PTCLoginResponse.fromJson(Map<String, dynamic> json) =>
      _$PTCLoginResponseFromJson(json);
  Map<String, dynamic> toJson() => _$PTCLoginResponseToJson(this);
}

@JsonSerializable()
class PTCChangePasswordRequest {
  final String email;
  @JsonKey(name: 'current_password')
  final String? currentPassword;
  @JsonKey(name: 'new_password')
  final String newPassword;
  final String? token;

  PTCChangePasswordRequest({
    required this.email,
    this.currentPassword,
    required this.newPassword,
    this.token,
  });

  factory PTCChangePasswordRequest.fromJson(Map<String, dynamic> json) =>
      _$PTCChangePasswordRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PTCChangePasswordRequestToJson(this);
}

@JsonSerializable()
class PTCApiResponse {
  final String status;
  final String message;

  PTCApiResponse({required this.status, required this.message});

  factory PTCApiResponse.fromJson(Map<String, dynamic> json) =>
      _$PTCApiResponseFromJson(json);
  Map<String, dynamic> toJson() => _$PTCApiResponseToJson(this);
}

@JsonSerializable()
class PTCApiError {
  final String error;
  final String? message;

  PTCApiError({required this.error, this.message});

  factory PTCApiError.fromJson(Map<String, dynamic> json) =>
      _$PTCApiErrorFromJson(json);
  Map<String, dynamic> toJson() => _$PTCApiErrorToJson(this);
}

// Modèles pour la synchronisation
@JsonSerializable()
class PTCSyncRequest {
  final List<PTCPatientSync> patients;
  final List<PTCPregnancySync> pregnancies;
  final List<PTCAppointmentSync> appointments;

  PTCSyncRequest({
    this.patients = const [],
    this.pregnancies = const [],
    this.appointments = const [],
  });

  factory PTCSyncRequest.fromJson(Map<String, dynamic> json) =>
      _$PTCSyncRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PTCSyncRequestToJson(this);
}

@JsonSerializable()
class PTCPatientSync {
  @JsonKey(name: 'mobile_id')
  final String mobileId;
  final String firstname;
  final String lastname;
  final String tel;
  final String sexe;
  @JsonKey(name: 'birth_date')
  final String birthDate;
  final String address;
  @JsonKey(name: 'is_child')
  final bool isChild;

  PTCPatientSync({
    required this.mobileId,
    required this.firstname,
    required this.lastname,
    required this.tel,
    required this.sexe,
    required this.birthDate,
    required this.address,
    required this.isChild,
  });

  factory PTCPatientSync.fromJson(Map<String, dynamic> json) =>
      _$PTCPatientSyncFromJson(json);
  Map<String, dynamic> toJson() => _$PTCPatientSyncToJson(this);
}

@JsonSerializable()
class PTCSyncResponse {
  final String status;
  final String message;
  final List<PTCProcessedItem> processed;

  PTCSyncResponse({
    required this.status,
    required this.message,
    required this.processed,
  });

  factory PTCSyncResponse.fromJson(Map<String, dynamic> json) =>
      _$PTCSyncResponseFromJson(json);
  Map<String, dynamic> toJson() => _$PTCSyncResponseToJson(this);
}

@JsonSerializable()
class PTCProcessedItem {
  @JsonKey(name: 'mobile_id')
  final String mobileId;
  @JsonKey(name: 'server_id')
  final int serverId;
  final bool created;

  PTCProcessedItem({
    required this.mobileId,
    required this.serverId,
    required this.created,
  });

  factory PTCProcessedItem.fromJson(Map<String, dynamic> json) =>
      _$PTCProcessedItemFromJson(json);
  Map<String, dynamic> toJson() => _$PTCProcessedItemToJson(this);
}

// Énumérations
enum UserRole { administrator, agent, patient }
enum SyncStatus { pending, synced, error }
```

### 2. Service API avec Retrofit

```dart
// lib/services/ptc_api_service.dart
import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import '../models/ptc_models.dart';

part 'ptc_api_service.g.dart';

@RestApi()
abstract class PTCApiService {
  factory PTCApiService(Dio dio, {String baseUrl}) = _PTCApiService;

  // Authentification
  @POST('/api/login/')
  Future<PTCLoginResponse> login(@Body() PTCLoginRequest request);

  @POST('/api/change-password/')
  Future<PTCApiResponse> changePassword(@Body() PTCChangePasswordRequest request);

  // Données de référence
  @GET('/api/initial-data/')
  Future<PTCInitialDataResponse> getInitialData();

  @GET('/api/health-centers/')
  Future<PTCHealthCentersResponse> getHealthCenters();

  // Gestion Admin
  @POST('/api/create-health-agent/')
  Future<PTCCreateAgentResponse> createHealthAgent(
    @Header('Authorization') String token,
    @Body() PTCCreateAgentRequest request,
  );

  @POST('/api/create-health-center/')
  Future<PTCCreateCenterResponse> createHealthCenter(
    @Header('Authorization') String token,
    @Body() PTCCreateCenterRequest request,
  );

  @GET('/api/agents/')
  Future<PTCAgentsResponse> getAgents(@Header('Authorization') String token);

  // Gestion Agent
  @POST('/api/create-patient/')
  Future<PTCCreatePatientResponse> createPatient(
    @Header('Authorization') String token,
    @Body() PTCCreatePatientRequest request,
  );

  @GET('/api/patients/')
  Future<PTCPatientsResponse> getPatients(@Header('Authorization') String token);

  // Synchronisation
  @POST('/api/sync-mobile-data/')
  Future<PTCSyncResponse> syncMobileData(
    @Header('Authorization') String token,
    @Body() PTCSyncRequest request,
  );
}
```

### 3. Configuration Dio

```dart
// lib/services/dio_config.dart
import 'package:dio/dio.dart';
import 'interceptors/password_change_interceptor.dart';

class DioConfig {
  static Dio createDio({required String baseUrl}) {
    final dio = Dio(BaseOptions(
      baseUrl: baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Ajout des intercepteurs
    dio.interceptors.add(PasswordChangeInterceptor());
    dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (object) => print(object),
    ));

    return dio;
  }
}
```

## 🔐 Gestionnaire d'Authentification

### 1. Service d'Authentification

```dart
// lib/services/auth_service.dart
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user_session.dart';
import '../models/ptc_models.dart';
import 'ptc_api_service.dart';

class AuthService {
  static const _storage = FlutterSecureStorage();
  static const _sessionKey = 'user_session';
  
  final PTCApiService _apiService;

  AuthService(this._apiService);

  Future<AuthResult> authenticateWithPTCCare(String email, String password) async {
    try {
      final request = PTCLoginRequest(email: email, password: password);
      final response = await _apiService.login(request);

      // Mapper vers votre modèle utilisateur
      final userSession = UserSession(
        userId: response.userId,
        username: response.username,
        name: response.name,
        email: email,
        role: _mapPTCRoleToUserRole(response.role),
        token: 'Bearer ${response.userId}',
        profileId: response.profileId,
        hospitalId: response.hospitalId,
        serviceId: response.serviceId,
        specialityId: response.specialityId,
        isPTCUser: true,
      );

      // Sauvegarder la session
      await _saveUserSession(userSession);

      return AuthResult.success(userSession);
    } on DioException catch (e) {
      if (e.response?.statusCode == 401) {
        return AuthResult.error('Email ou mot de passe incorrect');
      } else if (e.response?.statusCode == 404) {
        return AuthResult.error('Utilisateur non trouvé');
      } else {
        return AuthResult.error('Erreur de connexion: ${e.message}');
      }
    } catch (e) {
      return AuthResult.error('Erreur inattendue: $e');
    }
  }

  Future<bool> changePassword({
    required String email,
    String? currentPassword,
    required String newPassword,
    String? token,
  }) async {
    try {
      final request = PTCChangePasswordRequest(
        email: email,
        currentPassword: currentPassword,
        newPassword: newPassword,
        token: token,
      );

      await _apiService.changePassword(request);
      return true;
    } catch (e) {
      return false;
    }
  }

  UserRole _mapPTCRoleToUserRole(String ptcRole) {
    switch (ptcRole) {
      case 'admin':
      case 'docteur':
        return UserRole.administrator;
      case 'assistant':
        return UserRole.agent;
      default:
        return UserRole.agent;
    }
  }

  Future<void> _saveUserSession(UserSession session) async {
    final sessionJson = session.toJson();
    await _storage.write(key: _sessionKey, value: jsonEncode(sessionJson));
  }

  Future<UserSession?> getCurrentUser() async {
    try {
      final sessionString = await _storage.read(key: _sessionKey);
      if (sessionString != null) {
        final sessionMap = jsonDecode(sessionString) as Map<String, dynamic>;
        return UserSession.fromJson(sessionMap);
      }
    } catch (e) {
      print('Erreur lecture session: $e');
    }
    return null;
  }

  Future<bool> isLoggedIn() async {
    final user = await getCurrentUser();
    return user != null;
  }

  Future<void> logout() async {
    await _storage.delete(key: _sessionKey);
  }
}

// Modèle de session utilisateur
class UserSession {
  final int userId;
  final String username;
  final String name;
  final String email;
  final UserRole role;
  final String token;
  final int profileId;
  final int? hospitalId;
  final int? serviceId;
  final int? specialityId;
  final bool isPTCUser;

  UserSession({
    required this.userId,
    required this.username,
    required this.name,
    required this.email,
    required this.role,
    required this.token,
    required this.profileId,
    this.hospitalId,
    this.serviceId,
    this.specialityId,
    this.isPTCUser = false,
  });

  factory UserSession.fromJson(Map<String, dynamic> json) {
    return UserSession(
      userId: json['userId'],
      username: json['username'],
      name: json['name'],
      email: json['email'],
      role: UserRole.values.firstWhere((e) => e.name == json['role']),
      token: json['token'],
      profileId: json['profileId'],
      hospitalId: json['hospitalId'],
      serviceId: json['serviceId'],
      specialityId: json['specialityId'],
      isPTCUser: json['isPTCUser'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'username': username,
      'name': name,
      'email': email,
      'role': role.name,
      'token': token,
      'profileId': profileId,
      'hospitalId': hospitalId,
      'serviceId': serviceId,
      'specialityId': specialityId,
      'isPTCUser': isPTCUser,
    };
  }
}

// Classe de résultat d'authentification
sealed class AuthResult {
  const AuthResult();
  
  factory AuthResult.success(UserSession user) = AuthSuccess;
  factory AuthResult.error(String message) = AuthError;
}

class AuthSuccess extends AuthResult {
  final UserSession user;
  const AuthSuccess(this.user);
}

class AuthError extends AuthResult {
  final String message;
  const AuthError(this.message);
}
```

### 2. Intercepteur Changement de Mot de Passe

```dart
// lib/services/interceptors/password_change_interceptor.dart
import 'package:dio/dio.dart';
import '../../events/app_events.dart';

class PasswordChangeInterceptor extends Interceptor {
  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    handler.next(response);
  }

  @override
  void onError(DioException err, ErrorInterceptorHandler handler) {
    if (err.response?.statusCode == 403) {
      try {
        final errorData = err.response?.data;
        if (errorData != null && errorData['error'] == 'password_change_required') {
          // Émettre un événement pour afficher l'écran de changement de mot de passe
          AppEvents.instance.emitPasswordChangeRequired();
        }
      } catch (e) {
        // Ignorer les erreurs de parsing
      }
    }
    handler.next(err);
  }
}

// lib/events/app_events.dart
import 'dart:async';

class AppEvents {
  static final AppEvents _instance = AppEvents._internal();
  static AppEvents get instance => _instance;
  AppEvents._internal();

  final _passwordChangeController = StreamController<void>.broadcast();
  Stream<void> get passwordChangeRequired => _passwordChangeController.stream;

  void emitPasswordChangeRequired() {
    _passwordChangeController.add(null);
  }

  void dispose() {
    _passwordChangeController.close();
  }
}
```

## 🔄 Gestionnaire de Synchronisation

### 1. Base de Données Floor

```dart
// lib/database/app_database.dart
import 'dart:async';
import 'package:floor/floor.dart';
import 'package:sqflite/sqflite.dart' as sqflite;

import 'entities/patient_entity.dart';
import 'entities/sync_queue_entity.dart';
import 'dao/patient_dao.dart';
import 'dao/sync_queue_dao.dart';

part 'app_database.g.dart';

@Database(version: 1, entities: [PatientEntity, SyncQueueEntity])
abstract class AppDatabase extends FloorDatabase {
  PatientDao get patientDao;
  SyncQueueDao get syncQueueDao;
}

// lib/database/entities/patient_entity.dart
import 'package:floor/floor.dart';

@Entity(tableName: 'patients')
class PatientEntity {
  @primaryKey
  final int? id;
  
  @ColumnInfo(name: 'mobile_id')
  final String mobileId;
  
  @ColumnInfo(name: 'server_id')
  final int? serverId;
  
  final String firstname;
  final String lastname;
  final String tel;
  final String sexe;
  
  @ColumnInfo(name: 'birth_date')
  final String birthDate;
  
  final String address;
  final String email;
  
  @ColumnInfo(name: 'is_child')
  final bool isChild;
  
  @ColumnInfo(name: 'is_pregnant')
  final bool isPregnant;
  
  @ColumnInfo(name: 'sync_status')
  final String syncStatus; // 'pending', 'synced', 'error'
  
  @ColumnInfo(name: 'needs_sync')
  final bool needsSync;
  
  @ColumnInfo(name: 'created_at')
  final DateTime createdAt;
  
  @ColumnInfo(name: 'updated_at')
  final DateTime updatedAt;

  PatientEntity({
    this.id,
    required this.mobileId,
    this.serverId,
    required this.firstname,
    required this.lastname,
    required this.tel,
    required this.sexe,
    required this.birthDate,
    required this.address,
    required this.email,
    required this.isChild,
    required this.isPregnant,
    this.syncStatus = 'pending',
    this.needsSync = true,
    required this.createdAt,
    required this.updatedAt,
  });
}

// lib/database/entities/sync_queue_entity.dart
import 'package:floor/floor.dart';

@Entity(tableName: 'sync_queue')
class SyncQueueEntity {
  @primaryKey
  final int? id;
  
  final String type; // 'patient', 'agent', 'health_center'
  
  @ColumnInfo(name: 'mobile_id')
  final String mobileId;
  
  @ColumnInfo(name: 'local_id')
  final int localId;
  
  final String data; // JSON des données à synchroniser
  
  @ColumnInfo(name: 'sync_status')
  final String syncStatus;
  
  @ColumnInfo(name: 'created_at')
  final DateTime createdAt;
  
  @ColumnInfo(name: 'retry_count')
  final int retryCount;

  SyncQueueEntity({
    this.id,
    required this.type,
    required this.mobileId,
    required this.localId,
    required this.data,
    this.syncStatus = 'pending',
    required this.createdAt,
    this.retryCount = 0,
  });
}
```

### 2. Service de Synchronisation

```dart
// lib/services/sync_service.dart
import 'dart:convert';
import 'package:uuid/uuid.dart';
import '../database/app_database.dart';
import '../models/ptc_models.dart';
import 'ptc_api_service.dart';
import 'auth_service.dart';

class SyncService {
  final PTCApiService _apiService;
  final AuthService _authService;
  final AppDatabase _database;
  final _uuid = const Uuid();

  SyncService(this._apiService, this._authService, this._database);

  Future<SyncResult> syncAllData() async {
    try {
      final user = await _authService.getCurrentUser();
      if (user?.isPTCUser != true || user?.token == null) {
        return SyncResult.error('Utilisateur PTC non connecté');
      }

      // Récupérer les données en attente de synchronisation
      final pendingPatients = await _database.patientDao.getPatientsNeedingSync();
      final pendingSyncItems = await _database.syncQueueDao.getAllPendingSync();

      if (pendingPatients.isEmpty && pendingSyncItems.isEmpty) {
        return SyncResult.success(0, 'Aucune donnée à synchroniser');
      }

      // Préparer la requête de synchronisation
      final syncRequest = PTCSyncRequest(
        patients: pendingPatients.map((patient) => PTCPatientSync(
          mobileId: patient.mobileId,
          firstname: patient.firstname,
          lastname: patient.lastname,
          tel: patient.tel,
          sexe: patient.sexe,
          birthDate: patient.birthDate,
          address: patient.address,
          isChild: patient.isChild,
        )).toList(),
      );

      // Envoyer au serveur
      final response = await _apiService.syncMobileData(user.token!, syncRequest);

      // Mettre à jour les IDs locaux avec les IDs serveur
      await _updateLocalRecordsWithServerIds(response.processed);

      return SyncResult.success(
        response.processed.length,
        'Synchronisation réussie',
      );
    } catch (e) {
      return SyncResult.error('Erreur de synchronisation: $e');
    }
  }

  Future<void> _updateLocalRecordsWithServerIds(List<PTCProcessedItem> processed) async {
    for (final item in processed) {
      if (item.mobileId.startsWith('patient_')) {
        await _database.patientDao.updateServerId(item.mobileId, item.serverId);
        await _database.patientDao.updateSyncStatus(item.mobileId, 'synced');
      }
      // Ajouter d'autres types d'entités si nécessaire
    }
  }

  // Créer un patient avec synchronisation automatique
  Future<CreateResult> createPatientWithSync(PatientCreationData patientData) async {
    try {
      final user = await _authService.getCurrentUser();
      final mobileId = _uuid.v4();
      
      // Créer l'entité patient locale
      final patient = PatientEntity(
        mobileId: mobileId,
        firstname: patientData.firstname,
        lastname: patientData.lastname,
        tel: patientData.tel,
        sexe: patientData.sexe,
        birthDate: patientData.birthDate,
        address: patientData.address,
        email: patientData.email,
        isChild: patientData.isChild,
        isPregnant: patientData.isPregnant,
        syncStatus: 'pending',
        needsSync: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      if (user?.isPTCUser == true && user?.token != null) {
        // Essayer de créer directement sur le serveur
        try {
          final ptcRequest = _mapToCreatePatientRequest(patientData);
          final response = await _apiService.createPatient(user.token!, ptcRequest);
          
          // Succès : mettre à jour avec l'ID serveur
          final updatedPatient = PatientEntity(
            id: patient.id,
            mobileId: patient.mobileId,
            serverId: response.patientId,
            firstname: patient.firstname,
            lastname: patient.lastname,
            tel: patient.tel,
            sexe: patient.sexe,
            birthDate: patient.birthDate,
            address: patient.address,
            email: patient.email,
            isChild: patient.isChild,
            isPregnant: patient.isPregnant,
            syncStatus: 'synced',
            needsSync: false,
            createdAt: patient.createdAt,
            updatedAt: DateTime.now(),
          );
          
          await _database.patientDao.insertPatient(updatedPatient);
          
          return CreateResult.success(
            updatedPatient,
            'Patient créé avec succès. Email envoyé: ${response.emailSent}',
          );
        } catch (e) {
          // Échec en ligne : sauvegarder localement pour sync ultérieure
          await _database.patientDao.insertPatient(patient);
          
          // Ajouter à la queue de synchronisation
          final syncItem = SyncQueueEntity(
            type: 'patient',
            mobileId: mobileId,
            localId: patient.id ?? 0,
            data: jsonEncode(patientData.toJson()),
            createdAt: DateTime.now(),
          );
          await _database.syncQueueDao.addToQueue(syncItem);
          
          return CreateResult.success(
            patient,
            'Patient créé localement. Sera synchronisé plus tard.',
          );
        }
      } else {
        // Utilisateur non-PTC : sauvegarder uniquement localement
        await _database.patientDao.insertPatient(patient);
        return CreateResult.success(patient, 'Patient créé localement');
      }
    } catch (e) {
      return CreateResult.error('Erreur lors de la création: $e');
    }
  }

  PTCCreatePatientRequest _mapToCreatePatientRequest(PatientCreationData data) {
    return PTCCreatePatientRequest(
      firstname: data.firstname,
      lastname: data.lastname,
      tel: data.tel,
      email: data.email,
      sexe: data.sexe,
      birthDate: data.birthDate,
      address: data.address,
      languageId: data.languageId,
      occupation: data.occupation,
      assurance: data.assurance,
      isChild: data.isChild,
      isPregnant: data.isPregnant,
      pregnancySituation: data.pregnancyInfo?.situation,
      pregnancyDescription: data.pregnancyInfo?.description,
      pregnancyTerm: data.pregnancyInfo?.term,
      pregnancyStartDate: data.pregnancyInfo?.startDate,
      studyLevel: data.studyLevel,
      husbandName: data.husbandName,
      husbandTel: data.husbandTel,
      personalHistory: data.personalHistory,
      familyHistory: data.familyHistory,
      allergies: data.allergies,
      vaccinations: data.vaccinations,
      specialMarks: data.specialMarks,
      motherId: data.motherId,
      otherInformations: data.otherInformations,
    );
  }

  // Synchronisation automatique en arrière-plan
  Future<void> scheduleAutoSync() async {
    // Utiliser WorkManager pour la synchronisation périodique
    await Workmanager().registerPeriodicTask(
      'ptc-auto-sync',
      'PTCAutoSyncTask',
      frequency: const Duration(minutes: 15),
      constraints: Constraints(
        networkType: NetworkType.connected,
        requiresBatteryNotLow: true,
      ),
    );
  }
}

// Résultats
sealed class SyncResult {
  const SyncResult();
  
  factory SyncResult.success(int syncedCount, String message) = SyncSuccess;
  factory SyncResult.error(String message) = SyncError;
}

class SyncSuccess extends SyncResult {
  final int syncedCount;
  final String message;
  const SyncSuccess(this.syncedCount, this.message);
}

class SyncError extends SyncResult {
  final String message;
  const SyncError(this.message);
}

sealed class CreateResult {
  const CreateResult();
  
  factory CreateResult.success(PatientEntity patient, String message) = CreateSuccess;
  factory CreateResult.error(String message) = CreateError;
}

class CreateSuccess extends CreateResult {
  final PatientEntity patient;
  final String message;
  const CreateSuccess(this.patient, this.message);
}

class CreateError extends CreateResult {
  final String message;
  const CreateError(this.message);
}
```

## 🖥️ Intégration UI avec Provider

### 1. Provider pour l'Authentification

```dart
// lib/providers/auth_provider.dart
import 'package:flutter/foundation.dart';
import '../services/auth_service.dart';
import '../models/user_session.dart';

class AuthProvider extends ChangeNotifier {
  final AuthService _authService;
  
  UserSession? _currentUser;
  bool _isLoading = false;
  
  AuthProvider(this._authService) {
    _loadCurrentUser();
  }

  UserSession? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  bool get isLoggedIn => _currentUser != null;

  Future<void> _loadCurrentUser() async {
    _currentUser = await _authService.getCurrentUser();
    notifyListeners();
  }

  Future<AuthResult> login(String email, String password) async {
    _isLoading = true;
    notifyListeners();

    try {
      // Essayer d'abord l'authentification PTC Care
      final ptcResult = await _authService.authenticateWithPTCCare(email, password);
      
      if (ptcResult is AuthSuccess) {
        _currentUser = ptcResult.user;
        notifyListeners();
        return ptcResult;
      } else {
        // Ici vous pouvez ajouter votre logique d'authentification locale
        // si l'utilisateur n'est pas trouvé dans PTC Care
        return ptcResult;
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> logout() async {
    await _authService.logout();
    _currentUser = null;
    notifyListeners();
  }

  Future<bool> changePassword({
    required String email,
    String? currentPassword,
    required String newPassword,
    String? token,
  }) async {
    return await _authService.changePassword(
      email: email,
      currentPassword: currentPassword,
      newPassword: newPassword,
      token: token,
    );
  }
}
```

### 2. Provider pour la Synchronisation

```dart
// lib/providers/sync_provider.dart
import 'package:flutter/foundation.dart';
import '../services/sync_service.dart';

class SyncProvider extends ChangeNotifier {
  final SyncService _syncService;
  
  bool _isSyncing = false;
  int _pendingCount = 0;
  DateTime? _lastSyncTime;
  String? _lastSyncMessage;
  
  SyncProvider(this._syncService) {
    _loadSyncStatus();
  }

  bool get isSyncing => _isSyncing;
  int get pendingCount => _pendingCount;
  DateTime? get lastSyncTime => _lastSyncTime;
  String? get lastSyncMessage => _lastSyncMessage;
  bool get syncNeeded => _pendingCount > 0;

  Future<void> _loadSyncStatus() async {
    // Charger le statut depuis SharedPreferences ou la base de données
    // _pendingCount = await _database.countRecordsNeedingSync();
    notifyListeners();
  }

  Future<SyncResult> performManualSync() async {
    _isSyncing = true;
    notifyListeners();

    try {
      final result = await _syncService.syncAllData();
      
      if (result is SyncSuccess) {
        _lastSyncTime = DateTime.now();
        _lastSyncMessage = result.message;
        _pendingCount = 0; // Réinitialiser le compteur
      } else if (result is SyncError) {
        _lastSyncMessage = result.message;
      }
      
      notifyListeners();
      return result;
    } finally {
      _isSyncing = false;
      notifyListeners();
    }
  }

  void updatePendingCount(int count) {
    _pendingCount = count;
    notifyListeners();
  }

  Future<void> startAutoSync() async {
    await _syncService.scheduleAutoSync();
  }
}
```

### 3. Écran de Connexion

```dart
// lib/screens/login_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../services/auth_service.dart';

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Connexion PTC Care')),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              TextFormField(
                controller: _emailController,
                decoration: InputDecoration(
                  labelText: 'Email ou nom d\'utilisateur',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez saisir votre email';
                  }
                  return null;
                },
              ),
              SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                decoration: InputDecoration(
                  labelText: 'Mot de passe',
                  border: OutlineInputBorder(),
                ),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Veuillez saisir votre mot de passe';
                  }
                  return null;
                },
              ),
              SizedBox(height: 24),
              Consumer<AuthProvider>(
                builder: (context, authProvider, child) {
                  return SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: authProvider.isLoading ? null : _handleLogin,
                      child: authProvider.isLoading
                          ? CircularProgressIndicator()
                          : Text('Se connecter'),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _handleLogin() async {
    if (_formKey.currentState!.validate()) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      
      final result = await authProvider.login(
        _emailController.text.trim(),
        _passwordController.text,
      );

      if (result is AuthSuccess) {
        // Navigation vers le dashboard approprié
        _navigateToDashboard(result.user.role);
      } else if (result is AuthError) {
        _showError(result.message);
      }
    }
  }

  void _navigateToDashboard(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        Navigator.pushReplacementNamed(context, '/admin-dashboard');
        break;
      case UserRole.agent:
        Navigator.pushReplacementNamed(context, '/agent-dashboard');
        break;
      case UserRole.patient:
        // Déconnexion automatique pour les patients
        Provider.of<AuthProvider>(context, listen: false).logout();
        _showError('Accès patient non autorisé sur mobile');
        break;
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
```

### 4. Dashboard avec Synchronisation

```dart
// lib/screens/dashboard_screen.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../providers/sync_provider.dart';

class DashboardScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Dashboard PTC Care'),
        actions: [
          Consumer<SyncProvider>(
            builder: (context, syncProvider, child) {
              return IconButton(
                icon: Stack(
                  children: [
                    Icon(Icons.sync),
                    if (syncProvider.syncNeeded)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          padding: EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(6),
                          ),
                          constraints: BoxConstraints(
                            minWidth: 12,
                            minHeight: 12,
                          ),
                          child: Text(
                            '${syncProvider.pendingCount}',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 8,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                  ],
                ),
                onPressed: syncProvider.isSyncing ? null : () => _performSync(context),
              );
            },
          ),
        ],
      ),
      body: Consumer2<AuthProvider, SyncProvider>(
        builder: (context, authProvider, syncProvider, child) {
          final user = authProvider.currentUser!;
          
          return Padding(
            padding: EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Informations utilisateur
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Bonjour, ${user.name}',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        SizedBox(height: 8),
                        Text('Rôle: ${_getRoleDisplay(user.role)}'),
                        if (user.isPTCUser)
                          Chip(
                            label: Text('PTC Care'),
                            backgroundColor: Colors.green.shade100,
                          ),
                      ],
                    ),
                  ),
                ),
                
                SizedBox(height: 16),
                
                // Statut de synchronisation
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              syncProvider.syncNeeded ? Icons.sync_problem : Icons.sync,
                              color: syncProvider.syncNeeded ? Colors.orange : Colors.green,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'Synchronisation',
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        if (syncProvider.syncNeeded)
                          Text('${syncProvider.pendingCount} éléments en attente'),
                        if (syncProvider.lastSyncTime != null)
                          Text('Dernière sync: ${_formatDateTime(syncProvider.lastSyncTime!)}'),
                        if (syncProvider.isSyncing)
                          LinearProgressIndicator(),
                      ],
                    ),
                  ),
                ),
                
                SizedBox(height: 16),
                
                // Boutons d'action selon le rôle
                if (user.role == UserRole.administrator) ..._buildAdminActions(context),
                if (user.role == UserRole.agent) ..._buildAgentActions(context),
              ],
            ),
          );
        },
      ),
    );
  }

  String _getRoleDisplay(UserRole role) {
    switch (role) {
      case UserRole.administrator:
        return 'Administrateur';
      case UserRole.agent:
        return 'Agent de Santé';
      case UserRole.patient:
        return 'Patient';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  List<Widget> _buildAdminActions(BuildContext context) {
    return [
      Text(
        'Actions Administrateur',
        style: Theme.of(context).textTheme.titleMedium,
      ),
      SizedBox(height: 8),
      Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              icon: Icon(Icons.group_add),
              label: Text('Créer Agent'),
              onPressed: () => Navigator.pushNamed(context, '/create-agent'),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: ElevatedButton.icon(
              icon: Icon(Icons.local_hospital),
              label: Text('Gérer Centres'),
              onPressed: () => Navigator.pushNamed(context, '/manage-centers'),
            ),
          ),
        ],
      ),
    ];
  }

  List<Widget> _buildAgentActions(BuildContext context) {
    return [
      Text(
        'Actions Agent de Santé',
        style: Theme.of(context).textTheme.titleMedium,
      ),
      SizedBox(height: 8),
      Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              icon: Icon(Icons.person_add),
              label: Text('Nouveau Patient'),
              onPressed: () => Navigator.pushNamed(context, '/create-patient'),
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: ElevatedButton.icon(
              icon: Icon(Icons.list),
              label: Text('Mes Patients'),
              onPressed: () => Navigator.pushNamed(context, '/patients-list'),
            ),
          ),
        ],
      ),
    ];
  }

  Future<void> _performSync(BuildContext context) async {
    final syncProvider = Provider.of<SyncProvider>(context, listen: false);
    final result = await syncProvider.performManualSync();

    if (result is SyncSuccess) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${result.syncedCount} éléments synchronisés'),
          backgroundColor: Colors.green,
        ),
      );
    } else if (result is SyncError) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur: ${result.message}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
```

## ⚙️ Configuration et Initialisation

### 1. Main.dart

```dart
// lib/main.dart
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:workmanager/workmanager.dart';

import 'services/dio_config.dart';
import 'services/ptc_api_service.dart';
import 'services/auth_service.dart';
import 'services/sync_service.dart';
import 'providers/auth_provider.dart';
import 'providers/sync_provider.dart';
import 'screens/login_screen.dart';
import 'screens/dashboard_screen.dart';
import 'database/app_database.dart';

// Fonction pour le background task
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    switch (task) {
      case 'PTCAutoSyncTask':
        // Logique de synchronisation en arrière-plan
        print('Exécution de la synchronisation automatique');
        // Ici vous pouvez appeler votre service de synchronisation
        break;
    }
    return Future.value(true);
  });
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Initialiser WorkManager
  await Workmanager().initialize(callbackDispatcher, isInDebugMode: true);
  
  // Initialiser la base de données
  final database = await $FloorAppDatabase.databaseBuilder('app_database.db').build();
  
  // Configuration des services
  const baseUrl = 'http://your-server.com'; // Remplacez par votre URL
  final dio = DioConfig.createDio(baseUrl: baseUrl);
  final apiService = PTCApiService(dio, baseUrl: baseUrl);
  final authService = AuthService(apiService);
  final syncService = SyncService(apiService, authService, database);

  runApp(PTCCareApp(
    authService: authService,
    syncService: syncService,
  ));
}

class PTCCareApp extends StatelessWidget {
  final AuthService authService;
  final SyncService syncService;

  const PTCCareApp({
    Key? key,
    required this.authService,
    required this.syncService,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => AuthProvider(authService)),
        ChangeNotifierProvider(create: (_) => SyncProvider(syncService)),
      ],
      child: MaterialApp(
        title: 'PTC Care Mobile',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          visualDensity: VisualDensity.adaptivePlatformDensity,
        ),
        home: AuthenticationWrapper(),
        routes: {
          '/login': (context) => LoginScreen(),
          '/admin-dashboard': (context) => DashboardScreen(),
          '/agent-dashboard': (context) => DashboardScreen(),
          // Ajoutez vos autres routes ici
        },
      ),
    );
  }
}

class AuthenticationWrapper extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (authProvider.isLoggedIn) {
          final user = authProvider.currentUser!;
          switch (user.role) {
            case UserRole.administrator:
            case UserRole.agent:
              return DashboardScreen();
            case UserRole.patient:
              // Déconnexion automatique pour les patients
              WidgetsBinding.instance.addPostFrameCallback((_) {
                authProvider.logout();
              });
              return LoginScreen();
          }
        } else {
          return LoginScreen();
        }
      },
    );
  }
}
```

## 🔧 Conseils d'Implémentation

### 1. **Génération de Code**
```bash
# Générer les modèles JSON et API
flutter packages pub run build_runner build

# En mode watch (développement)
flutter packages pub run build_runner watch
```

### 2. **Configuration de Développement**
Créez un fichier `lib/config/app_config.dart` :

```dart
class AppConfig {
  static const String baseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'http://********:8000', // Android Emulator
    // defaultValue: 'http://localhost:8000', // iOS Simulator
  );
  
  static const bool debugMode = bool.fromEnvironment('DEBUG_MODE', defaultValue: true);
}
```

### 3. **Tests**
```bash
# Lancer les tests
flutter test

# Tests d'intégration
flutter drive --target=test_driver/app.dart
```

Ce guide Flutter vous permet d'intégrer progressivement l'authentification et la synchronisation PTC Care dans votre application existante sans casser le fonctionnement actuel.

<function_calls>
<invoke name="todo_write">
<parameter name="todos">[{"id": "flutter-integration", "content": "Cr\u00e9er un guide d'int\u00e9gration Flutter pour l'authentification et synchronisation PTC Care", "status": "completed", "priority": "high"}, {"id": "adapt-dart-code", "content": "Adapter tous les exemples de code pour Flutter/Dart", "status": "completed", "priority": "high"}, {"id": "flutter-packages", "content": "Identifier les packages Flutter n\u00e9cessaires pour l'int\u00e9gration", "status": "completed", "priority": "high"}]
