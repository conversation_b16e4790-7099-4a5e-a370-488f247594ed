# Configuration d'environnement pour PTCCare
# Copiez ce fichier vers .env et remplissez les valeurs

# Django Configuration
SECRET_KEY=votre-cle-secrete-django-tres-longue-et-complexe
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1

# Base de données
DATABASE_URL=postgres://username:password@localhost:5432/ptccare
# Ou pour MySQL local:
# DATABASE_URL=mysql://root:@localhost:3306/ptccare

# Redis pour Celery
REDIS_URL=redis://localhost:6379

# Configuration Email
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=votre-mot-de-passe-application
DEFAULT_FROM_EMAIL=<EMAIL>

# Administrateur par défaut
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# Configuration Firebase (optionnel)
FIREBASE_TYPE=service_account
FIREBASE_PROJECT_ID=votre-projet-firebase
FIREBASE_PRIVATE_KEY_ID=votre-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nvotre-cle-privee\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=votre-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token
FIREBASE_AUTH_PROVIDER_X509_CERT_URL=https://www.googleapis.com/oauth2/v1/certs
FIREBASE_CLIENT_X509_CERT_URL=https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk%40votre-projet.iam.gserviceaccount.com

# Configuration SMS (optionnel)
SMS_ENABLED=False
GSM_MODEM_PORT=/dev/ttyUSB0

# Configuration de développement
CORS_ALLOW_ALL_ORIGINS=True
