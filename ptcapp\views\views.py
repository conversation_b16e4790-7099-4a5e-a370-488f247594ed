from multiprocessing import context
from django.http import HttpResponse, HttpResponseForbidden, JsonResponse
from django.shortcuts import get_object_or_404, redirect, render
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import Group, User
from django.core import serializers
from datetime import date, datetime
# Create your views here.
from ptcapp.helpers.authentification_helper import authenticated_user, go_home, is_admin, is_assistant, is_doctor, is_patient
from ptcapp.models import mother_pregnancy
from ptcapp.models import map_doctor_patient
from ptcapp.models import pregnancy
from ptcapp.models.appointment import Appointment
from ptcapp.models.appointment_file import AppointmentFile
from ptcapp.models.hospital import Hospital
from ptcapp.models.language import Language
from ptcapp.models.map_doctor_patient import MapDoctorPatient
from ptcapp.models.map_mother_child import MapMotherChild
from ptcapp.models.mother_pregnancy import MotherPregnancy
from ptcapp.models.pregnancy import Pregnancy
from ptcapp.models.profile import Profile
from ptcapp.models.profile_other_information import OtherInformation
from ptcapp.models.service import Service
from ptcapp.models.speciality import Speciality


def index(request):
    context = {}
    if request.method == "POST":
        # CAPTCHA désactivé pour le déploiement Heroku
        # if not request.POST.get('g-recaptcha-response'):
        #     context['captcha'] = "Veuillez confirmer que vous êtes pas un robot"
        # else:

        # Utilisation de l'email pour l'authentification
        email = request.POST.get('email', '')
        password = request.POST.get('password', '')

        user = authenticate(request, email=email, password=password)
        if user is not None:
            login(request, user)
            return go_home(request.user)
        else:
            context['error'] = "Adresse email ou mot de passe incorrect"
        return render(request, "auth/index.html", context)
    else:
        if request.user.is_authenticated:
            return go_home(request.user)
        else:
            return render(request, "auth/index.html", context)

def disconnect(request):
    logout(request)
    return redirect('/')

# UTIL
def getpatient(pat):
    return Profile.objects.filter(id=pat.get('patient')).filter(archived = False).first()

def getMotherPatient(pat):
    return Profile.objects.filter(id=pat.get('patient')).filter(archived = False).filter(child=False).first()

def getarchivedpatient(pat):
    return Profile.objects.filter(id=pat.get('patient')).filter(archived = True).first()

def getId(pat):
    if pat:
        return pat.pk

def idLists(pats):
    rtn = list(map(getId,pats))
    return rtn 

def getchild(child):
    return Profile.objects.filter(id=child.get('child')).first()

# PATIENT
def patientIndex(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_patient(request.user): return HttpResponseForbidden()

    context = {}
    context['title'] = "Mes dossiers"
    context['mother'] = get_object_or_404(Profile, user=request.user)
    context['mother_children'] = MapMotherChild.objects.filter(mother=context['mother'])
    return render(request, "patient/index.html", context)

def showFile(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_patient(request.user): return HttpResponseForbidden()
    context = {}
    context['title'] = "Dossier"
    context['profile'] = get_object_or_404(Profile, id=id)


    mother = get_object_or_404(Profile, user=request.user)
    childs = list(map(getchild, MapMotherChild.objects.filter(mother=mother).values('child')))

    if(int(id)!=mother.id and int(id) not in idLists(childs)):
        return HttpResponseForbidden()

    context['appointments'] = Appointment.objects.filter(patient=context['profile'])
    return render(request, "patient/show_file.html", context )

def showAppointment(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_patient(request.user): return HttpResponseForbidden()
    context = {}
    context['title'] = "Mes dossiers"

    mother = get_object_or_404(Profile, user=request.user)
    p_lists = list(map(getchild, MapMotherChild.objects.filter(mother=mother).values('child')))
    p_lists.append(mother)
    context['appointment'] = Appointment.objects.filter(id=id, patient__in=idLists(p_lists)).first()

    if(not context['appointment']):
        return HttpResponseForbidden()

    context['files'] = AppointmentFile.objects.filter(appointment=context['appointment'])
    print(context['appointment'])
    return render(request, "patient/show_appointment.html", context)

def profile(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_patient(request.user): return HttpResponseForbidden()
    context = {}
    context['title'] = "Profil"
    context['profile'] = get_object_or_404(Profile, user=request.user)

    return render(request, "patient/profile.html", context)

def information(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_patient(request.user): return HttpResponseForbidden()
    context = {}
    context['title'] = "Renseignements"

    profile = get_object_or_404(Profile, user=request.user)
    context['doc_pat'] = MapDoctorPatient.objects.filter(patient=profile).last()
    return render(request, "patient/information.html", context)
    
# ADMIN
def adminIndex(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = 'Liste du personnel'
    context['initpersonnels'] = Profile.objects.all()
    context['personnels'] = Profile.objects.all().exclude(user__groups__name='patient')
    context['groups'] = Group.objects.all()
    if request.session.get('new_credential') != None:
        context['new_credential'] = request.session['new_credential']
        del request.session['new_credential']
    return render(request, "direction/index.html", context)

def patients(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = 'Liste des patients'
    context['initpersonnels'] = Profile.objects.all()
    context['patients'] = Profile.objects.all().filter(user__groups__name='patient')
    context['groups'] = Group.objects.all()
    if request.session.get('new_credential') != None:
        context['new_credential'] = request.session['new_credential']
        del request.session['new_credential']
    return render(request, "direction/indexPatient.html", context)

def createPersonnel(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    context= {}
    context['title'] = 'Créer Personnel'
    context['groups'] = Group.objects.all()
    context['hospitals'] = Hospital.objects.all()
    context['services'] = Service.objects.all()
    context['specialities'] = Speciality.objects.all()
    return render(request, "direction/personnel/create.html", context)

def editPersonnel(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    context={}

    profile = get_object_or_404(Profile, id = id)
    context["title"] = "Personnel"
    context["personnel"] = profile
    context['groups'] = Group.objects.all()
    context['hospitals'] = Hospital.objects.all()
    context['services'] = Service.objects.all()
    context['specialities'] = Speciality.objects.all()
    return render(request, "direction/personnel/edit.html", context)

def showPersonnel(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    context={}
    profile = get_object_or_404(Profile, id = id)
    context["profile"] = profile
    context["assistants"] = Profile.objects.all()
    return render(request, "direction/personnel/show.html", context)

def adminShowRecord(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    context={}
    profile = get_object_or_404(Profile, id = id)
    context["docteurs"] = Profile.objects.filter(user__groups__name='docteur')
    context["profile"] = profile
    return render(request, "direction/show_record.html", context)

def adminCreatePatient(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    context = {}
    context["title"] = "Patient"
    context["languages"] = Language.objects.all()
    context["docteurs"] = Profile.objects.filter(user__groups__name='docteur')
    context["mothers"] = Profile.objects.filter(user__groups__name='patient', child=False)
    return render(request, "direction/patient/create.html", context)

def adminEditPatient(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    context = {}
    context["title"] = "Modifier Patient"
    context["languages"] = Language.objects.all()
    context["docteurs"] = Profile.objects.filter(user__groups__name='docteur')
    context["mothers"] = Profile.objects.filter(user__groups__name='patient', child=False)
    patient = context["patient"] = get_object_or_404(Profile, id=id, user__groups__name='patient')
    context["doc_pat"] = get_object_or_404(MapDoctorPatient, patient=patient)
    if patient.child:
        context["mot_child"] = get_object_or_404(MapMotherChild, child=patient)
    else:
        pregnancy = context["pregnancy"] = Pregnancy.objects.filter(motherpregnancy__mother=patient, state=True).first()
    context["other_informations"] = OtherInformation.objects.filter(profile=patient)
    return render(request, "direction/patient/edit.html", context)


def indexFilters(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = 'Liste du personnel'
    context['groups'] = Group.objects.all()
    context['initpersonnels'] = Profile.objects.all()

    personnels = Profile.objects.all()
    if request.GET["created_by"] != "all":
        try:
            user=User.objects.filter(id=request.GET["created_by"]).get()
            personnels = personnels.filter(created_by=user)
        except:
            context['personnels'] = None
            return render(request, "direction/index.html", context)
    
    if request.GET["fullname"]:
        personnels = personnels.filter(firstname__icontains=request.GET["fullname"]) | personnels.filter(lastname__icontains=request.GET["fullname"])

    if request.GET["start"] and request.GET["end"]:
        personnels = personnels.filter(created_at__range=[request.GET["start"], request.GET["end"]])
    
    context['personnels'] = personnels
    return render(request, "direction/index.html", context)

def hospital(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    hospitals = Hospital.objects.all()
    return render(request, "direction/indexHopital.html", {"title":"Hôpital", "hospitals":hospitals})

def service(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    services = Service.objects.all()
    hospitals = Hospital.objects.all()
    return render(request, "direction/indexServices.html", {"title":"Services", "services":services, "hospitals":hospitals})

def language(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    languages = Language.objects.all()
    return render(request, "direction/indexLanguage.html", {"title":"Langues", "languages":languages})

def speciality(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    specialities = Speciality.objects.all()
    return render(request, "direction/indexSpeciality.html", {"title":"Spécialités", "specialities":specialities})

def adminProfile(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    try: 
        profile = Profile.objects.filter(user=request.user).get()
    except:
        profile = None
    return render(request, "direction/profile.html", {"title":"Profil", "profile":profile})

#DOCTOR
def doctorIndex(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = "Patients suivis"
    context['patients'] = list(map(getpatient, MapDoctorPatient.objects.filter(doctor__user=request.user).values('patient')))
    context['docteurs'] = Profile.objects.filter(user__groups__name='docteur')
    if request.session.get('new_credential') != None:
        context['new_credential'] = request.session['new_credential']
        del request.session['new_credential']
    return render(request, "doctor/patients.html", context)
    # return render(request, "doctor/index.html", {"title":"Dossiers"})

def doctorArchives(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context= {}
    context['title'] = "Patients archivés"
    context['patients'] = list(map(getarchivedpatient, MapDoctorPatient.objects.filter(doctor__user=request.user).values('patient')))
    return render(request, "doctor/archives.html", context)

def doctorPregnancies(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context= {}
    context['title'] = "Grossesses"
    mothers = context['patients'] = list(map(getMotherPatient, MapDoctorPatient.objects.filter(doctor__user=request.user).values('patient')))
    mot_preg = context['moth_pregs'] = MotherPregnancy.objects.filter(mother__pk__in=idLists(mothers))
    return render(request, "doctor/pregnancy/index.html", context)
    
def attached_doctor(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user) and not is_assistant(request.user): return HttpResponseForbidden()
    if request.method == 'POST':
        doctor = get_object_or_404(Profile, id=request.POST['doctor'])
        patient = get_object_or_404(Profile, id=request.POST['patient'])
        pat_doc = MapDoctorPatient.objects.filter(doctor=doctor, patient=patient)
        if not pat_doc:
            doc_pat = MapDoctorPatient()
            doc_pat.doctor = doctor
            doc_pat.patient = patient
            doc_pat.save()
            return JsonResponse({'profile':int(doc_pat.id), 'success' : True})
    return JsonResponse({'success' : False})


def doctorPatients(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    return render(request, "doctor/patients.html", {"title":"Patients suivis"})

def doctorShowRecord(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context={}
    profile = get_object_or_404(Profile, id = id)
    context['docteurs'] = Profile.objects.filter(user__groups__name='docteur')
    context["profile"] = profile
    context["appointments"] = Appointment.objects.filter(patient=profile)
    return render(request, "doctor/show_record.html", context)

def doctorCreatePatient(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context = {}
    context["title"] = " Créer Patient"
    context["languages"] = Language.objects.all()
    context["docteurs"] = Profile.objects.filter(user__groups__name='docteur')
    context["mothers"] = Profile.objects.filter(user__groups__name='patient', child=False)
    return render(request, "doctor/patient/create.html", context)

def doctorEditPatient(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context = {}
    context["title"] = "Modifier Patient"
    context["languages"] = Language.objects.all()
    context["docteurs"] = Profile.objects.filter(user__groups__name='docteur')
    context["mothers"] = Profile.objects.filter(user__groups__name='patient', child=False)
    patient = context["patient"] = get_object_or_404(Profile, id=id, user__groups__name='patient')
    context["doc_pat"] = get_object_or_404(MapDoctorPatient, patient=patient, doctor=get_object_or_404(Profile, user=request.user))
    if patient.child:
        context["mot_child"] = get_object_or_404(MapMotherChild, child=patient)
    else:
        pregnancy = context["pregnancy"] = Pregnancy.objects.filter(motherpregnancy__mother=patient, state=True).first()
    context["other_informations"] = OtherInformation.objects.filter(profile=patient)
    return render(request, "doctor/patient/edit.html", context)

def doctorAppointmentIndex(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context= {}
    context['title'] = "Patients suivis"
    context['date'] = date.today()
    patients = context['patients'] = list(map(getpatient, MapDoctorPatient.objects.filter(doctor__user=request.user).values('patient')))
    context['appointments'] = Appointment.objects.filter(patient__pk__in=idLists(patients), doctor__user=request.user)
    return render(request, "doctor/appointment/index.html", context)

def doctorAppointmentEdit(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context= {}
    context['title'] = "Modifier consultation"
    context['date'] = date.today()
    patients = context['patients'] = list(map(getpatient, MapDoctorPatient.objects.filter(doctor__user=request.user).values('patient')))
    context['appointment'] = get_object_or_404(Appointment, id=id)
    return render(request, "doctor/appointment/edit.html", context)

def doctorPregnancyEdit(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context= {}
    context['title'] = "Modifier grossesse"
    
    context['pregnancy'] = get_object_or_404(Pregnancy, id=id)
    context["childs"] = Profile.objects.filter(user__groups__name='patient', child=True)
    return render(request, "doctor/pregnancy/edit.html", context)
    

def doctorAppointmentShow(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context= {}
    context['title'] = "Modifier consultation"
    context['date'] = date.today()
    patients = context['patients'] = list(map(getpatient, MapDoctorPatient.objects.filter(doctor__user=request.user).values('patient')))
    context['appointment'] = get_object_or_404(Appointment, id=id)
    context['files'] = AppointmentFile.objects.filter(appointment=context['appointment'])
    return render(request, "doctor/appointment/show.html", context)


def doctorProfile(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    try: 
        profile = Profile.objects.filter(user=request.user).get()
    except:
        profile = None
    return render(request, "doctor/profile.html", {"title":"Profil", "profile":profile})

def doctorFilters(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = 'Liste des patients'
    context['groups'] = Group.objects.all()
    context['initpatients'] = Profile.objects.filter(user__groups__name='patient')

    patients = Profile.objects.filter(user__groups__name='patient', archived = False)
    if request.GET["fullname"]:
        patients = patients.filter(firstname__icontains=request.GET["fullname"]) | patients.filter(lastname__icontains=request.GET["fullname"])
    if request.GET["last_appointment"]:
        entry_list = list(patients)
        for patient in patients:    
            
            consul = Appointment.objects.filter(patient=patient, state="done").last()
            
            if(consul):
                consul_date=consul.consul_date.strftime('%Y-%m-%d')
                if (consul_date != request.GET["last_appointment"]):                    
                    entry_list.remove(patient)
            else:
                entry_list.remove(patient)
        patients = entry_list
    if request.GET["next_appointment"]:
        entry_list = list(patients)
        for patient in patients:    
            
            consul = Appointment.objects.filter(patient=patient, state="pending").first()
            
            if(consul):
                consul_date=consul.consul_date.strftime('%Y-%m-%d')
                if (consul_date != request.GET["next_appointment"]):                    
                    entry_list.remove(patient)
            else:
                entry_list.remove(patient)
        patients = entry_list            
        
    # if request.GET["last_appointment"]:
    #     patients = patients.filter()
    context['patients'] = patients
    return render(request, "doctor/patients.html", context)

def adminFilters(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = 'Liste des patients'
    context['initpatients'] = Profile.objects.filter(user__groups__name='patient')

    patients = Profile.objects.filter(user__groups__name='patient')
    if request.GET["fullname"]:
        patients = patients.filter(firstname__icontains=request.GET["fullname"]) | patients.filter(lastname__icontains=request.GET["fullname"])
    if request.GET["last_appointment"]:
        entry_list = list(patients)
        for patient in patients:    
            print(request.GET["last_appointment"])
            consul = Appointment.objects.filter(patient=patient, state="done").last()
            print(consul)
            if(consul):
                consul_date=consul.consul_date.strftime('%Y-%m-%d')
                if (consul_date != request.GET["last_appointment"]):                    
                    entry_list.remove(patient)
            else:
                entry_list.remove(patient)
        patients = entry_list
    if request.GET["next_appointment"]:
        entry_list = list(patients)
        for patient in patients:    
            print(request.GET["next_appointment"])
            consul = Appointment.objects.filter(patient=patient, state="pending").first()
            print(consul)
            if(consul):
                consul_date=consul.consul_date.strftime('%Y-%m-%d')
                if (consul_date != request.GET["next_appointment"]):                    
                    entry_list.remove(patient)
            else:
                entry_list.remove(patient)
        patients = entry_list            
    context['patients'] = patients
    return render(request, "direction/indexPatient.html", context)

def doctorAppointmentFilters(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = 'Liste des consultations'
    context['initpatients'] = Profile.objects.filter(user__groups__name='patient')
    patients = context['patients'] = list(map(getpatient, MapDoctorPatient.objects.filter(doctor__user=request.user).values('patient')))
    appointments = context['appointments'] = Appointment.objects.filter(patient__pk__in=idLists(patients))
    if request.GET.get("fullname", None):
        appointments = appointments.filter(patient__firstname__icontains=request.GET["fullname"]) | appointments.filter(patient__lastname__icontains=request.GET["fullname"])
    if request.GET.get("statut", None):
        appointments = appointments.filter(state = request.GET["statut"])
    if request.GET.get("date_appointment", None):
        appointments = appointments.filter(consul_date = request.GET["date_appointment"])
    context['appointments'] = appointments
    return render(request, "doctor/appointment/index.html", context)

def doctorPregnancyFilters(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = 'Liste des grossesses'
    mothers = context['patients'] = list(map(getMotherPatient, MapDoctorPatient.objects.filter(doctor__user=request.user).values('patient')))
    mot_preg = context['moth_pregs'] = MotherPregnancy.objects.filter(mother__pk__in=idLists(mothers))
    if request.GET.get("fullname", None):
        mot_preg = mot_preg.filter(mother__firstname__icontains=request.GET["fullname"]) | mot_preg.filter(mother__lastname__icontains=request.GET["fullname"])
    if request.GET.get("statut", None):
        mot_preg = mot_preg.filter(pregnancy__state = request.GET["statut"])
    if request.GET.get("start_date", None):
        mot_preg = mot_preg.filter(pregnancy__start_date = request.GET["start_date"])
    
    context['moth_pregs'] = mot_preg
    return render(request, "doctor/pregnancy/index.html", context)


def doctorArchiveFilters(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = 'Liste des patients archivés'
    context['initpatients'] = Profile.objects.filter(user__groups__name='patient')

    patients = Profile.objects.filter(user__groups__name='patient', archived = True)
    if request.GET.get("fullname"):
        patients = patients.filter(firstname__icontains=request.GET["fullname"]) | patients.filter(lastname__icontains=request.GET["fullname"])
    if request.GET.get("last_appointment"):
        entry_list = list(patients)
        for patient in patients:    
            print(request.GET["last_appointment"])
            consul = Appointment.objects.filter(patient=patient, state="done").last()
            print(consul)
            if(consul):
                consul_date=consul.consul_date.strftime('%Y-%m-%d')
                if (consul_date != request.GET["last_appointment"]):                    
                    entry_list.remove(patient)
            else:
                entry_list.remove(patient)
        patients = entry_list
    if request.GET["next_appointment"]:
        entry_list = list(patients)
        for patient in patients:    
            print(request.GET["next_appointment"])
            consul = Appointment.objects.filter(patient=patient, state="pending").first()
            print(consul)
            if(consul):
                consul_date=consul.consul_date.strftime('%Y-%m-%d')
                if (consul_date != request.GET["next_appointment"]):                    
                    entry_list.remove(patient)
            else:
                entry_list.remove(patient)
        patients = entry_list            
    context['patients'] = patients
    return render(request, "doctor/archives.html", context)



# ASSISTANT
def assistantIndex(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = "Patients suivis"
    docs = context['docs'] = Profile.objects.filter(user__groups__name='docteur', assistant = request.user)
    patients = list()
    for docteur in docs:
        pats = context['patients'] = list(map(getpatient, MapDoctorPatient.objects.filter(doctor__user=docteur.user).values('patient')))
        patients.extend(pats)
        
    context['patients'] = patients
    context['docteurs'] = Profile.objects.filter(user__groups__name='docteur')
    if request.session.get('new_credential') != None:
        context['new_credential'] = request.session['new_credential']
        del request.session['new_credential']
    return render(request, "assistance/index.html", context)

def assistantFilters(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = 'Liste des patients'
    context['groups'] = Group.objects.all()
    context['initpatients'] = Profile.objects.filter(user__groups__name='patient')

    patients = Profile.objects.filter(user__groups__name='patient', archived = False)
    if request.GET["fullname"]:
        patients = patients.filter(firstname__icontains=request.GET["fullname"]) | patients.filter(lastname__icontains=request.GET["fullname"])
    if request.GET["last_appointment"]:
        entry_list = list(patients)
        for patient in patients:    
            
            consul = Appointment.objects.filter(patient=patient, state="done").last()
            
            if(consul):
                consul_date=consul.consul_date.strftime('%Y-%m-%d')
                if (consul_date != request.GET["last_appointment"]):                    
                    entry_list.remove(patient)
            else:
                entry_list.remove(patient)
        patients = entry_list
    if request.GET["next_appointment"]:
        entry_list = list(patients)
        for patient in patients:    
            
            consul = Appointment.objects.filter(patient=patient, state="pending").first()
            
            if(consul):
                consul_date=consul.consul_date.strftime('%Y-%m-%d')
                if (consul_date != request.GET["next_appointment"]):                    
                    entry_list.remove(patient)
            else:
                entry_list.remove(patient)
        patients = entry_list            
        
    # if request.GET["last_appointment"]:
    #     patients = patients.filter()
    context['patients'] = patients
    return render(request, "assistance/patients.html", context)

def assistantArchiveFilters(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = 'Liste des patients archivés'
    context['initpatients'] = Profile.objects.filter(user__groups__name='patient')

    patients = Profile.objects.filter(user__groups__name='patient', archived = True)
    if request.GET.get("fullname"):
        patients = patients.filter(firstname__icontains=request.GET["fullname"]) | patients.filter(lastname__icontains=request.GET["fullname"])
    if request.GET.get("last_appointment"):
        entry_list = list(patients)
        for patient in patients:    
            print(request.GET["last_appointment"])
            consul = Appointment.objects.filter(patient=patient, state="done").last()
            print(consul)
            if(consul):
                consul_date=consul.consul_date.strftime('%Y-%m-%d')
                if (consul_date != request.GET["last_appointment"]):                    
                    entry_list.remove(patient)
            else:
                entry_list.remove(patient)
        patients = entry_list
    if request.GET["next_appointment"]:
        entry_list = list(patients)
        for patient in patients:    
            print(request.GET["next_appointment"])
            consul = Appointment.objects.filter(patient=patient, state="pending").first()
            print(consul)
            if(consul):
                consul_date=consul.consul_date.strftime('%Y-%m-%d')
                if (consul_date != request.GET["next_appointment"]):                    
                    entry_list.remove(patient)
            else:
                entry_list.remove(patient)
        patients = entry_list            
    context['patients'] = patients
    return render(request, "assistance/archives.html", context)

def assistantAppointmentFilters(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = 'Liste des consultations'
    context['initpatients'] = Profile.objects.filter(user__groups__name='patient')
    patients = context['patients'] = list(map(getpatient, MapDoctorPatient.objects.filter(doctor__user=request.user).values('patient')))
    appointments = context['appointments'] = Appointment.objects.filter(patient__pk__in=idLists(patients))
    if request.GET.get("fullname", None):
        appointments = appointments.filter(patient__firstname__icontains=request.GET["fullname"]) | appointments.filter(patient__lastname__icontains=request.GET["fullname"])
    if request.GET.get("statut", None):
        appointments = appointments.filter(state = request.GET["statut"])
    if request.GET.get("date_appointment", None):
        appointments = appointments.filter(consul_date = request.GET["date_appointment"])
    context['appointments'] = appointments
    return render(request, "assistance/appointment/index.html", context)

def assistantPregnancyFilters(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context= {}
    context['new_credential'] = None
    context['title'] = 'Liste des grossesses'
    mothers = context['patients'] = list(map(getMotherPatient, MapDoctorPatient.objects.filter(doctor__user=request.user).values('patient')))
    mot_preg = context['moth_pregs'] = MotherPregnancy.objects.filter(mother__pk__in=idLists(mothers))
    if request.GET.get("fullname", None):
        mot_preg = mot_preg.filter(mother__firstname__icontains=request.GET["fullname"]) | mot_preg.filter(mother__lastname__icontains=request.GET["fullname"])
    if request.GET.get("statut", None):
        mot_preg = mot_preg.filter(pregnancy__state = request.GET["statut"])
    if request.GET.get("start_date", None):
        mot_preg = mot_preg.filter(pregnancy__start_date = request.GET["start_date"])
    
    context['moth_pregs'] = mot_preg
    return render(request, "assistance/pregnancy/index.html", context)

def assistantShowRecord(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context={}
    profile = get_object_or_404(Profile, id = id)
    context['docteurs'] = Profile.objects.filter(user__groups__name='docteur')
    context["profile"] = profile
    context["appointments"] = Appointment.objects.filter(patient=profile)
    return render(request, "assistance/show_record.html", context)

def assistantCreatePatient(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context = {}
    context["title"] = " Créer Patient"
    context["languages"] = Language.objects.all()
    context["docteurs"] = Profile.objects.filter(user__groups__name='docteur')
    context["mothers"] = Profile.objects.filter(user__groups__name='patient', child=False, archived = False)
    return render(request, "assistance/patient/create.html", context)

def assistantEditPatient(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context = {}
    context["title"] = "Modifier Patient"
    context["languages"] = Language.objects.all()
    context["docteurs"] = Profile.objects.filter(user__groups__name='docteur')
    context["mothers"] = Profile.objects.filter(user__groups__name='patient', child=False)
    patient = context["patient"] = get_object_or_404(Profile, id=id, user__groups__name='patient')
    context["doc_pat"] = get_object_or_404(MapDoctorPatient, patient=patient, doctor=get_object_or_404(Profile, user=request.user))
    if patient.child:
        context["mot_child"] = get_object_or_404(MapMotherChild, child=patient)
    else:
        pregnancy = context["pregnancy"] = Pregnancy.objects.filter(motherpregnancy__mother=patient, state=True).first()
    context["other_informations"] = OtherInformation.objects.filter(profile=patient)
    return render(request, "assistance/patient/edit.html", context)

    
def assistantAppointmentIndex(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context= {}
    context['title'] = "Patients suivis"
    context['date'] = date.today()

    docs = context['docs'] = Profile.objects.filter(user__groups__name='docteur', assistant = request.user)
    appointments = list()
    patients = list()
    for docteur in docs:
        pats = context['patients'] = list(map(getpatient, MapDoctorPatient.objects.filter(doctor__user=docteur.user).values('patient')))
        apps = Appointment.objects.filter(patient__pk__in=idLists(pats))
        appointments.extend(apps)
        patients.extend(pats)
    context['patients'] = patients
    context['appointments'] = appointments
    return render(request, "assistance/appointment/index.html", context)

def assistantAppointmentEdit(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context= {}
    context['title'] = "Modifier consultation"
    context['date'] = date.today()
    patients = list()
    docs = context['doctors']= Profile.objects.filter(user__groups__name='docteur', assistant = request.user)
    for docteur in docs:
        pats = context['patients'] = list(map(getpatient, MapDoctorPatient.objects.filter(doctor__user=docteur.user).values('patient')))
        patients.extend(pats)
        
    context['patients'] = patients
    context['appointment'] = get_object_or_404(Appointment, id=id)
    return render(request, "assistance/appointment/edit.html", context)

def assistantPregnancyEdit(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context= {}
    context['title'] = "Modifier grossesse"
    
    context['pregnancy'] = get_object_or_404(Pregnancy, id=id)
    context["childs"] = Profile.objects.filter(user__groups__name='patient', child=True)
    return render(request, "assistance/pregnancy/edit.html", context)

def assistantProfile(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    try: 
        profile = Profile.objects.filter(user=request.user).get()
    except:
        profile = None
    return render(request, "assistance/profile.html", {"title":"Profil", "profile":profile})

def assistantAppointmentShow(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context= {}
    context['title'] = "Modifier consultation"
    context['date'] = date.today()
    patients = context['patients'] = list(map(getpatient, MapDoctorPatient.objects.filter(doctor__user=request.user).values('patient')))
    context['appointment'] = get_object_or_404(Appointment, id=id)
    return render(request, "assistance/appointment/show.html", context)

def assistantArchives(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context= {}
    context['title'] = "Patients archivés"
    docs = context['docs'] = Profile.objects.filter(user__groups__name='docteur', assistant = request.user)
    patients = list()
    for docteur in docs:
        pats = context['patients'] = list(map(getarchivedpatient, MapDoctorPatient.objects.filter(doctor__user=docteur.user).values('patient')))
        patients.extend(pats)
        
    context['patients'] = patients
    return render(request, "assistance/archives.html", context)

def assistantPregnancies(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_assistant(request.user): return HttpResponseForbidden()
    context= {}
    context['title'] = "Grossesses"
    docs = context['docs'] = Profile.objects.filter(user__groups__name='docteur', assistant = request.user)
    mothers = list()
    patients = list()
    for docteur in docs:
        pats = context['patients'] = list(map(getMotherPatient, MapDoctorPatient.objects.filter(doctor__user=docteur.user).values('patient')))
        moths = MotherPregnancy.objects.filter(mother__pk__in=idLists(pats))
        mothers.extend(moths)
        patients.extend(pats)
    context['patients'] = patients
    mot_preg = context['moth_pregs'] = mothers
    return render(request, "assistance/pregnancy/index.html", context)


