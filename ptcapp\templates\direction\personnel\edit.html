{% extends 'direction/layout.html' %} 
{% load static %}
{% load layout %}

{% block up-style %}
    <!-- DataTables -->
    <link href="{% static 'libs/select2/css/select2.min.css' %}" rel="stylesheet" type="text/css" />
    <style>
        fieldset{
            border: 1px solid !important;
            padding: 10px !important;
        }
        fieldset div label {
            width: 50% !important;
        }
        legend{
            padding-right: 10px !important;
            padding-left: 10px !important;
            display: inline-block;
            position: relative;
            bottom: 30px;
            background-color: white;
            width: fit-content !important;
        }

        label.required::after{
            content: "*";
            color: red;
            padding-left: 3px
        }
    </style>
{% endblock up-style %}

{% block content %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white text-left">
                    <span class="h2 ">Modification du compte de l'utilisateur {{personnel.user.username}}</span>
                </div>
                <div class="card-body">
                    <form action="{% url 'profile.update' id=personnel.id %}" method="POST">
                        {% csrf_token %}
                        <div class="row">
                            <div class="d-flex  justify-content-end">
                                {% for group in groups %}
                                    {% if group.name != "patient" %}
                                        <div class="form-check mx-2">
                                            <label for="lastname" class="form-check-label required text-capitalize">{{group.name}}</label>
                                            <input class="form-check-input" type="radio" name="group" value="{{group.id}}" id="{{group.name}}-radio" {% if group.name == personnel.user|user_group %}checked{% endif %}>
                                        </div>
                                    {% endif %}
                                {% endfor %}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-12 mt-4">
                                <fieldset class="h-100">
                                    <legend>Informations Générales</legend>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="lastname" class="form-label required">Nom</label>
                                        <input class="form-control" type="text" name="lastname" placeholder="Nom" value="{{personnel.lastname}}" id="lastname" required="required">
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="firstname" class="form-label required">Prénom</label>
                                        <input class="form-control" type="text" name="firstname" placeholder="Prénom(s)" value="{{personnel.firstname}}" id="firstname" required="required">
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="sexe" class="form-label required">Sexe</label>
                                        <div class="form-check mx-2">
                                            <label for="masculin" class="form-check-label w-100">Masculin</label>
                                            <input class="form-check-input" type="radio" name="sexe" value="MAN" id="masculin" checked>
                                        </div>
                                        <div class="form-check mx-2">
                                            <label for="feminin" class="form-check-label w-100">Féminin</label>
                                            <input class="form-check-input" type="radio" name="sexe" value="WOMAN" id="feminin" {% if personnel.sexe == "WOMAN" %}checked{% endif %}>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="address" class="form-label required">Adresse</label>
                                        <input class="form-control" type="text" name="address" placeholder="Adresse" id="address" value="{{personnel.address}}" required="required">
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="mobile" class="form-label required">Mobile</label>
                                        <input class="form-control" type="tel" name="tel" placeholder="Mobile" id="mobile" value="{{personnel.tel}}" required="required">
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="email" class="form-label required">Adresse email</label>
                                        <input class="form-control" type="email" name="email" placeholder="Email" id="email" value="{{personnel.user.email}}" required="required">
                                    </div>
                                </fieldset>
                            </div>
                            <div class="col-md-6 col-sm-12 mt-4">
                                <fieldset>
                                    <legend>Informations Spécifiques</legend>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="hospital" class="form-label">Hôpital</label>
                                        <select class="form-control select2" name="hospital" id="hospital" >
                                            <option value="" class="none">---</option>
                                            {% for hospital in hospitals %}
                                                <option value="{{hospital.pk}}" {% if personnel.hospital_id == hospital.pk %}selected{% endif %}>{{hospital.name}}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="service" class="form-label">Service</label>
                                        <select class="form-control select2" name="service" id="service" >
                                            <option value="" class="none">---</option>
                                            {% for service in services %}
                                                <option value="{{service.pk}}" data-hospital-id="{{service.hospital.pk}}" {% if personnel.hospital_id != service.hospital.pk %}disabled{% endif %} {% if personnel.service_id == service.pk %}selected{% endif %}>{{service.name}}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="speciality" class="form-label">Spécialité</label>
                                        <select class="form-control select2" name="speciality" id="speciality" >
                                            <option value="" class="none">---</option>
                                            {% for speciality in specialities %}
                                                <option value="{{speciality.pk}}" {% if personnel.speciality_id == speciality.pk %}selected{% endif %}>{{speciality.name}}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </fieldset>
                                <fieldset class="mt-4">
                                    <legend>Informations Connexions</legend>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="identifiant" class="form-label">Modifier information de connexion ?</label>
                                        <input type="checkbox" name="modify_id_password" id="switch4" switch="primary" />
                                        <label for="switch4" style="width: 56px !important;" data-on-label="Oui" data-off-label="Non"></label>
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="identifiant" class="form-label">Identifiant</label>
                                        <input class="form-control" type="tel" name="text" value="XXX-XXXXXXXX" id="identifiant" disabled>
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="password" class="form-label">Mot de passe</label>
                                        <input class="form-control" type="passsword" name="passsword" value="*********" id="passsword" disabled>
                                    </div>
                                </fieldset>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col">
                                <input class="btn btn-success w-auto" type="submit" value="Mettre à jour">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}

{% block down-script %}
    <script src="{% static 'libs/select2/js/select2.min.js'%}"></script>
    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        $('#admin-radio').on('change', function (e){
            if($(this)[0].checked){
                $('#hospital').attr('required', false)
                $('label[for="hospital"').removeClass('required')
                $('#service').attr('required', false)
                $('label[for="service"').removeClass('required')
                $('#speciality').attr('required', false)
                $('label[for="speciality"').removeClass('required')
            }
        })
        $('#docteur-radio').on('change', function (e){
            if($(this)[0].checked){
                $('#hospital').attr('required', "required")
                $('label[for="hospital"').addClass('required')
                $('#service').attr('required', "required")
                $('label[for="service"').addClass('required')
                $('#speciality').attr('required', "required")
                $('label[for="speciality"').addClass('required')
            }
        })
        $('#admin-radio').on('change', function (e){
            if($(this)[0].checked){
                $('#hospital').attr('required', false)
                $('label[for="hospital"').removeClass('required')
                $('#service').attr('required', false)
                $('label[for="service"').removeClass('required')
                $('#speciality').attr('required', false)
                $('label[for="speciality"').removeClass('required')
            }
        })
        $('.select2').select2()

        $("#hospital").on('change', function(e){
            const id = $(this).val()
            
            $("#service").val("").trigger('change')

            $("#service option").each(function(o){
                if ($(this).prop("value") != "none"){
                    $(this).prop("disabled", "disabled");
                }
            })
            $("#service option[data-hospital-id='" + id + "']").prop("disabled", "");

        })
    </script>
{% endblock down-script %}