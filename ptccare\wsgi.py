"""
WSGI config for ptccare project.

It exposes the WSGI callable as a module-level variable named ``application``.

For more information on this file, see
https://docs.djangoproject.com/en/4.0/howto/deployment/wsgi/
"""

import os

from django.core.wsgi import get_wsgi_application

# Utiliser les paramètres de production sur Heroku
if 'DYNO' in os.environ or 'HEROKU_APP_NAME' in os.environ or os.environ.get('DJANGO_SETTINGS_MODULE') == 'ptccare.settings_production':
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ptccare.settings_production')
else:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ptccare.settings')

application = get_wsgi_application()
