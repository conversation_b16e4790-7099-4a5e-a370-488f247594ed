from importlib.resources import path
from operator import mod
from django.db import models
from .appointment import Appointment


class AppointmentFile(models.Model):
    title = models.CharField(max_length=50)
    path = models.CharField(max_length=100)
    appointment = models.ForeignKey(Appointment, related_name='appointment_file', null = True, on_delete=models.SET_NULL)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)