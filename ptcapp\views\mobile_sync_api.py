"""
API de synchronisation mobile pour PTCCare
Endpoints pour la synchronisation bidirectionnelle des données
"""

import json
from datetime import datetime, timedelta
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.models import User
from django.db.models import Q
from django.utils import timezone
from django.db import transaction

from ..models.profile import Profile
from ..models.appointment import Appointment
from ..models.pregnancy import Pregnancy
from ..models.mother_pregnancy import MotherPregnancy
from ..middleware.mobile_auth_middleware import mobile_auth_required
from ..decorators.cors_decorators import mobile_api_cors


@csrf_exempt
@mobile_api_cors
def sync_status(request):
    """
    GET /api/mobile/sync/status
    Obtenir le statut de synchronisation et les timestamps
    """
    if request.method != 'GET':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        # Utiliser les données du middleware mobile
        if not hasattr(request, 'mobile_user'):
            return JsonResponse({
                'error': 'Authentification requise',
                'code': 'AUTH_REQUIRED'
            }, status=401)

        user = request.mobile_user
        profile = request.mobile_profile
        
        # Dernières modifications par type de données
        last_sync_data = {
            'user_id': user.id,
            'last_sync': timezone.now().isoformat(),
            'server_time': timezone.now().isoformat(),
            'data_timestamps': {
                'patients': get_last_modified_timestamp(Profile, user),
                'appointments': get_last_modified_timestamp(Appointment, user),
                'pregnancies': get_last_modified_timestamp(Pregnancy, user),
            }
        }
        
        return JsonResponse({
            'status': 'success',
            'data': last_sync_data
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Erreur lors de la récupération du statut: {str(e)}'
        }, status=500)


@csrf_exempt
@mobile_api_cors
def sync_patients(request):
    """
    GET /api/mobile/sync/patients?since=timestamp
    POST /api/mobile/sync/patients (upload des modifications)
    Synchronisation des patients
    """
    try:
        # Vérifier l'authentification
        if not hasattr(request, 'mobile_user'):
            return JsonResponse({
                'error': 'Authentification requise',
                'code': 'AUTH_REQUIRED'
            }, status=401)

        user = request.mobile_user
        profile = request.mobile_profile
        
        if request.method == 'GET':
            # Télécharger les patients modifiés depuis un timestamp
            since = request.GET.get('since')
            
            # Filtrer les patients selon le rôle
            if user.groups.filter(name='admin').exists():
                patients_query = Profile.objects.filter(user__groups__name='patient')
            elif user.groups.filter(name='docteur').exists():
                # Patients du médecin
                patients_query = Profile.objects.filter(
                    user__groups__name='patient'
                ).distinct()
            else:
                patients_query = Profile.objects.none()
            
            # Filtrer par timestamp si fourni
            if since:
                try:
                    since_date = datetime.fromisoformat(since.replace('Z', '+00:00'))
                    patients_query = patients_query.filter(updated_at__gte=since_date)
                except:
                    pass
            
            patients_data = []
            for patient in patients_query:
                patients_data.append({
                    'id': patient.id,
                    'user_id': patient.user.id,
                    'username': patient.user.username,
                    'email': patient.user.email,
                    'firstname': patient.firstname,
                    'lastname': patient.lastname,
                    'tel': patient.tel,
                    'birth_date': patient.birth_date.isoformat() if patient.birth_date else None,
                    'address': patient.address,
                    'assurance': patient.assurance,
                    'husband_name': patient.husband_name,
                    'husband_tel': patient.husband_tel,
                    'created_at': patient.created_at.isoformat(),
                    'updated_at': patient.updated_at.isoformat(),
                })
            
            return JsonResponse({
                'status': 'success',
                'data': {
                    'patients': patients_data,
                    'count': len(patients_data),
                    'timestamp': timezone.now().isoformat()
                }
            })
            
        elif request.method == 'POST':
            # Upload des modifications depuis le mobile
            data = json.loads(request.body)
            patients_updates = data.get('patients', [])

            created_count = 0
            updated_count = 0
            errors = []
            created_patients = []

            # Traiter chaque patient dans une transaction séparée
            for patient_data in patients_updates:
                try:
                    with transaction.atomic():
                        # Vérifier si c'est une création ou une mise à jour
                        mobile_id = patient_data.get('mobile_id')  # ID temporaire du mobile
                        server_id = patient_data.get('id')  # ID serveur si existe

                        if server_id:
                            # Mise à jour d'un patient existant
                            try:
                                patient = Profile.objects.get(id=server_id, user__groups__name='patient')
                                updated_count += update_patient(patient, patient_data, user)
                            except Profile.DoesNotExist:
                                errors.append({
                                    'mobile_id': mobile_id,
                                    'error': f'Patient avec ID {server_id} non trouvé'
                                })
                        else:
                            # Création d'un nouveau patient
                            new_patient = create_patient_from_mobile(patient_data, user)
                            created_patients.append({
                                'mobile_id': mobile_id,
                                'server_id': new_patient.id,
                                'username': new_patient.user.username,
                                'email': new_patient.user.email
                            })
                            created_count += 1

                except Exception as e:
                    errors.append({
                        'mobile_id': patient_data.get('mobile_id', 'unknown'),
                        'error': str(e)
                    })

            return JsonResponse({
                'status': 'success',
                'data': {
                    'created_count': created_count,
                    'updated_count': updated_count,
                    'created_patients': created_patients,
                    'errors': errors,
                    'timestamp': timezone.now().isoformat()
                }
            })
            
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Erreur de synchronisation patients: {str(e)}'
        }, status=500)


@csrf_exempt
@mobile_api_cors
def sync_appointments(request):
    """
    GET /api/mobile/sync/appointments?since=timestamp
    POST /api/mobile/sync/appointments
    Synchronisation des rendez-vous
    """
    try:
        # Vérifier l'authentification
        if not hasattr(request, 'mobile_user'):
            return JsonResponse({
                'error': 'Authentification requise',
                'code': 'AUTH_REQUIRED'
            }, status=401)

        user = request.mobile_user
        profile = request.mobile_profile
        
        if request.method == 'GET':
            since = request.GET.get('since')
            
            # Filtrer les rendez-vous selon le rôle
            if user.groups.filter(name='admin').exists():
                appointments_query = Appointment.objects.all()
            elif user.groups.filter(name='docteur').exists():
                appointments_query = Appointment.objects.filter(doctor=profile)
            else:
                appointments_query = Appointment.objects.none()
            
            # Filtrer par timestamp
            if since:
                try:
                    since_date = datetime.fromisoformat(since.replace('Z', '+00:00'))
                    appointments_query = appointments_query.filter(updated_at__gte=since_date)
                except:
                    pass
            
            appointments_data = []
            for appointment in appointments_query:
                appointments_data.append({
                    'id': appointment.id,
                    'patient_id': appointment.patient.id,
                    'patient_name': f"{appointment.patient.firstname} {appointment.patient.lastname}",
                    'doctor_id': appointment.doctor.id if appointment.doctor else None,
                    'consul_date': appointment.consul_date.isoformat(),
                    'consul_hour': appointment.consul_hour.strftime('%H:%M'),
                    'appointment_type': appointment.appointment_type,
                    'state': appointment.state,
                    'consul_data': appointment.consul_data,
                    'created_at': appointment.created_at.isoformat(),
                    'updated_at': appointment.updated_at.isoformat(),
                })
            
            return JsonResponse({
                'status': 'success',
                'data': {
                    'appointments': appointments_data,
                    'count': len(appointments_data),
                    'timestamp': timezone.now().isoformat()
                }
            })
            
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Erreur de synchronisation rendez-vous: {str(e)}'
        }, status=500)


@csrf_exempt
@mobile_api_cors
@mobile_auth_required
def sync_pregnancies(request):
    """
    GET /api/mobile/sync/pregnancies?since=timestamp
    POST /api/mobile/sync/pregnancies
    Synchronisation des grossesses
    """
    try:
        user = request.user
        profile = Profile.objects.get(user=user)
        
        if request.method == 'GET':
            since = request.GET.get('since')
            
            # Filtrer les grossesses selon le rôle
            if user.groups.filter(name='admin').exists():
                pregnancies_query = Pregnancy.objects.all()
            elif user.groups.filter(name='docteur').exists():
                pregnancies_query = Pregnancy.objects.filter(
                    patient__appointments__doctor=profile
                ).distinct()
            else:
                pregnancies_query = Pregnancy.objects.none()
            
            # Filtrer par timestamp
            if since:
                try:
                    since_date = datetime.fromisoformat(since.replace('Z', '+00:00'))
                    pregnancies_query = pregnancies_query.filter(updated_at__gte=since_date)
                except:
                    pass
            
            pregnancies_data = []
            for pregnancy in pregnancies_query:
                pregnancies_data.append({
                    'id': pregnancy.id,
                    'patient_id': pregnancy.patient.id,
                    'term': pregnancy.term,
                    'state': pregnancy.state,
                    'start_date': pregnancy.start_date.isoformat() if pregnancy.start_date else None,
                    'end_date': pregnancy.end_date.isoformat() if pregnancy.end_date else None,
                    'created_at': pregnancy.created_at.isoformat(),
                    'updated_at': pregnancy.updated_at.isoformat(),
                })
            
            return JsonResponse({
                'status': 'success',
                'data': {
                    'pregnancies': pregnancies_data,
                    'count': len(pregnancies_data),
                    'timestamp': timezone.now().isoformat()
                }
            })
            
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Erreur de synchronisation grossesses: {str(e)}'
        }, status=500)


def create_patient_from_mobile(patient_data, created_by_user):
    """
    Créer un nouveau patient à partir des données mobiles
    """
    from django.contrib.auth.models import Group
    import random
    import string

    # Générer un nom d'utilisateur unique
    base_username = f"PAT-{timezone.now().strftime('%Y%m%d')}"
    counter = 1
    username = f"{base_username}{counter:04d}"

    while User.objects.filter(username=username).exists():
        counter += 1
        username = f"{base_username}{counter:04d}"

    # Générer un mot de passe temporaire
    temp_password = ''.join(random.choices(string.ascii_letters + string.digits, k=8))

    # Créer l'utilisateur
    user = User.objects.create_user(
        username=username,
        email=patient_data.get('email', ''),
        password=temp_password,
        first_name=patient_data.get('firstname', ''),
        last_name=patient_data.get('lastname', '')
    )

    # Ajouter au groupe patient
    patient_group, created = Group.objects.get_or_create(name='patient')
    user.groups.add(patient_group)

    # Créer le profil patient
    birth_date = None
    if patient_data.get('birth_date'):
        try:
            from datetime import datetime
            birth_date = datetime.fromisoformat(patient_data['birth_date'].replace('Z', '+00:00')).date()
        except:
            pass

    patient = Profile.objects.create(
        user=user,
        firstname=patient_data.get('firstname', ''),
        lastname=patient_data.get('lastname', ''),
        tel=patient_data.get('tel', ''),
        birth_date=birth_date,
        address=patient_data.get('address', ''),
        assurance=patient_data.get('assurance', ''),
        husband_name=patient_data.get('husband_name', ''),
        husband_tel=patient_data.get('husband_tel', ''),
        created_by=created_by_user,
        child=False  # C'est une mère par défaut
    )

    return patient


def update_patient(patient, patient_data, updated_by_user):
    """
    Mettre à jour un patient existant
    """
    updated = False

    # Mettre à jour les champs du profil
    fields_to_update = [
        'firstname', 'lastname', 'tel', 'address',
        'assurance', 'husband_name', 'husband_tel'
    ]

    for field in fields_to_update:
        if field in patient_data:
            new_value = patient_data[field]
            if getattr(patient, field) != new_value:
                setattr(patient, field, new_value)
                updated = True

    # Mettre à jour la date de naissance si fournie
    if 'birth_date' in patient_data and patient_data['birth_date']:
        try:
            from datetime import datetime
            birth_date = datetime.fromisoformat(patient_data['birth_date'].replace('Z', '+00:00')).date()
            if patient.birth_date != birth_date:
                patient.birth_date = birth_date
                updated = True
        except:
            pass

    # Mettre à jour l'utilisateur associé
    user = patient.user
    if 'email' in patient_data and patient_data['email'] != user.email:
        user.email = patient_data['email']
        user.save()
        updated = True

    if updated:
        patient.save()
        return 1
    return 0


def get_last_modified_timestamp(model_class, user):
    """
    Obtenir le timestamp de la dernière modification pour un modèle
    """
    try:
        latest = model_class.objects.filter(
            # Filtrer selon les permissions utilisateur
        ).order_by('-updated_at').first()

        if latest and hasattr(latest, 'updated_at'):
            return latest.updated_at.isoformat()
        return None
    except:
        return None
