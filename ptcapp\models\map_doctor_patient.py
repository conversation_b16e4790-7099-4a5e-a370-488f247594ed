from django.db import models
from .profile import Profile


class MapDoctorPatient(models.Model):
    patient = models.ForeignKey(Profile, related_name='patiente', null = True, on_delete=models.SET_NULL)
    doctor = models.ForeignKey(Profile, related_name='doctor', null = True, on_delete=models.SET_NULL)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)