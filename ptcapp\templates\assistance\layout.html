{% load static %}
{% load layout %}
<!doctype html>
<html lang="en">


<!-- Mirrored from themesdesign.in/medroc/layouts/layouts-colored-sidebar.html by HTTrack Website Copier/3.x [XR&CO'2014], Fri, 25 Feb 2022 13:00:54 GMT -->

<head>

    <meta charset="utf-8" />
    <title>{{title}}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Premium Multipurpose Admin & Dashboard Template" name="description" />
    <meta content="Themesdesign" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="{% static 'favicon.ico' %}">

    <!-- jquery.vectormap css -->
    {% comment %}
    <link href="assets/libs/admin-resources/jquery.vectormap/jquery-jvectormap-1.2.2.css" rel="stylesheet" type="text/css" /> {% endcomment %} {% block up-style %}{% endblock up-style %}
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/app.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/icons.min.css' %}">
    <link rel="stylesheet" href="{% static 'libs/sweetalert2/sweetalert2.min.css' %}"> {% block down-style %}{% endblock down-style %}

</head>

<body data-sidebar="colored">

    <!-- Begin page -->
    <div id="layout-wrapper">

        <header id="page-topbar">
            <div class="navbar-header">
                <div class="d-flex">
                    <!-- LOGO -->
                    <div class="navbar-brand-box">
                        <a href="{% url 'admin.index' %}" class="logo">
                            <span class="logo-sm">
                                <img src="{% static 'images/logo-sm.png' %}" alt="" height="30">
                            </span>
                            <span class="logo-lg">
                                <img src="{% static 'images/logo-light.png' %}" alt="" height="50">
                            </span>
                        </a>
                    </div>

                    <button type="button" class="btn btn-sm px-3 font-size-16 header-item waves-effect vertical-menu-btn">
                        <i class="fa fa-fw fa-bars"></i>
                    </button>
                </div>

                <div class="d-flex">
                    {% comment %} <div class="dropdown d-inline-block">
                        <button type="button" class="btn header-item noti-icon waves-effect" id="page-header-notifications-dropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="ri-notification-3-line"></i>
                            <span class="noti-dot"></span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end p-0" aria-labelledby="page-header-notifications-dropdown">
                            <div class="p-3">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h6 class="m-0"> Notifications </h6>
                                    </div>
                                    
                                    <div class="col-auto">
                                        <a href="#!" class="small"> View All</a>
                                    </div>
                                </div>
                            </div>
                            <div data-simplebar class="mb-2" style="max-height: 230px;">
                                <a href="javascript:;" data-doc="William Dr." data-datetime="20/05/2022 à 14 : 30" class="text-reset notification-item sa-title">
                                    <div class="d-flex align-items-center">
                                        <img src="{% static 'images/default-profile.png' %}" class="me-3 rounded-circle avatar-xs" alt="user-pic">
                                        <div class="flex-grow-1 text-truncate">
                                            <h6 class="mt-0 mb-1">Docteur William<span class="mb-1 text-muted fw-normal"> a prévu une consultation pour le 20/05/2022.</span>
                                            </h6>
                                            <p class="mb-0 font-size-12"><i class="mdi mdi-clock-outline"></i> 5 min ago</p>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            
                            <div class="p-2 border-top">
                                <div class="d-grid">
                                    <a class="btn btn-sm btn-link font-size-14 text-center" href="{% url 'logout' %}">
                                        <i class="mdi mdi-arrow-right-circle me-1"></i> View More..
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div> {% endcomment %}

                    <div class="dropdown d-inline-block user-dropdown">
                        <button type="button" class="btn header-item waves-effect d-flex align-items-center" id="page-header-user-dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <img class="rounded-circle header-profile-user" src="{% static 'images/default-profile.png' %}"
                                alt="Header Avatar"> <span class="h5 m-0"> {{ request.user|auth_fullname }} </span>
                        </button>
                        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end">
                            <div class="p-3">
                                <div class="row align-items-center">
                                    <div class="col">
                                        <h6 class="m-0"> {{ request.user|auth_fullname }} </h6>
                                    </div>
                                    {% comment %}
                                    <div class="col-auto">
                                        <a href="#!" class="small"> Available</a>
                                    </div> {% endcomment %}
                                </div>
                            </div>
                            <div data-simplebar style="max-height: 230px;">
                                <!-- item-->
                                <a href="{% url 'assistant.profile' %}" class="text-reset notification-item">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-xs me-3 mt-1">
                                            <span class="avatar-title bg-soft-primary rounded-circle font-size-16">
                                            <i class="ri-user-line text-primary font-size-16"></i> 
                                        </span>
                                        </div>
                                        <div class="flex-grow-1 text-truncate">
                                            <h6 class="mb-1">Profil</h6>
                                            <p class="mb-0 font-size-12">Voir mes informations personnelles</p>
                                        </div>
                                    </div>
                                </a>
                            </div>
                            <!-- item-->
                            <div class="pt-2 border-top">
                                <div class="d-grid">
                                    <a class="btn btn-sm btn-link font-size-14 text-center" href="{% url 'logout' %}">
                                        <i class="ri-shut-down-line align-middle me-1"></i> Se déconnecter
                                    </a>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
        </header>

        <!-- ========== Left Sidebar Start ========== -->
        <div class="vertical-menu" >

            <!-- LOGO -->
            <div class="navbar-brand-box"  style="background-color:#fff !important;">
                <a href="{% url 'admin.index' %}" class="logo">
                    <span class="logo-sm">
                        <img src="{% static 'images/logo-sm.png' %}" alt="" height="30">
                    </span>
                    <span class="logo-lg">
                        <img src="{% static 'images/logo-light.png' %}" alt="" height="70">
                    </span>
                </a>
            </div>

            <button type="button" class="btn btn-sm px-3 font-size-16 header-item waves-effect vertical-menu-btn"  style="color:#bcbccc !important;">
                <i class="fa fa-fw fa-bars"></i>
            </button>

            <div data-simplebar class="sidebar-menu-scroll">

                <!--- Sidemenu -->
                <div id="sidebar-menu">
                    <!-- Left Menu Start -->
                    <ul class="metismenu list-unstyled" id="side-menu">
                        <li>
                            <a href="{% url 'assistant.index' %}" class="waves-effect">
                                <i class="ri-team-fill"></i>
                                <span>Patients suivis</span>
                            </a>
                        </li>
                        <!-- <li>
                            <a href="{% url 'assistant.archives' %}" class="waves-effect">
                                <i class="ri-inbox-archive-fill"></i>
                                <span>Liste des patients archivés</span>
                            </a>
                        </li> -->
                        <li>
                            <a href="{% url 'assistant.pregnancy' %}" class="waves-effect">
                                <i class="mdi mdi-human-pregnant"></i>
                                <span>Grossesses</span>
                            </a>
                        </li>
                        <li>
                            <a href="{% url 'assistant.appointment.index' %}" class="waves-effect">
                                <i class="ri-heart-add-fill"></i>
                                <span>Consultations</span>
                            </a>
                        </li>
                        
                    </ul>
                </div>
                <!-- Sidebar -->
            </div>
        </div>
        <!-- Left Sidebar End -->



        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->
        <div class="main-content">

            <div class="page-content">
                <div class="container-fluid">
                    <!-- start page title -->
                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-flex align-items-center justify-content-between">
                                {% url 'admin.index' as url %}
                                <div class="d-flex align-items-center">
                                    <h4 class="mb-0">{% if request.path == url %}Bienvenue {{ request.user|auth_fullname }} {% else %} <a href="javascript:;" onclick="history.back()">Retour</a> {% endif %}</h4>
    
                                    {% block action_button %}{% endblock action_button %}   

                                </div>

                                <div class="page-title-right">
                                    <ol class="breadcrumb m-0">
                                        <li class="breadcrumb-item active">{{request.path}}</li>
                                    </ol>
                                </div>

                            </div>
                        </div>
                    </div>
                    <!-- end page title -->
                    {% block content %}{% endblock content %}

                </div>
                <!-- container-fluid -->
            </div>
            <!-- End Page-content -->

            <footer class="footer">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-sm-6">
                            <script>
                                document.write(new Date().getFullYear())
                            </script> © PTCCare.
                        </div>
                    </div>
                </div>
            </footer>

        </div>
        <!-- end main content-->

        </div>
        <!-- END layout-wrapper -->

        <!-- Right bar overlay-->
        <div class="rightbar-overlay"></div>

        <!-- JAVASCRIPT -->
        {% block up-script %}{% endblock up-script %}
        <script src="{% static 'libs/jquery/jquery.min.js' %}"></script>
        <script src="{% static 'libs/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
        <script src="{% static 'libs/simplebar/simplebar.min.js' %}"></script>
        <script src="{% static 'libs/sweetalert2/sweetalert2.min.js' %}"></script>
        <script>
            $(".sa-title").click(function() {
                Swal.fire({
                    title: $(this).data('doc'),
                    text: "Vous avez une consultation prévu le " + $(this).data('datetime'),
                    icon: "info",
                    confirmButtonColor: "#5664d2",
                });
            })
        </script>
        {% block down-script %}{% endblock down-script %}
        <script src="{% static 'js/app.js' %}"></script>

</body>

<!-- Mirrored from themesdesign.in/medroc/layouts/layouts-colored-sidebar.html by HTTrack Website Copier/3.x [XR&CO'2014], Fri, 25 Feb 2022 13:00:54 GMT -->

</html>