# 🏥 PTCCare - Application de Gestion des Soins Prénataux

## 📋 Description

PTCCare est une application web Django complète pour la gestion des soins prénataux avec une API mobile sécurisée pour l'intégration Flutter. L'application permet aux professionnels de santé de gérer les patientes, les grossesses, les rendez-vous et offre une synchronisation hors ligne pour les applications mobiles.

## 🚀 Déploiement Rapide sur Heroku

### Option 1 : Déploiement en un clic
[![Deploy](https://www.herokucdn.com/deploy/button.svg)](https://heroku.com/deploy?template=https://github.com/votre-username/ptccare-web)

### Option 2 : Déploiement avec script PowerShell
```powershell
.\deploy.ps1 -AppName "ptccare-app-2025" -AdminEmail "<EMAIL>"
```

### Option 3 : Déploiement manuel
Suivez le guide détaillé dans [HEROKU_DEPLOYMENT_GUIDE.md](HEROKU_DEPLOYMENT_GUIDE.md)

## 🔧 Configuration Locale

### Prérequis
- Python 3.11+
- PostgreSQL ou MySQL
- Redis (pour Celery)

### Installation
```bash
# Cloner le projet
git clone https://github.com/votre-username/ptccare-web.git
cd ptccare-web

# Créer l'environnement virtuel
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
.\venv\Scripts\Activate.ps1  # Windows

# Installer les dépendances
pip install -r requirements.txt

# Configuration
cp .env.example .env
# Éditer .env avec vos paramètres

# Migrations
python manage.py migrate

# Créer le superutilisateur
python manage.py createsuperuser

# Charger les données de test
python populate_test_data.py

# Démarrer le serveur
python manage.py runserver
```

## 📱 API Mobile

### Endpoints d'authentification
- `POST /api/mobile/auth/login` - Connexion
- `POST /api/mobile/auth/refresh` - Rafraîchir le token
- `GET /api/mobile/auth/verify` - Vérifier le token
- `POST /api/mobile/auth/logout` - Déconnexion

### Endpoints de données
- `GET /api/mobile/initial-data` - Données de référence
- `GET /api/mobile/user-data` - Données utilisateur

### Documentation complète
Voir [MOBILE_API_DOCUMENTATION.md](MOBILE_API_DOCUMENTATION.md)

## 👥 Comptes de Test

| Rôle | Username | Email | Password |
|------|----------|-------|----------|
| Admin | admin | <EMAIL> | admin123 |
| Médecin | DOC-12345678 | <EMAIL> | doctor123 |
| Assistant | ASS-11111111 | <EMAIL> | assistant123 |
| Patiente | PAT-20240001 | <EMAIL> | patient123 |

## 🔒 Sécurité

- Authentification JWT pour l'API mobile
- CORS configuré pour les applications mobiles
- HTTPS forcé en production
- Variables d'environnement pour les secrets
- Validation des données d'entrée

## 🏗️ Architecture

```
PTCCare/
├── ptccare/           # Configuration Django
├── ptcapp/            # Application principale
│   ├── models/        # Modèles de données
│   ├── views/         # Vues web et API
│   ├── templates/     # Templates HTML
│   ├── static/        # Fichiers statiques
│   └── middleware/    # Middleware personnalisé
├── requirements.txt   # Dépendances Python
├── Procfile          # Configuration Heroku
└── app.json          # Métadonnées Heroku
```

## 🔄 Workflow de Développement

1. **Développement local** avec SQLite/MySQL
2. **Tests** avec les données de test
3. **Déploiement staging** sur Heroku
4. **Tests d'intégration** mobile
5. **Déploiement production**

## 📊 Monitoring

### Heroku
```bash
# Logs en temps réel
heroku logs --tail -a votre-app

# Statut des dynos
heroku ps -a votre-app

# Métriques
heroku addons:open newrelic -a votre-app
```

### Endpoints de santé
- `GET /` - Page d'accueil
- `GET /api/mobile/initial-data` - Test API

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Support

- **Documentation** : [MOBILE_API_DOCUMENTATION.md](MOBILE_API_DOCUMENTATION.md)
- **Guide de déploiement** : [HEROKU_DEPLOYMENT_GUIDE.md](HEROKU_DEPLOYMENT_GUIDE.md)
- **Issues** : [GitHub Issues](https://github.com/votre-username/ptccare-web/issues)

## 🎯 Roadmap

- [ ] Intégration complète Firebase
- [ ] Notifications push mobiles
- [ ] Rapports et analytics
- [ ] API de télémédecine
- [ ] Support multi-langues
- [ ] Tests automatisés

---

**PTCCare** - Améliorer les soins prénataux grâce à la technologie 🏥📱
