from __future__ import absolute_import, unicode_literals, print_function
import datetime as dt 

from django.contrib.auth.models import User
from django.shortcuts import get_object_or_404

from celery import shared_task, Celery

import sys, time, logging, wave, serial

from gsmmodem.modem import GsmModem, SentSms
from gsmmodem.exceptions import InterruptedException, CommandError

from ptcapp.models.alert import Alert


PORT = "/dev/ttyUSB0"
BAUDRATE = 9600
PIN = None

@shared_task(bind = True)
def send_sms(self, name, date, hour, number, doctor):
    PIN = None
    print(doctor)
    print("Iniciating modem...")
    logging.basicConfig(format='%(levelname)s: %(message)s', level=logging.DEBUG)
    modem = GsmModem(PORT, BAUDRATE)
    modem.smsTextMode = False 
    x = modem.connect(PIN)
    sms="Bonsoir Mme "+ name+", vous avez une consultation programmée pour le "+ date+" à "+hour+ " auprès du Docteur "+ doctor+" au CHD. \n\nMerci, bonne suite de journée. \n\nCe message est envoyé par le serveur PTCCare."
    response = modem.sendSms(number,sms )

    if type(response) == SentSms:
        print('Done')
        # alert = get_object_or_404(Alert, task_id = self.request.task_id)
        # alert.state = "sent"
        # alert.save
    else : 
        print("Error")
    modem.close()


@shared_task(bind = True)
def call(self, number, audio):
    PIN = None
    if number == None or number == '00000':
        print('Error: Please change the NUMBER variable\'s value before running this example.')
        sys.exit(1)
    print('Initializing modem...')
    logging.basicConfig(format='%(levelname)s: %(message)s', level=logging.DEBUG)
    modem = GsmModem(PORT, BAUDRATE)
    modem.connect(PIN)
    print('Waiting for network coverage...')
    modem.waitForNetworkCoverage(30)
    print('Dialing number: {0}'.format(number))
    music = wave.open(audio,'r')
    call = modem.dial(number)
    print('Waiting for call to be answered/rejected')
    wasAnswered = False
    while call.active:
        if call.answered:
            wasAnswered = True
            print('Call has been answered; waiting a while...')
            # Wait for a bit - some older modems struggle to send DTMF tone immediately after answering a call
            time.sleep(1.0)
            try:
                if call.active: # Call could have been ended by remote party while we waited in the time.sleep() call
                    # call.sendDtmfTone('9515')
                    modem.write('AT^DDSETEX=2')
                    ser = serial.Serial(dsrdtr=True, rtscts=True)
                    ser.port = "/dev/ttyUSB1"
                    ser.baudrate = 115200
                    # ser.bytesize = serial.EIGHTBITS  # number of bits per bytes
                    ser.timeout = 1  # non-block read
                    ser.writeTimeout = None  # timeout for write
                    chunk=320
                    frame = music.readframes(chunk)      
                    if not ser.isOpen():
                        ser.open() 
                    while frame != b'':
                        if ser.isOpen():
                            ser.write(frame)
                            frame = music.readframes(chunk)  
                            time.sleep(0.035)
                    music.close
                    # alert = get_object_or_404(Alert, task_id = self.request.task_id)
                    # alert.state = "sent"
                    # alert.save

            except InterruptedException as e:
                # Call was ended during playback

                print('DTMF playback interrupted: {0} ({1} Error {2})'.format(e, e.cause.type, e.cause.code))
            except CommandError as e:
                print('DTMF playback failed: {0}'.format(e))
            finally:
                if call.active: # Call is still active
                    print('Hanging up call...')
                    modem.write('AT+CHUP')
                    call.hangup()
                else: # Call is no longer active (remote party ended it)
                    print('Call has been ended by remote party')
        else:
            # Wait a bit and check again
            time.sleep(0.5)

    if not wasAnswered:
        print('Call was not answered by remote party')
        # alert = get_object_or_404(Alert, task_id = self.request.task_id)
        # alert.time = dt.datetime.strptime(alert.time,"%H%M").time() + dt.timedelta(hours = 4)
        # new_task = call.apply_async((number, audio), eta=dt.datetime.combine(alert.date, alert.time))
        # alert.task_id = new_task.task_id
        # alert.save()

    print('Done.')
    modem.close()

@shared_task
def show():
    print('alert rev')

@shared_task
def show1():
    print('alert 1')