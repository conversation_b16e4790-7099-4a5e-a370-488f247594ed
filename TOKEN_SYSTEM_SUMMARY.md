# Résumé Co<PERSON>t - Système de Gestion des Tokens PTC Care

## 🎯 Réponses aux Questions Posées

### 1. **Stockage des Tokens - Tables de Base de Données**

#### **Table Principale : `ptcapp_password_reset_token`**
```sql
CREATE TABLE ptcapp_password_reset_token (
    id INTEGER PRIMARY KEY,
    user_id INTEGER UNIQUE REFERENCES auth_user(id),
    token VARCHAR(64) UNIQUE NOT NULL,
    created_at DATETIME NOT NULL,
    expires_at DATETIME NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    force_password_change BOOLEAN DEFAULT TRUE
);
```

#### **Table Complémentaire : `ptcapp_user_password_status`**
```sql
CREATE TABLE ptcapp_user_password_status (
    id INTEGER PRIMARY KEY,
    user_id INTEGER UNIQUE REFERENCES auth_user(id),
    must_change_password B<PERSON><PERSON><PERSON>N DEFAULT FALSE,
    password_changed_at DATETIME NULL,
    first_login_completed BOOLEAN DEFAULT FALSE,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);
```

### 2. **Endpoints Concernés par la Gestion des Tokens**

#### **Endpoints qui CRÉENT des tokens :**
| Endpoint | Action | Type de Token |
|----------|--------|---------------|
| `POST /api/create-health-agent/` | Création automatique | Token de bienvenue (48h) |
| `POST /api/create-patient/` | Création automatique | Token de bienvenue (48h) |
| `POST /api/request-password-reset/` | Création sur demande | Token de réinitialisation (48h) |

#### **Endpoints qui UTILISENT des tokens :**
| Endpoint | Action | Effet |
|----------|--------|-------|
| `GET /change-password/{token}/` | Validation web | Affichage formulaire |
| `POST /api/change-password/` | Utilisation API | Changement + invalidation |

#### **Endpoints qui VÉRIFIENT le statut :**
| Endpoint | Action | Vérification |
|----------|--------|--------------|
| `POST /api/login/` | Connexion | `must_change_password` |
| Tous les endpoints protégés | Middleware | Redirection si changement requis |

### 3. **Cycle de Vie Complet des Tokens**

#### **Phase 1 : Génération**
```python
# Lors de création d'utilisateur
def send_welcome_email(user, temporary_password, role):
    # Création ou récupération du token
    token, created = PasswordResetToken.objects.get_or_create(
        user=user,
        defaults={'force_password_change': True}
    )
    
    # Génération du token si nouveau
    if created:
        token.token = PasswordResetToken.generate_token()  # 64 caractères
        token.expires_at = timezone.now() + timedelta(hours=48)
        token.save()
```

#### **Phase 2 : Stockage**
```python
# Structure en base
PasswordResetToken:
    user_id: 123 (FK vers auth_user)
    token: "abc123...xyz789" (64 caractères uniques)
    created_at: 2024-06-15 10:00:00
    expires_at: 2024-06-17 10:00:00 (48h plus tard)
    is_used: False
    force_password_change: True

UserPasswordStatus:
    user_id: 123 (FK vers auth_user)
    must_change_password: True
    password_changed_at: NULL
    first_login_completed: False
```

#### **Phase 3 : Récupération et Validation**
```python
# Lors de l'utilisation du token
def change_password_api(request):
    token_value = data.get('token')
    
    # Récupération et validation
    reset_token = PasswordResetToken.objects.get(
        token=token_value,
        is_used=False,
        expires_at__gt=timezone.now()
    )
    
    # Validation réussie = token valide
```

#### **Phase 4 : Invalidation**
```python
# Après changement de mot de passe réussi
def mark_as_used(self):
    self.is_used = True
    self.force_password_change = False
    self.save()

# Mise à jour du statut utilisateur
def mark_password_changed(self):
    self.must_change_password = False
    self.password_changed_at = timezone.now()
    self.first_login_completed = True
    self.save()
```

### 4. **Relations entre les Modèles**

#### **Diagramme de Relations**
```
auth_user (Django)
    ├── OneToOne → PasswordResetToken
    │   ├── Stockage du token de réinitialisation
    │   ├── Gestion de l'expiration (48h)
    │   └── Suivi de l'utilisation (is_used)
    │
    ├── OneToOne → UserPasswordStatus  
    │   ├── Statut de changement obligatoire
    │   ├── Historique des changements
    │   └── Suivi de la première connexion
    │
    └── OneToOne → Profile
        └── Informations personnelles
```

#### **Synchronisation des États**
```python
# Lors de la création d'un utilisateur
User.objects.create_user(...)  # Utilisateur Django
Profile.objects.create(...)    # Profil PTC Care
UserPasswordStatus.objects.create(must_change_password=True)  # Statut
PasswordResetToken.objects.create(...)  # Token automatique

# Lors du changement de mot de passe
reset_token.mark_as_used()  # Token invalidé
user.password_status.mark_password_changed()  # Statut mis à jour
```

## 🔄 Flux Détaillé par Scénario

### **Scénario A : Création d'un Nouvel Agent**

1. **Admin appelle** `POST /api/create-health-agent/`
2. **Système crée** :
   - `User` Django avec mot de passe temporaire
   - `Profile` avec informations personnelles
   - `UserPasswordStatus` avec `must_change_password=True`
3. **Service email** appelle `send_welcome_email()`
4. **Token créé** automatiquement :
   ```python
   PasswordResetToken.objects.get_or_create(
       user=user,
       defaults={
           'token': generate_token(),  # 64 chars
           'expires_at': now + 48h,
           'force_password_change': True
       }
   )
   ```
5. **Email envoyé** avec lien : `/change-password/{token}/`

### **Scénario B : Réinitialisation de Mot de Passe**

1. **Utilisateur appelle** `POST /api/request-password-reset/`
2. **Système invalide** anciens tokens :
   ```python
   PasswordResetToken.objects.filter(
       user=user, is_used=False
   ).update(is_used=True)
   ```
3. **Nouveau token créé** :
   ```python
   PasswordResetToken.objects.create(
       user=user,
       token=secrets.token_urlsafe(48),  # Sécurisé
       expires_at=now + 48h
   )
   ```
4. **Email envoyé** avec nouveau lien

### **Scénario C : Utilisation du Token**

1. **Utilisateur clique** lien email → `GET /change-password/{token}/`
2. **Système valide** token :
   ```python
   token = PasswordResetToken.objects.get(
       token=token_value,
       is_used=False,
       expires_at__gt=now
   )
   ```
3. **Formulaire affiché** si token valide
4. **Utilisateur soumet** → `POST /api/change-password/`
5. **Système finalise** :
   ```python
   user.set_password(new_password)  # Django
   token.mark_as_used()  # Token invalidé
   user.password_status.mark_password_changed()  # Statut mis à jour
   ```

## 🔒 Sécurité et Contraintes

### **Contraintes de Base de Données**
- **Token unique** : Contrainte UNIQUE sur le champ token
- **Un token par utilisateur** : Relation OneToOne
- **Expiration automatique** : Vérification à chaque utilisation
- **Usage unique** : Flag `is_used` empêche la réutilisation

### **Génération Sécurisée**
```python
# Méthode 1 : Modèle PasswordResetToken
@staticmethod
def generate_token():
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(64))

# Méthode 2 : Endpoint request-password-reset
def generate_secure_token():
    return secrets.token_urlsafe(48)  # 64 caractères base64url
```

### **Validation Multi-Niveaux**
1. **Existence** : Token existe en base
2. **Expiration** : `expires_at > now()`
3. **Utilisation** : `is_used = False`
4. **Utilisateur** : Relation valide avec User

## 📊 Maintenance et Monitoring

### **Requêtes de Monitoring**
```sql
-- Tokens actifs
SELECT COUNT(*) FROM ptcapp_password_reset_token 
WHERE is_used = FALSE AND expires_at > NOW();

-- Utilisateurs bloqués
SELECT COUNT(*) FROM ptcapp_user_password_status 
WHERE must_change_password = TRUE;

-- Tokens à nettoyer
SELECT COUNT(*) FROM ptcapp_password_reset_token 
WHERE is_used = TRUE OR expires_at <= NOW();
```

### **Nettoyage Automatique**
```python
# Commande de maintenance recommandée
PasswordResetToken.objects.filter(
    models.Q(expires_at__lt=timezone.now()) | 
    models.Q(is_used=True)
).delete()
```

## ✅ Points Clés à Retenir

1. **Deux modèles complémentaires** : `PasswordResetToken` (tokens) + `UserPasswordStatus` (statuts)
2. **Création automatique** : Tokens générés lors de création d'utilisateurs
3. **Sécurité renforcée** : Tokens uniques, expiration 48h, usage unique
4. **Synchronisation** : États cohérents entre les deux modèles
5. **Maintenance** : Nettoyage périodique des tokens expirés/utilisés

Le système est **robuste, sécurisé et entièrement intégré** dans le workflow PTC Care ! 🎉
