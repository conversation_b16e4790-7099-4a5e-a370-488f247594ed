# Generated by Django 3.2.12 on 2022-07-09 00:05

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('ptcapp', '0002_appointment_doctor'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='pregnancy',
            name='end_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='MapPregnancyChild',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('child', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='nouveau_né', to='ptcapp.profile')),
                ('pregnancy', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='grossesse', to='ptcapp.pregnancy')),
            ],
        ),
    ]
