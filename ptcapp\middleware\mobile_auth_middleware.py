"""
Middleware d'authentification mobile pour PTCCare
Gestion automatique de l'authentification JWT pour les endpoints mobile
"""

import jwt
from django.conf import settings
from django.http import JsonResponse
from django.contrib.auth.models import User
from django.utils.deprecation import MiddlewareMixin

from ..models import Profile

class MobileAuthMiddleware(MiddlewareMixin):
    """
    Middleware pour l'authentification automatique des endpoints mobile
    """
    
    # Endpoints qui nécessitent une authentification
    PROTECTED_PATHS = [
        '/api/mobile/user-data',
        '/api/mobile/data/',
        '/api/mobile/sync/',
        '/api/mobile/patients/',
        '/api/mobile/appointments/',
        '/api/mobile/pregnancies/',
        '/api/mobile/profile/',
    ]
    
    # Endpoints publics (pas d'authentification requise)
    PUBLIC_PATHS = [
        '/api/mobile/auth/login',
        '/api/mobile/auth/refresh',
        '/api/mobile/auth/logout',
        '/api/mobile/auth/verify',
        '/api/mobile/initial-data',
    ]

    def process_request(self, request):
        """Traiter la requête pour vérifier l'authentification"""
        
        # Vérifier si c'est un endpoint mobile protégé
        if not any(request.path.startswith(path) for path in self.PROTECTED_PATHS):
            return None
            
        # Vérifier si c'est un endpoint public
        if any(request.path.startswith(path) for path in self.PUBLIC_PATHS):
            return None

        # Récupérer le token JWT
        auth_header = request.META.get('HTTP_AUTHORIZATION')
        if not auth_header or not auth_header.startswith('Bearer '):
            return JsonResponse({
                'error': 'Token d\'authentification requis',
                'code': 'MISSING_TOKEN'
            }, status=401)

        try:
            token = auth_header.split(' ')[1]
            payload = self.verify_jwt_token(token)
            
            if not payload:
                return JsonResponse({
                    'error': 'Token invalide ou expiré',
                    'code': 'INVALID_TOKEN'
                }, status=401)

            # Récupérer l'utilisateur et le profil
            user = User.objects.get(id=payload['user_id'])
            profile = Profile.objects.get(id=payload['profile_id'])
            
            # Ajouter les informations à la requête
            request.mobile_user = user
            request.mobile_profile = profile
            request.mobile_token_payload = payload
            
            return None
            
        except (User.DoesNotExist, Profile.DoesNotExist):
            return JsonResponse({
                'error': 'Utilisateur non trouvé',
                'code': 'USER_NOT_FOUND'
            }, status=404)
        except Exception as e:
            return JsonResponse({
                'error': 'Erreur d\'authentification',
                'code': 'AUTH_ERROR'
            }, status=401)

    def verify_jwt_token(self, token):
        """Vérifier et décoder un token JWT"""
        try:
            JWT_SECRET = getattr(settings, 'SECRET_KEY', 'ptccare-mobile-secret-key')
            payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None

def mobile_auth_required(view_func):
    """
    Décorateur pour les vues qui nécessitent une authentification mobile
    """
    def wrapper(request, *args, **kwargs):
        if not hasattr(request, 'mobile_user'):
            return JsonResponse({
                'error': 'Authentification requise',
                'code': 'AUTH_REQUIRED'
            }, status=401)
        return view_func(request, *args, **kwargs)
    return wrapper

def admin_required(view_func):
    """
    Décorateur pour les vues qui nécessitent un rôle administrateur
    """
    def wrapper(request, *args, **kwargs):
        if not hasattr(request, 'mobile_user'):
            return JsonResponse({
                'error': 'Authentification requise',
                'code': 'AUTH_REQUIRED'
            }, status=401)
            
        if not request.mobile_user.groups.filter(name='admin').exists():
            return JsonResponse({
                'error': 'Accès administrateur requis',
                'code': 'ADMIN_REQUIRED'
            }, status=403)
            
        return view_func(request, *args, **kwargs)
    return wrapper

def doctor_or_admin_required(view_func):
    """
    Décorateur pour les vues qui nécessitent un rôle médecin ou administrateur
    """
    def wrapper(request, *args, **kwargs):
        if not hasattr(request, 'mobile_user'):
            return JsonResponse({
                'error': 'Authentification requise',
                'code': 'AUTH_REQUIRED'
            }, status=401)
            
        allowed_roles = ['admin', 'docteur']
        user_role = request.mobile_user.groups.first().name if request.mobile_user.groups.exists() else None
        
        if user_role not in allowed_roles:
            return JsonResponse({
                'error': 'Accès médecin ou administrateur requis',
                'code': 'DOCTOR_OR_ADMIN_REQUIRED'
            }, status=403)
            
        return view_func(request, *args, **kwargs)
    return wrapper
