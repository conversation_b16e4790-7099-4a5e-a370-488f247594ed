{"info": {"name": "PTC Care API Collection - Updated", "description": "Collection complète pour tester tous les endpoints API de PTC Care avec le nouvel endpoint de réinitialisation de mot de passe", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "version": "1.1.0"}, "variable": [{"key": "base_url", "value": "http://localhost:8000", "type": "string"}, {"key": "user_id", "value": "", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}], "item": [{"name": "🔐 Authentication & Password", "item": [{"name": "Login - <PERSON><PERSON>", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Login successful', function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('success');", "    ", "    if (jsonData.user_id) {", "        pm.collectionVariables.set('user_id', jsonData.user_id);", "        pm.collectionVariables.set('auth_token', 'Bearer ' + jsonData.user_id);", "    }", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"admin123\"\n}"}, "url": {"raw": "{{base_url}}/api/login/", "host": ["{{base_url}}"], "path": ["api", "login", ""]}}}, {"name": "Request Password Reset", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Password reset request successful', function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('success');", "});", "", "pm.test('Contains security message', function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.message).to.include('Si cette adresse email existe');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/request-password-reset/", "host": ["{{base_url}}"], "path": ["api", "request-password-reset", ""]}, "description": "Demande de réinitialisation de mot de passe sécurisée avec rate limiting"}}, {"name": "Request Password Reset - Rate Limit Test", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/api/request-password-reset/", "host": ["{{base_url}}"], "path": ["api", "request-password-reset", ""]}, "description": "Test du rate limiting - exécuter 4 fois rapidement pour tester la limite"}}, {"name": "Request Password Reset - Invalid Email", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Invalid email handled correctly', function () {", "    pm.response.to.have.status(400);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.error).to.include('email');", "});"]}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"invalid-email\"\n}"}, "url": {"raw": "{{base_url}}/api/request-password-reset/", "host": ["{{base_url}}"], "path": ["api", "request-password-reset", ""]}, "description": "Test avec format d'email invalide"}}, {"name": "Change Password - Current Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"current_password\": \"TempPassword123\",\n  \"new_password\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/change-password/", "host": ["{{base_url}}"], "path": ["api", "change-password", ""]}}}, {"name": "Change Password - Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"token\": \"your-reset-token-here\",\n  \"new_password\": \"NewSecurePassword123!\"\n}"}, "url": {"raw": "{{base_url}}/api/change-password/", "host": ["{{base_url}}"], "path": ["api", "change-password", ""]}}}]}, {"name": "👥 User Management", "item": [{"name": "Create Health Agent", "event": [{"listen": "test", "script": {"type": "text/javascript", "exec": ["pm.test('Agent created successfully', function () {", "    pm.response.to.have.status(200);", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql('success');", "    pm.expect(jsonData.email_sent).to.be.true;", "});"]}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_id}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstname\": \"<PERSON><PERSON> <PERSON>\",\n  \"lastname\": \"Reset\",\n  \"tel\": \"+22912345678\",\n  \"email\": \"<EMAIL>\",\n  \"role\": \"docteur\",\n  \"sexe\": \"M\",\n  \"address\": \"123 Reset Street\",\n  \"hospital_id\": 1,\n  \"service_id\": 1,\n  \"speciality_id\": 1\n}"}, "url": {"raw": "{{base_url}}/api/create-health-agent/", "host": ["{{base_url}}"], "path": ["api", "create-health-agent", ""]}}}, {"name": "Create Patient", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_id}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstname\": \"Patient\",\n  \"lastname\": \"Reset\",\n  \"tel\": \"+22987654321\",\n  \"email\": \"<EMAIL>\",\n  \"sexe\": \"F\",\n  \"birth_date\": \"1990-05-15\",\n  \"address\": \"456 Patient Street\",\n  \"language_id\": 1,\n  \"occupation\": \"Test\",\n  \"is_child\": false,\n  \"is_pregnant\": false\n}"}, "url": {"raw": "{{base_url}}/api/create-patient/", "host": ["{{base_url}}"], "path": ["api", "create-patient", ""]}}}]}, {"name": "📊 Data Retrieval", "item": [{"name": "Get Initial Data", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/initial-data/", "host": ["{{base_url}}"], "path": ["api", "initial-data", ""]}}}, {"name": "Get Patients", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_id}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/patients/", "host": ["{{base_url}}"], "path": ["api", "patients", ""]}}}, {"name": "Get Agents", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_id}}", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "{{base_url}}/api/agents/", "host": ["{{base_url}}"], "path": ["api", "agents", ""]}}}]}, {"name": "📱 Mobile Sync", "item": [{"name": "Sync Mobile Data", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{user_id}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"patients\": [\n    {\n      \"mobile_id\": \"local_123\",\n      \"firstname\": \"Mobile\",\n      \"lastname\": \"Patient\",\n      \"tel\": \"+22912345000\",\n      \"sexe\": \"M\",\n      \"birth_date\": \"1985-01-01\",\n      \"address\": \"Mobile Street\",\n      \"is_child\": false\n    }\n  ],\n  \"pregnancies\": [],\n  \"appointments\": []\n}"}, "url": {"raw": "{{base_url}}/api/sync-mobile-data/", "host": ["{{base_url}}"], "path": ["api", "sync-mobile-data", ""]}}}]}]}