import json
import os
import random

from django.http import Http404, HttpResponseForbidden, JsonResponse
from django.shortcuts import redirect, render,get_object_or_404,HttpResponseRedirect
from django.contrib.auth.models import Group, User
from django.contrib.auth import authenticate
from django.core import serializers
from django.contrib import messages
from datetime import datetime
# relative import of forms
from django.contrib.auth.hashers import check_password
from ptcapp.models import Profile, Service, Hospital
from ptcapp.forms.profile import ProfileForm
from django import forms
from ptcapp.models.language import Language
from ptcapp.models.map_doctor_patient import MapDoctorPatient
from ptcapp.models.map_mother_child import MapMotherChild
from ptcapp.models.mother_pregnancy import MotherPregnancy
from ptcapp.models.pregnancy import Pregnancy
from ptcapp.models.profile_other_information import OtherInformation

from ptcapp.models.speciality import Speciality
from ptcapp.views.views import profile
from ptcapp.helpers.authentification_helper import authenticated_user, is_admin, is_assistant, is_doctor
from django.core.validators import validate_email
from django.core.exceptions import ValidationError
from ptcapp.services.email_service import EmailNotificationService
from ptcapp.models.password_reset import UserPasswordStatus

def validate_user_email(email):
    """
    Valide le format et l'unicité d'un email pour la création d'utilisateur.
    
    Args:
        email (str): L'adresse email à valider
        
    Returns:
        tuple: (is_valid, error_message)
    """
    if not email or email.strip() == '':
        return False, "L'adresse email est obligatoire"
    
    email = email.strip().lower()
    
    # Validation du format
    try:
        validate_email(email)
    except ValidationError:
        return False, "Format d'adresse email invalide"
    
    # Validation de l'unicité
    if User.objects.filter(email__iexact=email).exists():
        return False, "Cette adresse email est déjà utilisée"
    
    return True, None


def attach_assistant(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    obj = get_object_or_404(Profile, id = id)
    if request.method == 'POST':
        assistant = request.POST['assistant']
        if assistant != '0':
            obj.assistant = get_object_or_404(User, id = assistant) 
        else:
            obj.assistant = None
        obj.save()
        return JsonResponse({'profile':int(obj.id), 'success' : True})
    return JsonResponse({'profile':int(obj.id), 'success' : False})



def set_picture(request, id):
    obj = get_object_or_404(Profile, id = id)
    if request.method == 'POST':
        #img = request.FILES['file']
        now = datetime.now()

        current_time = now.strftime("%m-%d-%y-%H-%M-%S")
        img = request.FILES['imgInp']

        img_extension = os.path.splitext(img.name)[1]
        print(img.name)

        user_folder = 'ptcapp/static/uploads/profile/'
        if not os.path.exists(user_folder):
            os.mkdir(user_folder)

        img_save_path = "ptcapp/static/uploads/profile/profile-"+ str(obj.pk)+ ".jpg"
        with open(img_save_path, 'wb+') as f:
            for chunk in img.chunks():
                f.write(chunk)

        obj.picture = "uploads/profile/profile-"+ str(obj.pk) + ".jpg"
        obj.save()
        return JsonResponse({'profile':int(obj.id), 'success' : True})
    return JsonResponse({'profile':int(obj.id), 'success' : False})

def create_view(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    request.session['new_credential']={}
    if request.method == 'POST':
        try:
            group = get_object_or_404(Group, id=request.POST['group'])  
            lastname = request.POST['lastname']
            firstname = request.POST['firstname']
            address = request.POST['address']
            tel = request.POST['tel']
            sexe = request.POST['sexe']
            email = request.POST['email']

            hospital = get_object_or_404(Hospital, id=request.POST['hospital']) if request.POST.get('hospital') else None
            service = get_object_or_404(Service, id=request.POST['service']) if request.POST.get('service') else None
            speciality = get_object_or_404(Speciality, id=request.POST['speciality']) if request.POST.get('speciality') else None

            if group.name == "admin" or group.name == "docteur" or group.name == "assistant":
                #CREATE USER
                username = group.name[0:3].upper()+"-"+str(random.randint(10000000, 99999999))
                password = User.objects.make_random_password()
                user = User()
                user.username = username
                user.email = email
                user.set_password(password)
                user.save()

                #ADD USER TO GROUP
                user.groups.add(group)

                #CREATE PROFILE
                profile = Profile()
                profile.user = user
                profile.firstname=firstname
                profile.lastname=lastname
                profile.tel=tel
                profile.sexe=sexe
                # profile.email=email  # CORRIGÉ: Champ email n'existe pas dans le modèle Profile
                profile.address=address
                profile.created_by = request.user

                profile.service = service
                profile.hospital = hospital
                profile.speciality = speciality

                profile.save()

                # Créer le statut de changement de mot de passe obligatoire
                UserPasswordStatus.objects.create(
                    user=user,
                    must_change_password=True
                )

                # Envoyer l'email de bienvenue avec les identifiants
                role_name = EmailNotificationService.get_role_display_name(user)
                success, message = EmailNotificationService.send_new_user_credentials(
                    user, password, role_name
                )

                request.session['new_credential'] ['username']= user.username
                request.session['new_credential'] ['password']= password

                if success:
                    messages.info(request, f"Agent ajouté avec succès. Un email avec les identifiants a été envoyé à {email}.")
                else:
                    messages.warning(request, f"Agent ajouté avec succès. Attention: {message}")
            
            return redirect("/admin/")
        except Exception as e:
            return JsonResponse({"success":True, "error":json.dumps(e)}, safe=False) 

    return HttpResponseRedirect("/")

def createPatient(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user) and not is_assistant(request.user) and not is_admin(request.user): return HttpResponseForbidden()
    if request.method == 'POST':
        try:
            group = get_object_or_404(Group, name='patient')
            lastname = request.POST['lastname']
            firstname = request.POST['firstname']
            address = request.POST['address']
            tel = request.POST['tel']
            sexe = request.POST['sexe']
            email = request.POST['email']

            language = get_object_or_404(Language, id=request.POST['language'])
            birth_date = request.POST['birth_date']
            occupation = request.POST['occupation']
            assurance = request.POST['assurance']
            special_marks = request.POST['special_marks']
            # allergies = request.POST['allergies']
            vaccinations = request.POST['vaccinations']
            # family_history = request.POST['family_history']
            # personal_history = request.POST['personal_history']
            other_informations = dict(zip(request.POST.getlist('keys[]'),request.POST.getlist('values[]')))

            study_level = request.POST.get('study_level',"")
            husband_name = request.POST['husband_name']
            pregnancy_term = request.POST['pregnancy_term']
            pregnancy_end = request.POST['pregnancy_end']
            husband_tel = request.POST['husband_tel']

            #CREATE USER
            username = group.name[0:3].upper()+"-"+str(random.randint(10000000, 99999999))
            password = User.objects.make_random_password()

            user = User()
            user.username = username
            user.email = email
            user.set_password(password)
            user.save()

            #ADD USER TO GROUP
            user.groups.add(group)

            #CREATE PROFILE
            profile = Profile()
            profile.user = user
            profile.firstname=firstname
            profile.lastname=lastname
            profile.tel=tel
            profile.sexe=sexe
            # profile.email=email  # CORRIGÉ: Champ email n'existe pas dans le modèle Profile
            profile.address=address
            if request.POST["group"] == 'child':
                profile.child = True
            else:
                profile.child = False
            profile.created_by = request.user

            profile.language = language
            profile.birth_date=birth_date
            profile.occupation=occupation
            profile.assurance=assurance
            profile.special_marks=special_marks
            # profile.personal_history=personal_history
            # profile.family_history=family_history
            # profile.allergies=allergies
            profile.vaccinations=vaccinations
            profile.husband_name = husband_name
            profile.husband_tel = husband_tel
            profile.study_level = study_level
            profile.husband_name = husband_name
            profile.husband_tel = husband_tel
            profile.study_level = study_level

            profile.save()

            # Créer le statut de changement de mot de passe obligatoire
            UserPasswordStatus.objects.create(
                user=user,
                must_change_password=True
            )

            # Envoyer l'email de bienvenue avec les identifiants
            role_name = EmailNotificationService.get_role_display_name(user)
            success, message = EmailNotificationService.send_new_user_credentials(
                user, password, role_name
            )

            if request.POST.get('pregnancy_state') == "on" :
                pregnancy = Pregnancy()
                pregnancy.state = "ongoing"
                pregnancy.start_date = request.POST["pregnancy_start"]
                pregnancy.situation = request.POST["pregnancy_priority"]
                pregnancy.description = request.POST["pregnancy_other_information"]
                pregnancy.term = pregnancy_term
                pregnancy.end_date = pregnancy_end
                pregnancy.save()

                mother_pregnancy = MotherPregnancy()
                mother_pregnancy.mother = profile
                mother_pregnancy.pregnancy = pregnancy
                mother_pregnancy.save()

            doc_pat = MapDoctorPatient()
            doc_pat.doctor = get_object_or_404(Profile, id=request.POST['doctor']) if request.POST.get('doctor') else get_object_or_404(Profile, user=request.user)
            doc_pat.patient = profile
            doc_pat.save()

            if request.POST["group"] == 'child':
                child_mother = MapMotherChild()
                child_mother.mother = get_object_or_404(Profile, id=request.POST["mother"])
                child_mother.child = profile
                child_mother.save()


            for key in other_informations:
                other_information = OtherInformation.objects.create()
                other_information.profile = profile
                other_information.key = key
                other_information.value = other_informations[key]
                other_information.save()

            if request.POST["group"] != 'child':
                request.session['new_credential']={}
                request.session['new_credential'] ['username']= user.username
                request.session['new_credential'] ['password']= password

            # Message de succès avec information sur l'email
            if success:
                messages.info(request, f"Patient ajouté avec succès. Un email avec les identifiants a été envoyé à {email}.")
            else:
                messages.warning(request, f"Patient ajouté avec succès. Attention: {message}")
            if is_doctor(request.user): 
                return redirect("/docteur/")
            if is_assistant(request.user):
                return redirect("/assistant/")
            if is_admin(request.user):
                return redirect("/admin/")
        except Exception as e:
            return JsonResponse({"success":True, "error":json.dumps(e)}, safe=False) 

    return HttpResponseRedirect("/")

def updatePatient(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_doctor(request.user) and not is_assistant(request.user) and not is_admin(request.user): return HttpResponseForbidden()
    profile = get_object_or_404(Profile, id = id)
 
    if request.method == 'POST':
        try:
            group = get_object_or_404(Group, name="patient") 
            lastname = request.POST['lastname']
            firstname = request.POST['firstname']
            address = request.POST['address']
            tel = request.POST['tel']
            sexe = request.POST['sexe']
            email = request.POST['email']
            
            language = get_object_or_404(Language, id=request.POST['language']) 
            birth_date = request.POST['birth_date']
            occupation = request.POST['occupation']
            assurance = request.POST['assurance']
            special_marks = request.POST['special_marks']
            # allergies = request.POST['allergies']
            vaccinations = request.POST['vaccinations']
            # family_history = request.POST['family_history']
            # personal_history = request.POST['personal_history']
            study_level = request.POST['study_level']
            husband_name = request.POST['husband_name']
            pregnancy_term = request.POST['pregnancy_term']
            pregnancy_end = request.POST['pregnancy_end']
            husband_tel = request.POST['husband_tel']
            other_informations = dict(zip(request.POST.getlist('keys[]'),request.POST.getlist('values[]')))

            #uppdate group if needed
            user = profile.user
            if request.POST.get('modify_id_password') == "on" :
                request.session['new_credential']={}
                username = group.name[0:3].upper()+"-"+str(random.randint(10000000, 99999999))
                password = User.objects.make_random_password()
                user.username = username
                user.email = email
                user.set_password(password)
                user.save()
                request.session['new_credential'] ['username']= user.username
                request.session['new_credential'] ['password']= password

                #uodate GROUP
                user.groups.clear()
                user.groups.add(group)

            #Modify PROFILE
            profile.user = user
            profile.firstname=firstname
            profile.lastname=lastname
            profile.tel=tel
            profile.sexe=sexe
            # profile.email=email  # CORRIGÉ: Champ email n'existe pas dans le modèle Profile
            profile.address=address

            profile.language = language
            profile.birth_date=birth_date
            profile.occupation=occupation
            profile.assurance=assurance
            profile.special_marks=special_marks
            # profile.personal_history=personal_history
            # profile.family_history=family_history
            # profile.allergies=allergies
            profile.vaccinations=vaccinations

            profile.save()

            pregnancy = Pregnancy.objects.filter(motherpregnancy__mother=profile, state=True).first()
            if not profile.child and pregnancy:
                pregnancy.state = "ongoing"
                pregnancy.start_date = request.POST["pregnancy_start"]
                pregnancy.situation = request.POST["pregnancy_priority"]
                pregnancy.description = request.POST["pregnancy_other_information"]
                pregnancy.term = pregnancy_term
                pregnancy.end_date = pregnancy_end
                pregnancy.save()

            if request.POST["group"] == 'child':
                child_mother = MapMotherChild.objects.filter(child=profile).first()
                child_mother.mother = get_object_or_404(Profile, id=request.POST["mother"])
                child_mother.child = profile
                child_mother.save()


            OtherInformation.objects.filter(profile=profile).delete()
            for key in other_informations:
                other_information = OtherInformation.objects.create()
                other_information.profile = profile
                other_information.key = key
                other_information.value = other_informations[key]
                other_information.save()

            messages.success(request, "Patient mis à jour avec succès")
            if is_doctor(request.user): 
                return redirect("/docteur/")
            if is_assistant(request.user):
                return redirect("/assistant/")
            if is_admin(request.user):
                return redirect("/admin/patients")
        except Exception as e:
            return JsonResponse({"success":True, "error":json.dumps(e)}, safe=False) 

    return HttpResponseRedirect("/index")
    


def upload(request):
    img = request.FILES['avatar']
    img_extension = os.path.splitext(img.name)[1]

    user_folder = 'static/profile/' + str(request.session['user_id'])
    if not os.path.exists(user_folder):
        os.mkdir(user_folder)

    img_save_path = "%s/%s%s", user_folder, 'avatar', img_extension
    with open(img_save_path, 'wb+') as f:
        for chunk in img.chunks():
            f.write(chunk)


def list_view(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    context ={}
 
    context["dataset"] = Profile.objects.all()
    return render(request, "profile/list.html", context)


def detail_view(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    
    context ={}
 
    #context["data"] = Profile.objects.get(id = id)
         
    return render(request, "profile/detail.html", context)


def update_view(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    profile = get_object_or_404(Profile, id = id)
 
    if request.method == 'POST':
        try:
            group = get_object_or_404(Group, id=request.POST['group']) 
            lastname = request.POST['lastname']
            firstname = request.POST['firstname']
            address = request.POST['address']
            tel = request.POST['tel']
            sexe = request.POST['sexe']
            email = request.POST['email']
            
            if group.name == "admin" or group.name == "docteur" or group.name == "assistant":
                hospital = get_object_or_404(Hospital, id=request.POST['hospital']) if request.POST.get('hospital') else None
                service = get_object_or_404(Service, id=request.POST['service']) if request.POST.get('service') else None
                speciality = get_object_or_404(Speciality, id=request.POST['speciality']) if request.POST.get('speciality') else None
            else :
                group.name == "patient"
                language = request.POST['language']
                birth_date = request.POST['birth_date']
                occupation = request.POST['occupation']
                assurance = request.POST['assurance']
                special_marks = request.POST['special_marks']
                allergies = request.POST['allergies']
                vaccinations = request.POST['vaccinations']
                family_history = request.POST['family_history']
                personal_history = request.POST['personal_history']
                other_informations = dict(zip(request.POST.getlist('keys[]'),request.POST.getlist('values[]')))

            #uppdate group if needed
            user = profile.user
            if request.POST.get('modify_id_password') == "on" :
                request.session['new_credential']={}
                username = group.name[0:3].upper()+"-"+str(random.randint(10000000, 99999999))
                password = User.objects.make_random_password()
                user.username = username
                user.email = email
                user.set_password(password)
                user.save()
                request.session['new_credential'] ['username']= user.username
                request.session['new_credential'] ['password']= password

            #uodate GROUP
            user.groups.clear()
            user.groups.add(group)

            #Modify PROFILE
            profile.user = user
            profile.firstname=firstname
            profile.lastname=lastname
            profile.tel=tel
            profile.sexe=sexe
            # profile.email=email  # CORRIGÉ: Champ email n'existe pas dans le modèle Profile
            profile.address=address

            if group.name == "admin" or group.name == "docteur" or group.name == "assistant":
                profile.service = service
                profile.hospital = hospital
                profile.speciality = speciality

            if group.name == "patient":
                profile.language = language
                profile.birth_date=birth_date
                profile.occupation=occupation
                profile.assurance=assurance
                profile.special_marks=special_marks
                profile.personal_history=personal_history
                profile.family_history=family_history
                profile.allergies=allergies
                profile.vaccinations=vaccinations

            profile.save()
            if group.name == "patient":
                #CREATE Other Informations

                for key in other_informations:
                    other_information = OtherInformation.objects.create()
                    other_information.profile = profile
                    other_information.key = key
                    other_information.value = other_information[key]
                    other_information.save()
            messages.success(request, "Agent mis à jour avec succès")
            return redirect("/admin/")
        except Exception as e:
            return JsonResponse({"success":True, "error":json.dumps(e)}, safe=False) 

    return HttpResponseRedirect("/index")
    

def delete_view(request, id):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    profile = get_object_or_404(Profile, id = id)

    user = profile.user
 
    if request.method =="POST":
        profile.delete()
        user.groups.clear()
        user.delete()
        return JsonResponse({"success":True}, safe=False) 

    return JsonResponse({"success":False}, safe=False) 

def archive(request, id):
    if not authenticated_user(request.user): return redirect('/')
    profile = get_object_or_404(Profile, id = id)
 
    if request.method =="POST":
        profile.archived = True
        profile.save()
        return JsonResponse({"success":True}, safe=False) 

    return JsonResponse({"success":False}, safe=False) 

def unarchive(request, id):
    if not authenticated_user(request.user): return redirect('/')
    profile = get_object_or_404(Profile, id = id)
 
    if request.method =="POST":
        profile.archived = False
        profile.save()
        return JsonResponse({"success":True}, safe=False) 

    return JsonResponse({"success":False}, safe=False) 


def uploadPP(request):
    if not authenticated_user(request.user): return redirect('/')
    if not is_admin(request.user): return HttpResponseForbidden()
    pass

def change_credential(request):
    if not authenticated_user(request.user): return redirect('/')
    
    currentpassword = request.user.password 
    if request.method == "POST" :
        user = request.user
        matchcheck= check_password(request.POST["old_password"], currentpassword)
        if matchcheck:
            password = request.POST['password']
            user.set_password(password)
            user.save()
            return JsonResponse({"success":True}, safe=False) 
        return JsonResponse({"success":False}, safe=False) 
    return Http404()