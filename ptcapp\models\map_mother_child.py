from django.db import models
from .profile import Profile


class MapMotherChild(models.Model):
    mother = models.ForeignKey(Profile, related_name='mère', null = True, on_delete=models.SET_NULL)
    child = models.ForeignKey(Profile, related_name='enfant', null = True, on_delete=models.SET_NULL)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)