from .. import views
from django.urls import path

urlpatterns = [
    path('', views.views.assistantIndex, name='assistant.index'),
    path('archives/', views.views.assistantArchives, name='assistant.archives'),
    path('grossesses/', views.views.assistantPregnancies, name='assistant.pregnancy'),
    # path('patients/', views.views.doctorPatients, name='doctor.patients'),
    path('patients', views.views.assistantFilters, name='assistant.filters'),
    path('archives', views.views.assistantArchiveFilters, name='assistant.archivefilters'),
    path('appointment', views.views.assistantAppointmentFilters, name='assistant.appointmentfilters'),
    path('pregnancy', views.views.assistantPregnancyFilters, name='assistant.pregnancyfilters'),
    path('dossier/<id>', views.views.assistantShowRecord, name='assistant.record.show'),
    path('dossier/archive/<id>', views.profile.archive, name='assistant.record.archive'),
    path('dossier/unarchive/<id>', views.profile.unarchive, name='assistant.record.unarchive'),
    path('patient/create', views.views.assistantCreatePatient, name='assistant.patient.create'),
    path('patient/edit/<id>', views.views.assistantEditPatient, name='assistant.patient.edit'),
    path('appointment/', views.views.assistantAppointmentIndex, name='assistant.appointment.index'),
    path('appointment/edit/<id>', views.views.assistantAppointmentEdit, name='assistant.appointment.edit'),
    path('pregnancy/edit/<id>', views.views.assistantPregnancyEdit, name='assistant.pregnancy.edit'),
    path('profil', views.views.assistantProfile, name='assistant.profile'),
    path('appointment/show/<id>', views.views.assistantAppointmentShow, name='assistant.appointment.show'),
    
    path('attached-doctor', views.views.attached_doctor, name='profile.attached')
]