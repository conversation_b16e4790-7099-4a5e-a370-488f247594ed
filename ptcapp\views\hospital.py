from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect, render,get_object_or_404,HttpResponseRedirect
import datetime

# relative import of forms
from ptcapp.models import Hospital
from ptcapp.forms.hospital import HospitalForm


def create_view(request):
    if request.method == 'POST':
        name = request.POST['name']
        contact = request.POST['contact']
        address = request.POST['address']
        hospital = Hospital.objects.create(name=name,tel=contact,address=address)
        hospital.save()
        # create_services.apply_async((hospital.id,), eta=datetime.datetime(2022, 5, 27, 9, 30)) #dans le eta (annee, mois, date, heure(utc), minute)

        return JsonResponse({'hospital':int(hospital.id), 'success' : True})
    return JsonResponse({'hospital':int(hospital.id), 'success' : False})


def update_view(request, id):
    obj = get_object_or_404(Hospital, id = id)
    
    if request.method == 'POST':
        obj.name = request.POST['name']
        obj.tel = request.POST['contact']
        obj.address = request.POST['address']
        obj.save()
        return JsonResponse({'hospital':int(obj.id), 'success' : True})
    return JsonResponse({'hospital':int(obj.id), 'success' : False})
    
def delete_view(request, id):
    obj = get_object_or_404(Hospital, id = id)
 
    if request.method =="POST":
        obj.delete()
        return JsonResponse({'success' : True})
    return JsonResponse({'hospital':int(obj.id), 'success' : False})
