{% extends 'doctor/layout.html' %} 
{% load static %}
{% load layout %}

{% block up-style %}
    <!-- DataTables -->
    <link href="{% static 'libs/select2/css/select2.min.css' %}" rel="stylesheet" type="text/css" />
    <link href="{% static 'libs/dropzone/min/dropzone.min.css' %}" rel="stylesheet" type="text/css" />
    <style>
        fieldset{
            border: 1px solid !important;
            padding: 10px !important;
            height: 100% !important;
        }
        fieldset div label {
            width: 50% !important;
        }
        legend{
            padding-right: 10px !important;
            padding-left: 10px !important;
            display: inline-block;
            position: relative;
            bottom: 30px;
            background-color: white;
            width: fit-content !important;
        }

        label.required::after{
            content: "*";
            color: red;
            padding-left: 3px
        }
    </style>
{% endblock up-style %}

{% block content %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white text-left">
                    <span class="h2 text-capitalize">Modification consultation</span>
                </div>
                <div class="card-body">
                    <form action="{% url 'appointment.update' id=appointment.id %}" id="appointment-edit-form" method="POST">
                        {% csrf_token %}
                        {% comment %} <div class="row">
                            <div class="d-flex  justify-content-end">
                                <div class="form-check mx-2">
                                    <label for="current_doc" class="form-check-label required">Dossier actuel</label>
                                    <input class="form-check-input" type="radio" name="group" value="current_doc" id="current_doc" checked>
                                </div>
                                <div class="form-check mx-2">
                                    <label for="new_doc" class="form-check-label required">Nouveau Dossier</label>
                                    <input class="form-check-input" type="radio" name="group" value="new_doc" id="new_doc">
                                </div>
                            </div>
                        </div> {% endcomment %}
                        <div class="row">
                            <div class="col-md-6 col-sm-12 mt-4">
                                <fieldset class="h-100">
                                    <legend>Pré Consultation</legend>
                                    {% comment %} <div class="d-flex align-items-center my-3">
                                        <label for="num_record" class="form-label required">Numéro du dossier</label>
                                        <input class="form-control" type="text" name="num_record" value="DOS-47852" id="num_record" required="required" disabled>
                                        <input class="form-control" type="hidden" name="current_num_record" value="DOS-47852" id="current_num_record" required="required">
                                    </div> {% endcomment %}
                                    <div class="d-flex align-items-center my-3">
                                        <label for="appointment_patient" class="form-label required">Sélectionner patient</label>
                                        <select class="select2 form-control" name="appointment_patient" id="appointment_patient" required="required">*
                                            {% for patient in patients %}
                                            {% if patient %}
                                            <option value="{{patient.id}}" {% if appointment.patient_id == patient.id %}selected{% endif %}>{{patient.user|auth_fullname}}</option>
                                            {% endif %}
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="type_appointment" class="form-label required">Type de consultation</label>
                                        <input class="form-control" type="text" name="type_appointment" placeholder="Type de consultation" id="type_appointment" required="required" value="{{appointment.appointment_type}}">
                                    </div>
                                    {% comment %} <div class="d-flex align-items-center my-3">
                                        <label for="illness" class="form-label required">Maux ressentis</label>
                                        <input class="form-control" type="text" name="illness" placeholder="Maux ressentis" id="illness" required="required" va>
                                    </div> {% endcomment %}
                                    <div class="d-flex align-items-center my-3">
                                        <label for="consul_date" class="form-label required">Date de la Consultaion</label>
                                        <input class="form-control" type="date" name="consul_date" placeholder="Date d'inscriptation" id="consul_date" required value="{{appointment.consul_date|date:"Y-m-d"}}">
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="consul_time" class="form-label required">Heure de la Consultaion</label>
                                        <input class="form-control" type="time" name="consul_time" placeholder="Date d'inscriptation" id="consul_time" required value="{{appointment.consul_hour|date:"H:i"}}">
                                    </div>
                                </fieldset>
                            </div>
                            <div class="col-md-6 col-sm-12 mt-4">
                                <fieldset>
                                    <legend>Post Constultation</legend>
                                    <div class="d-flex align-items-start my-3">
                                        <label for="state" class="form-label">Statut</label>
                                        <select class="form-control select2" name="state" id="state">
                                            <option value="pending" {% if appointment.state == "pending" %}selected{% endif %}>En attente</option>
                                            <option value="canceled" {% if appointment.state == "canceled" %}selected{% endif %}>Annulée</option>
                                            <option value="done" {% if appointment.state == "done" %}selected{% endif %}>Effectué</option>
                                            <option value="missed" {% if appointment.state == "missed" %}selected{% endif %}>Manqué</option>
                                        </select>
                                    </div>
                                    <div class="d-flex align-items-start my-3">
                                        <label class="mb-1">Données significatives relevées</label>
                                        <textarea id="textarea" class="form-control" maxlength="" name="consul_data" rows="3" placeholder="Données significatives relevées">{{appointment.consul_data}}</textarea>
                                    </div>
                                    <div class="d-flex align-items-start my-3">
                                        <label class="mb-1">Synthèse de la consultation</label>
                                        <textarea id="textarea" class="form-control" maxlength="" name="consul_resume" rows="3" placeholder="Synthèse de la consultation">{{appointment.consul_resume}}</textarea>
                                    </div>
                                    <div class="d-flex align-items-start my-3">
                                        <label class="mb-1">Décisions</label>
                                        <textarea id="textarea" class="form-control" maxlength="" name="consul_decisions" rows="3" placeholder="Décisions">{{appointment.consul_decisions}}</textarea>
                                    </div>
                                </fieldset>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-12">
                                <div class="dropzone">
                                    <div class="fallback">
                                        <input name="file" type="file" multiple="multiple">
                                    </div>
                                    <div class="dz-message needsclick">
                                        <div class="mb-3">
                                            <i class="display-4 text-muted ri-upload-cloud-2-line"></i>
                                        </div>
                                        
                                        <h4>Télécharger les documents</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <span class="text-danger"><i class=" ri-information-fill"></i> Nous suggérons les fichiers de type .jpg, .jpeg et .png </span>
                        <div class="row mt-2">
                            <div class="col">
                                <input id="submit-form" class="btn btn-success w-auto" type="submit" value="Sauvegarder">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}

{% block down-script %}
    <script src="{% static 'libs/select2/js/select2.min.js'%}"></script>
    <script src="{% static 'libs/dropzone/min/dropzone.min.js'%}"></script>
    <script type="text/javascript">

        Dropzone.autoDiscover = false;

        // init dropzone on id (form or div)
        $(document).ready(function() {
            $(document).ready(function () {
                myDropzone = new Dropzone(".dropzone", {
                    maxFiles: 2000,
                    url: "{% url 'appointment.files' id=appointment.id %}",
                    autoProcessQueue: false,
                    uploadMultiple: true, // uplaod files in a single request
                    parallelUploads: 100, // use it with uploadMultiple
                    addRemoveLinks: true,
                    headers: {
                        'X-CSRFToken': $('input[name="csrfmiddlewaretoken"]').val()
                    },
                    successmultiple: function(file){
                        $("#appointment-edit-form").submit();
                    }
                });
            })
        });
        
        $("#submit-form").on("click", function(e) {
            // Make sure that the form isn't actually being sent.
            e.preventDefault();
            e.stopPropagation();
            
            if (myDropzone.files != "") {
                myDropzone.processQueue();
            } else {
                $("#appointment-edit-form").submit();
            }

        });
     
    </script>
    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        $('.select2').select2()
        $('.remove').on('click', function(e){
            e.preventDefault()
            $('.other-informations-body').children().last().remove();
        })

        $('#new_doc').click(function(){
            $('#num_record').val('DOS-')
            $('#num_record').attr('disabled', false)
        })

        $('#current_doc').click(function(){
            $('#num_record').val($('#current_num_record').val())
            $('#num_record').attr('disabled', true)
        })


    </script>
{% endblock down-script %}