# PTC Care Authentication API Documentation

## 📱 Mobile Integration Guide

Cette documentation complète couvre tous les endpoints d'authentification de l'API PTC Care pour l'intégration mobile.

---

## 🔐 Authentication Endpoints

### 1. **User Login**

#### **Endpoint Details**
- **Method**: `POST`
- **URL**: `/api/login/`
- **Authentication**: None (public endpoint)
- **Content-Type**: `application/json`

#### **Request Specification**
```json
{
  "email": "<EMAIL>",
  "password": "userPassword123"
}
```

**Required Fields:**
- `email` (string): User email address or username
- `password` (string): User password

**Optional Fields:**
- `username` (string): Alternative to email for backward compatibility

#### **Success Response (200)**
```json
{
  "status": "success",
  "user_id": 123,
  "username": "PAT-12345678",
  "profile_id": 456,
  "role": "patient",
  "name": "<PERSON>",
  "hospital_id": 1,
  "service_id": 2,
  "speciality_id": null,
  "must_change_password": false,
  "password_reset_token": "abc123...xyz789"
}
```

**Response Fields:**
- `status` (string): "success"
- `user_id` (integer): Unique user identifier
- `username` (string): Generated username
- `profile_id` (integer): User profile ID
- `role` (string): User role ("admin", "agent", "patient")
- `name` (string): Full name
- `hospital_id` (integer|null): Associated hospital ID
- `service_id` (integer|null): Associated service ID
- `speciality_id` (integer|null): Associated speciality ID
- `must_change_password` (boolean): Password change requirement
- `password_reset_token` (string|optional): Token for password change

#### **Error Responses**
```json
// 400 - Bad Request
{
  "error": "Adresse email et mot de passe requis"
}

// 401 - Unauthorized
{
  "error": "Adresse email ou mot de passe incorrect"
}

// 404 - Not Found
{
  "error": "Profil introuvable"
}

// 500 - Internal Server Error
{
  "error": "Erreur interne du serveur"
}
```

#### **Mobile Integration Example**

**Android (Kotlin):**
```kotlin
data class LoginRequest(
    val email: String,
    val password: String
)

data class LoginResponse(
    val status: String,
    val user_id: Int,
    val username: String,
    val profile_id: Int,
    val role: String,
    val name: String,
    val hospital_id: Int?,
    val service_id: Int?,
    val speciality_id: Int?,
    val must_change_password: Boolean,
    val password_reset_token: String?
)

suspend fun login(email: String, password: String): LoginResponse {
    val response = apiClient.post<LoginResponse>(
        "/api/login/",
        LoginRequest(email, password)
    )
    
    // Handle password change requirement
    if (response.must_change_password && response.password_reset_token != null) {
        // Redirect to password change screen
        navigateToPasswordChange(response.password_reset_token)
    }
    
    return response
}
```

**iOS (Swift):**
```swift
struct LoginRequest: Codable {
    let email: String
    let password: String
}

struct LoginResponse: Codable {
    let status: String
    let user_id: Int
    let username: String
    let profile_id: Int
    let role: String
    let name: String
    let hospital_id: Int?
    let service_id: Int?
    let speciality_id: Int?
    let must_change_password: Bool
    let password_reset_token: String?
}

func login(email: String, password: String) async throws -> LoginResponse {
    let request = LoginRequest(email: email, password: password)
    let response: LoginResponse = try await apiClient.post("/api/login/", body: request)
    
    // Handle password change requirement
    if response.must_change_password, let token = response.password_reset_token {
        // Navigate to password change screen
        await navigateToPasswordChange(token: token)
    }
    
    return response
}
```

---

### 2. **Password Change (Current Password)**

#### **Endpoint Details**
- **Method**: `POST`
- **URL**: `/api/change-password/`
- **Authentication**: None (uses current password)
- **Content-Type**: `application/json`

#### **Request Specification**
```json
{
  "email": "<EMAIL>",
  "current_password": "oldPassword123",
  "new_password": "newPassword456"
}
```

**Required Fields:**
- `email` (string): User email address
- `current_password` (string): Current password for verification
- `new_password` (string): New password (must meet complexity requirements)

#### **Success Response (200)**
```json
{
  "status": "success",
  "message": "Mot de passe changé avec succès"
}
```

#### **Error Responses**
```json
// 400 - Bad Request
{
  "error": "Email et nouveau mot de passe requis"
}

// 400 - Password Validation Error
{
  "error": "Mot de passe non valide",
  "details": [
    "Le mot de passe doit contenir au moins 8 caractères",
    "Le mot de passe doit contenir au moins une majuscule"
  ]
}

// 400 - Current Password Incorrect
{
  "error": "Mot de passe actuel incorrect"
}

// 404 - User Not Found
{
  "error": "Utilisateur non trouvé"
}
```

#### **Password Requirements**
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one digit
- At least one special character (!@#$%^&*)

---

### 3. **Password Reset Request**

#### **Endpoint Details**
- **Method**: `POST`
- **URL**: `/api/request-password-reset/`
- **Authentication**: None (public endpoint)
- **Content-Type**: `application/json`
- **Rate Limiting**: 3 requests per email per hour

#### **Request Specification**
```json
{
  "email": "<EMAIL>"
}
```

**Required Fields:**
- `email` (string): User email address

#### **Success Response (200)**
```json
{
  "status": "success",
  "message": "Si cette adresse email existe, un lien de réinitialisation a été envoyé"
}
```

**Note**: The same response is returned regardless of whether the email exists (security feature to prevent email enumeration).

#### **Error Responses**
```json
// 400 - Bad Request
{
  "error": "Adresse email requise"
}

// 400 - Invalid Email Format
{
  "error": "Format d'adresse email invalide"
}

// 500 - Internal Server Error
{
  "error": "Erreur interne du serveur"
}
```

#### **Rate Limiting**
- **Limit**: 3 requests per email per hour
- **Response**: Same success message when limit exceeded
- **Reset**: Counter resets after 1 hour

---

### 4. **Password Change with Token**

#### **Endpoint Details**
- **Method**: `POST`
- **URL**: `/api/change-password/`
- **Authentication**: Token-based
- **Content-Type**: `application/json`

#### **Request Specification**
```json
{
  "email": "<EMAIL>",
  "token": "abc123def456ghi789...",
  "new_password": "newPassword456"
}
```

**Required Fields:**
- `email` (string): User email address
- `token` (string): Password reset token (64 characters)
- `new_password` (string): New password

#### **Success Response (200)**
```json
{
  "status": "success",
  "message": "Mot de passe changé avec succès"
}
```

#### **Error Responses**
```json
// 400 - Invalid Token
{
  "error": "Token invalide ou expiré"
}

// 404 - Token Not Found
{
  "error": "Token non trouvé"
}

// 400 - Password Validation Error
{
  "error": "Mot de passe non valide",
  "details": ["Validation error messages"]
}
```

#### **Token Properties**
- **Length**: 64 characters
- **Expiration**: 48 hours
- **Usage**: Single use only
- **Security**: Cryptographically secure generation

---

## 🔧 Mobile Integration Patterns

### **Authentication Flow**

#### **1. Standard Login Flow**
```javascript
// 1. User enters credentials
const loginData = { email, password };

// 2. Call login API
const response = await fetch('/api/login/', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(loginData)
});

const userData = await response.json();

// 3. Handle response
if (userData.must_change_password) {
  if (userData.password_reset_token) {
    // Direct password change with token
    navigateToPasswordChange(userData.password_reset_token);
  } else {
    // Request new token
    await requestPasswordReset(userData.email);
  }
} else {
  // Normal login - save user data and navigate to main screen
  await saveUserData(userData);
  navigateToMainScreen();
}
```

#### **2. Password Reset Flow**
```javascript
// 1. User requests password reset
await fetch('/api/request-password-reset/', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email })
});

// 2. User receives email with token
// 3. User opens app with deep link containing token
// 4. App extracts token and navigates to password change

// 5. Change password with token
await fetch('/api/change-password/', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    email,
    token,
    new_password
  })
});
```

### **Error Handling Best Practices**

#### **Network Error Handling**
```kotlin
// Android Example
sealed class ApiResult<T> {
    data class Success<T>(val data: T) : ApiResult<T>()
    data class Error<T>(val message: String, val code: Int? = null) : ApiResult<T>()
    data class NetworkError<T>(val exception: Throwable) : ApiResult<T>()
}

suspend fun safeApiCall(apiCall: suspend () -> Response): ApiResult<LoginResponse> {
    return try {
        val response = apiCall()
        if (response.isSuccessful) {
            ApiResult.Success(response.body()!!)
        } else {
            val errorBody = response.errorBody()?.string()
            val errorMessage = parseErrorMessage(errorBody)
            ApiResult.Error(errorMessage, response.code())
        }
    } catch (e: IOException) {
        ApiResult.NetworkError(e)
    } catch (e: Exception) {
        ApiResult.Error("Erreur inattendue: ${e.message}")
    }
}
```

#### **Token Management**
```swift
// iOS Example
class TokenManager {
    private let keychain = Keychain(service: "com.ptccare.app")
    
    func savePasswordResetToken(_ token: String) {
        keychain["password_reset_token"] = token
    }
    
    func getPasswordResetToken() -> String? {
        return keychain["password_reset_token"]
    }
    
    func clearPasswordResetToken() {
        keychain["password_reset_token"] = nil
    }
    
    func isTokenExpired(_ token: String) -> Bool {
        // Check token expiration (48 hours from creation)
        // Implementation depends on token format
        return false
    }
}
```

---

## 📱 Mobile-Specific Considerations

### **Headers and Authentication**

#### **Standard Headers**
```http
Content-Type: application/json
Accept: application/json
User-Agent: PTC-Care-Mobile/1.0 (Platform/Version)
```

#### **Authorization Header (for protected endpoints)**
```http
Authorization: Bearer {user_id}
```

### **Deep Link Handling**

#### **Password Reset Deep Links**
```
ptccare://password-reset?token=abc123def456...
```

#### **Android Deep Link Handling**
```kotlin
// In MainActivity
override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(savedInstanceState)
    
    intent?.data?.let { uri ->
        if (uri.scheme == "ptccare" && uri.host == "password-reset") {
            val token = uri.getQueryParameter("token")
            if (token != null) {
                navigateToPasswordChange(token)
            }
        }
    }
}
```

#### **iOS Deep Link Handling**
```swift
// In SceneDelegate
func scene(_ scene: UIScene, openURLContexts URLContexts: Set<UIOpenURLContext>) {
    guard let url = URLContexts.first?.url else { return }
    
    if url.scheme == "ptccare" && url.host == "password-reset" {
        let components = URLComponents(url: url, resolvingAgainstBaseURL: false)
        if let token = components?.queryItems?.first(where: { $0.name == "token" })?.value {
            navigateToPasswordChange(token: token)
        }
    }
}
```

### **Offline Handling**

#### **Caching Strategy**
```javascript
// Store user data locally for offline access
const userData = {
  user_id: response.user_id,
  username: response.username,
  role: response.role,
  name: response.name,
  must_change_password: response.must_change_password,
  last_login: new Date().toISOString()
};

// Save to secure storage
await SecureStore.setItemAsync('user_data', JSON.stringify(userData));
```

#### **Retry Logic**
```kotlin
// Android retry implementation
class ApiRetryInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        var request = chain.request()
        var response = chain.proceed(request)
        
        var tryCount = 0
        while (!response.isSuccessful && tryCount < 3) {
            tryCount++
            response.close()
            response = chain.proceed(request)
        }
        
        return response
    }
}
```

---

## 🔒 Security Best Practices

### **Password Security**
- Always validate passwords on both client and server
- Use secure password storage (Keychain on iOS, EncryptedSharedPreferences on Android)
- Implement password strength indicators
- Clear sensitive data from memory after use

### **Token Security**
- Store tokens securely (never in plain text)
- Implement token expiration checks
- Clear expired tokens automatically
- Use HTTPS for all API communications

### **Error Handling Security**
- Don't expose sensitive information in error messages
- Log security events for monitoring
- Implement rate limiting on client side
- Use generic error messages for authentication failures

---

## 📊 Rate Limiting and Performance

### **Rate Limits**
- **Login**: No specific limit (implement client-side throttling)
- **Password Reset**: 3 requests per email per hour
- **Password Change**: No specific limit

### **Performance Optimization**
- Cache user data locally
- Implement request timeouts (30 seconds recommended)
- Use connection pooling
- Implement exponential backoff for retries

### **Monitoring and Analytics**
- Track authentication success/failure rates
- Monitor API response times
- Log security events
- Implement crash reporting

---

## 🧪 Testing and Validation

### **Test Scenarios**
1. **Valid login with normal user**
2. **Valid login with password change required**
3. **Invalid credentials**
4. **Password reset request**
5. **Password change with token**
6. **Expired token handling**
7. **Network error scenarios**
8. **Rate limiting behavior**

### **Integration Testing**
```kotlin
// Example test for login flow
@Test
fun testLoginWithPasswordChangeRequired() {
    val loginRequest = LoginRequest("<EMAIL>", "password123")
    val response = authService.login(loginRequest)
    
    assertTrue(response.must_change_password)
    assertNotNull(response.password_reset_token)
    assertEquals("patient", response.role)
}
```

---

---

## 📋 Complete Integration Examples

### **Android Complete Implementation**

#### **AuthService.kt**
```kotlin
class AuthService(private val apiClient: ApiClient) {

    suspend fun login(email: String, password: String): Result<LoginResponse> {
        return try {
            val request = LoginRequest(email, password)
            val response = apiClient.post<LoginResponse>("/api/login/", request)

            // Handle password change requirement
            when {
                response.must_change_password && response.password_reset_token != null -> {
                    Result.PasswordChangeRequired(response, response.password_reset_token)
                }
                response.must_change_password -> {
                    Result.TokenExpired(response)
                }
                else -> {
                    Result.Success(response)
                }
            }
        } catch (e: HttpException) {
            when (e.code()) {
                401 -> Result.InvalidCredentials
                404 -> Result.UserNotFound
                else -> Result.Error(e.message())
            }
        } catch (e: Exception) {
            Result.NetworkError(e)
        }
    }

    suspend fun requestPasswordReset(email: String): Result<String> {
        return try {
            val request = PasswordResetRequest(email)
            val response = apiClient.post<PasswordResetResponse>(
                "/api/request-password-reset/",
                request
            )
            Result.Success(response.message)
        } catch (e: Exception) {
            Result.Error(e.message ?: "Unknown error")
        }
    }

    suspend fun changePasswordWithToken(
        email: String,
        token: String,
        newPassword: String
    ): Result<String> {
        return try {
            val request = ChangePasswordRequest(email, token, newPassword)
            val response = apiClient.post<ChangePasswordResponse>(
                "/api/change-password/",
                request
            )
            Result.Success(response.message)
        } catch (e: HttpException) {
            when (e.code()) {
                400 -> Result.InvalidToken
                else -> Result.Error(e.message())
            }
        } catch (e: Exception) {
            Result.NetworkError(e)
        }
    }
}

sealed class Result<T> {
    data class Success<T>(val data: T) : Result<T>()
    data class PasswordChangeRequired<T>(val user: T, val token: String) : Result<T>()
    data class TokenExpired<T>(val user: T) : Result<T>()
    data class Error<T>(val message: String) : Result<T>()
    data class NetworkError<T>(val exception: Throwable) : Result<T>()
    object InvalidCredentials : Result<Nothing>()
    object UserNotFound : Result<Nothing>()
    object InvalidToken : Result<Nothing>()
}
```

#### **LoginActivity.kt**
```kotlin
class LoginActivity : AppCompatActivity() {
    private lateinit var binding: ActivityLoginBinding
    private lateinit var authService: AuthService

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)

        authService = AuthService(ApiClient())

        binding.loginButton.setOnClickListener {
            performLogin()
        }

        binding.forgotPasswordButton.setOnClickListener {
            showForgotPasswordDialog()
        }
    }

    private fun performLogin() {
        val email = binding.emailEditText.text.toString()
        val password = binding.passwordEditText.text.toString()

        if (email.isBlank() || password.isBlank()) {
            showError("Veuillez remplir tous les champs")
            return
        }

        lifecycleScope.launch {
            binding.progressBar.visibility = View.VISIBLE
            binding.loginButton.isEnabled = false

            when (val result = authService.login(email, password)) {
                is Result.Success -> {
                    saveUserData(result.data)
                    navigateToMainActivity()
                }

                is Result.PasswordChangeRequired -> {
                    navigateToPasswordChange(result.token, result.user)
                }

                is Result.TokenExpired -> {
                    showTokenExpiredDialog(result.user)
                }

                is Result.InvalidCredentials -> {
                    showError("Email ou mot de passe incorrect")
                }

                is Result.UserNotFound -> {
                    showError("Utilisateur non trouvé")
                }

                is Result.Error -> {
                    showError("Erreur: ${result.message}")
                }

                is Result.NetworkError -> {
                    showError("Erreur de connexion. Vérifiez votre connexion internet.")
                }
            }

            binding.progressBar.visibility = View.GONE
            binding.loginButton.isEnabled = true
        }
    }

    private fun navigateToPasswordChange(token: String, user: LoginResponse) {
        val intent = Intent(this, PasswordChangeActivity::class.java).apply {
            putExtra("token", token)
            putExtra("email", user.email)
            putExtra("username", user.username)
        }
        startActivity(intent)
        finish()
    }
}
```

### **iOS Complete Implementation**

#### **AuthService.swift**
```swift
class AuthService {
    private let apiClient: APIClient

    init(apiClient: APIClient = APIClient()) {
        self.apiClient = apiClient
    }

    func login(email: String, password: String) async -> AuthResult {
        do {
            let request = LoginRequest(email: email, password: password)
            let response: LoginResponse = try await apiClient.post("/api/login/", body: request)

            switch (response.must_change_password, response.password_reset_token) {
            case (true, let token?) where !token.isEmpty:
                return .passwordChangeRequired(response, token)
            case (true, _):
                return .tokenExpired(response)
            default:
                return .success(response)
            }
        } catch let error as APIError {
            switch error {
            case .unauthorized:
                return .invalidCredentials
            case .notFound:
                return .userNotFound
            case .badRequest(let message):
                return .error(message)
            default:
                return .error(error.localizedDescription)
            }
        } catch {
            return .networkError(error)
        }
    }

    func requestPasswordReset(email: String) async -> AuthResult {
        do {
            let request = PasswordResetRequest(email: email)
            let response: PasswordResetResponse = try await apiClient.post(
                "/api/request-password-reset/",
                body: request
            )
            return .success(response.message)
        } catch {
            return .error(error.localizedDescription)
        }
    }

    func changePassword(email: String, token: String, newPassword: String) async -> AuthResult {
        do {
            let request = ChangePasswordRequest(
                email: email,
                token: token,
                new_password: newPassword
            )
            let response: ChangePasswordResponse = try await apiClient.post(
                "/api/change-password/",
                body: request
            )
            return .success(response.message)
        } catch let error as APIError {
            switch error {
            case .badRequest:
                return .invalidToken
            default:
                return .error(error.localizedDescription)
            }
        } catch {
            return .networkError(error)
        }
    }
}

enum AuthResult {
    case success(Any)
    case passwordChangeRequired(LoginResponse, String)
    case tokenExpired(LoginResponse)
    case invalidCredentials
    case userNotFound
    case invalidToken
    case error(String)
    case networkError(Error)
}
```

#### **LoginViewController.swift**
```swift
class LoginViewController: UIViewController {
    @IBOutlet weak var emailTextField: UITextField!
    @IBOutlet weak var passwordTextField: UITextField!
    @IBOutlet weak var loginButton: UIButton!
    @IBOutlet weak var activityIndicator: UIActivityIndicatorView!

    private let authService = AuthService()

    @IBAction func loginButtonTapped(_ sender: UIButton) {
        performLogin()
    }

    @IBAction func forgotPasswordTapped(_ sender: UIButton) {
        showForgotPasswordAlert()
    }

    private func performLogin() {
        guard let email = emailTextField.text, !email.isEmpty,
              let password = passwordTextField.text, !password.isEmpty else {
            showAlert(message: "Veuillez remplir tous les champs")
            return
        }

        setLoading(true)

        Task {
            let result = await authService.login(email: email, password: password)

            await MainActor.run {
                setLoading(false)

                switch result {
                case .success(let user as LoginResponse):
                    saveUserData(user)
                    navigateToMainScreen()

                case .passwordChangeRequired(let user, let token):
                    navigateToPasswordChange(user: user, token: token)

                case .tokenExpired(let user):
                    showTokenExpiredAlert(user: user)

                case .invalidCredentials:
                    showAlert(message: "Email ou mot de passe incorrect")

                case .userNotFound:
                    showAlert(message: "Utilisateur non trouvé")

                case .error(let message):
                    showAlert(message: "Erreur: \(message)")

                case .networkError:
                    showAlert(message: "Erreur de connexion. Vérifiez votre connexion internet.")

                default:
                    showAlert(message: "Erreur inattendue")
                }
            }
        }
    }

    private func navigateToPasswordChange(user: LoginResponse, token: String) {
        let storyboard = UIStoryboard(name: "Main", bundle: nil)
        let passwordChangeVC = storyboard.instantiateViewController(
            withIdentifier: "PasswordChangeViewController"
        ) as! PasswordChangeViewController

        passwordChangeVC.configure(email: user.email, token: token, username: user.username)
        navigationController?.pushViewController(passwordChangeVC, animated: true)
    }

    private func setLoading(_ loading: Bool) {
        loginButton.isEnabled = !loading
        if loading {
            activityIndicator.startAnimating()
        } else {
            activityIndicator.stopAnimating()
        }
    }
}
```

---

## 🔧 Advanced Integration Features

### **Biometric Authentication**

#### **Android Biometric Integration**
```kotlin
class BiometricAuthManager(private val context: Context) {

    fun authenticateWithBiometric(
        onSuccess: () -> Unit,
        onError: (String) -> Unit
    ) {
        val biometricPrompt = BiometricPrompt(
            context as FragmentActivity,
            ContextCompat.getMainExecutor(context),
            object : BiometricPrompt.AuthenticationCallback() {
                override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                    onSuccess()
                }

                override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                    onError(errString.toString())
                }
            }
        )

        val promptInfo = BiometricPrompt.PromptInfo.Builder()
            .setTitle("Authentification PTC Care")
            .setSubtitle("Utilisez votre empreinte digitale pour vous connecter")
            .setNegativeButtonText("Annuler")
            .build()

        biometricPrompt.authenticate(promptInfo)
    }
}
```

#### **iOS Biometric Integration**
```swift
import LocalAuthentication

class BiometricAuthManager {

    func authenticateWithBiometric() async -> Bool {
        let context = LAContext()
        var error: NSError?

        guard context.canEvaluatePolicy(.deviceOwnerAuthenticationWithBiometrics, error: &error) else {
            return false
        }

        do {
            let result = try await context.evaluatePolicy(
                .deviceOwnerAuthenticationWithBiometrics,
                localizedReason: "Authentifiez-vous pour accéder à PTC Care"
            )
            return result
        } catch {
            return false
        }
    }
}
```

### **Secure Storage Implementation**

#### **Android Secure Storage**
```kotlin
class SecureStorage(private val context: Context) {
    private val sharedPreferences = EncryptedSharedPreferences.create(
        "ptc_care_prefs",
        MasterKeys.getOrCreate(MasterKeys.AES256_GCM_SPEC),
        context,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )

    fun saveUserData(userData: LoginResponse) {
        val json = Gson().toJson(userData)
        sharedPreferences.edit()
            .putString("user_data", json)
            .apply()
    }

    fun getUserData(): LoginResponse? {
        val json = sharedPreferences.getString("user_data", null)
        return if (json != null) {
            Gson().fromJson(json, LoginResponse::class.java)
        } else null
    }

    fun savePasswordResetToken(token: String) {
        sharedPreferences.edit()
            .putString("password_reset_token", token)
            .apply()
    }

    fun clearAllData() {
        sharedPreferences.edit().clear().apply()
    }
}
```

#### **iOS Secure Storage**
```swift
import KeychainAccess

class SecureStorage {
    private let keychain = Keychain(service: "com.ptccare.app")

    func saveUserData(_ userData: LoginResponse) {
        do {
            let data = try JSONEncoder().encode(userData)
            keychain["user_data"] = data
        } catch {
            print("Failed to save user data: \(error)")
        }
    }

    func getUserData() -> LoginResponse? {
        guard let data = keychain["user_data"] else { return nil }

        do {
            return try JSONDecoder().decode(LoginResponse.self, from: data)
        } catch {
            print("Failed to decode user data: \(error)")
            return nil
        }
    }

    func savePasswordResetToken(_ token: String) {
        keychain["password_reset_token"] = token
    }

    func getPasswordResetToken() -> String? {
        return keychain["password_reset_token"]
    }

    func clearAllData() {
        try? keychain.removeAll()
    }
}
```

---

**API Version**: 1.0
**Last Updated**: 2024-06-15
**Base URL**: `https://your-domain.com` or `http://localhost:8000` (development)
