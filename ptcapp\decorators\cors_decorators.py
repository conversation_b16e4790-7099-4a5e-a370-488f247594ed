"""
Décorateurs CORS pour l'API mobile PTCCare
Gestion automatique des requêtes OPTIONS et headers CORS
"""

from functools import wraps
from django.http import JsonResponse

def cors_enabled(allowed_methods=None):
    """
    Décorateur pour activer CORS sur une vue
    
    Args:
        allowed_methods (list): Liste des méthodes HTTP autorisées
                               Par défaut: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
    """
    if allowed_methods is None:
        allowed_methods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
    
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(request, *args, **kwargs):
            # Gérer les requêtes OPTIONS (preflight)
            if request.method == 'OPTIONS':
                response = JsonResponse({'status': 'ok'})
                response['Access-Control-Allow-Origin'] = '*'
                response['Access-Control-Allow-Methods'] = ', '.join(allowed_methods)
                response['Access-Control-Allow-Headers'] = (
                    'Content-Type, Authorization, X-Requested-With, '
                    'Accept, Origin, User-Agent, DNT, Cache-Control, '
                    'X-Mx-ReqToken, Keep-Alive, X-CSRFToken'
                )
                response['Access-Control-Allow-Credentials'] = 'true'
                response['Access-Control-Max-Age'] = '86400'  # 24 heures
                return response
            
            # Exécuter la vue normale
            response = view_func(request, *args, **kwargs)
            
            # Ajouter les headers CORS à la réponse
            if hasattr(response, '__setitem__'):
                response['Access-Control-Allow-Origin'] = '*'
                response['Access-Control-Allow-Credentials'] = 'true'
                response['Access-Control-Expose-Headers'] = (
                    'Content-Type, Authorization, X-Requested-With, '
                    'Accept, Origin, User-Agent'
                )
            
            return response
        return wrapper
    return decorator

def mobile_api_cors(view_func):
    """
    Décorateur spécialisé pour l'API mobile
    Inclut toutes les méthodes et headers nécessaires pour Flutter
    """
    return cors_enabled(['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'])(view_func)
