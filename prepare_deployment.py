#!/usr/bin/env python
"""
Script de préparation pour le déploiement Heroku
Installe les dépendances manquantes et teste la configuration
"""
import subprocess
import sys
import os

def run_command(command, description):
    """Exécuter une commande et afficher le résultat"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ {description} - Succès")
            return True
        else:
            print(f"❌ {description} - Erreur:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ {description} - Exception: {e}")
        return False

def main():
    print("🚀 Préparation du déploiement PTCCare")
    print("=" * 50)
    
    # Liste des dépendances à installer
    dependencies = [
        "dj-database-url==2.1.0",
        "psycopg2-binary==2.9.9", 
        "gunicorn==21.2.0",
        "whitenoise==6.6.0"
    ]
    
    # Installation des dépendances
    for dep in dependencies:
        success = run_command(
            f".\\venv\\Scripts\\pip.exe install {dep}",
            f"Installation de {dep}"
        )
        if not success:
            print(f"⚠️ Échec de l'installation de {dep}, mais on continue...")
    
    # Test de la configuration de production
    print("\n🧪 Test de la configuration de production...")
    
    # Définir les variables d'environnement de test
    os.environ['SECRET_KEY'] = 'test-secret-key-for-deployment-check'
    os.environ['DEBUG'] = 'False'
    
    try:
        # Import des paramètres de production
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ptccare.settings_production')
        
        import django
        django.setup()
        
        from django.conf import settings
        
        print("✅ Configuration de production chargée")
        print(f"   DEBUG: {settings.DEBUG}")
        print(f"   ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
        print(f"   Base de données: {settings.DATABASES['default']['ENGINE']}")
        
        # Test simple de Django
        from django.core.management import call_command
        from io import StringIO
        
        out = StringIO()
        call_command('check', stdout=out, stderr=out)
        output = out.getvalue()
        
        if "System check identified no issues" in output or len(output.strip()) == 0:
            print("✅ Vérification Django réussie")
        else:
            print("⚠️ Vérification Django avec avertissements:")
            print(output)
            
    except Exception as e:
        print(f"❌ Erreur lors du test de configuration: {e}")
        print("⚠️ Cela peut être normal en développement local")
    
    print("\n📋 Vérification des fichiers de déploiement...")
    
    required_files = [
        "requirements.txt",
        "Procfile", 
        "runtime.txt",
        "app.json",
        "ptccare/settings_production.py"
    ]
    
    all_files_present = True
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MANQUANT")
            all_files_present = False
    
    print("\n" + "=" * 50)
    if all_files_present:
        print("🎉 Préparation terminée avec succès !")
        print("📝 Prochaines étapes:")
        print("   1. Créer un compte Heroku si pas déjà fait")
        print("   2. Installer Heroku CLI")
        print("   3. Exécuter: .\\deploy.ps1 -AppName 'votre-nom-app'")
        print("   4. Ou suivre le guide: HEROKU_DEPLOYMENT_GUIDE.md")
    else:
        print("⚠️ Certains fichiers sont manquants")
        print("   Vérifiez la structure du projet")

if __name__ == "__main__":
    main()
