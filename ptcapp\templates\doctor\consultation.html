{% extends 'doctor/layout.html' %} 
{% load static %}

{% block up-style %}
    <!-- DataTables -->
    <link href="{% static 'libs/select2/css/select2.min.css' %}" rel="stylesheet" type="text/css" />
    <link href="{% static 'libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css' %}" rel="stylesheet" type="text/css" />
{% endblock up-style %}
{% block action_button %}
<a href="{% url 'assistant.patient.create' %}" id="addPersonnel" class="btn btn-primary mx-3">Ajouter un patient</a>
{% endblock action_button %}
{% block content %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white text-left">
                    <span class="h2 text-capitalize">Filtre sur les patients</span>
                </div>
                <div class="card-body">
                    <form action="#" method="POST">
                        <div class="row">
                            <div class="col-md-3 col-sm-12">
                                <label for="fullname" class="form-label">Nom & Prénoms</label>
                                <input class="form-control" type="text" name="fullname" placeholder="Nom & Prénoms" id="fullname">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="prev_appointment" class="form-label">Précédente Consultaion</label>
                                <input class="form-control" type="date" name="prev_appointment" placeholder="Date d'inscriptation" id="prev_appointment">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="next_appointment" class="form-label">Prochaine Consultaion</label>
                                <input class="form-control" type="date" name="next_appointment" placeholder="Date d'inscriptation" id="next_appointment">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="" class="form-label text-white">Appliquer</label>
                                <input class="form-control btn btn-success" type="submit" value="Appliquer">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-center">
                    <span class="h2 text-capitalize text-white"> liste des patients suivis</span>
                </div>
                <div class="card-body">
                    <table
                        id="datatable"
                        class="table table-striped dt-responsive nowrap"
                        style="border-collapse: collapse; border-spacing: 0; width: 100%"
                        >
                        <thead>
                            <tr>
                                <th></th>
                                <th>Créé par</th>
                                <th>Nom & Prénoms</th>
                                <th>Précédente Consultation</th>
                                <th>Prochaine Consultation</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr>
                                <td>Mère</td>
                                <td>Ben DOE</td>
                                <td>Jessica GEORGE</td>
                                <td>2011/04/25</td>
                                <td>2011/04/25</td>
                                <td>
                                    <a href="{% url 'assistant.patient.edit' id=45 %}" data-toggle="tooltip" data-placement="right" title="Modifier"><i class=" ri-pencil-fill" ></i></a>
                                    <a href="#" class="delete-user" data-user-id="15" data-toggle="tooltip" data-placement="right" title="Supprimer"><i class=" ri-delete-bin-fill" ></i></a>
                                </td>
                            </tr>
                            <tr>
                                <td>Enfant</td>
                                <td>Ben DOE</td>
                                <td>Jessica GEORGE</td>
                                <td>2011/04/25</td>
                                <td>2011/04/25</td>
                                <td>
                                    <a href="{% url 'assistant.patient.edit' id=45 %}" data-toggle="tooltip" data-placement="right" title="Modifier"><i class=" ri-pencil-fill" ></i></a>
                                    <a href="#" class="delete-user" data-user-id="15" data-toggle="tooltip" data-placement="right" title="Supprimer"><i class=" ri-delete-bin-fill" ></i></a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}

{% block down-script %}
    <script src="//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json"></script>
    <script src="{% static 'libs/select2/js/select2.min.js'%}"></script>
    <!-- Required datatable js -->
    <script src="{% static 'libs/datatables.net/js/jquery.dataTables.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js'%}"></script>
    <!-- Responsive examples -->
    <script src="{% static 'libs/datatables.net-responsive/js/dataTables.responsive.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js'%}"></script>
    <!-- Datatable init js -->
    {% comment %} <script src="{% static 'js/pages/datatables.init.js' %}"></script> {% endcomment %}
    <script>
        var dt = $("#datatable").DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
            },
            "columnDefs": [ {
                "targets": [5],
                "orderable": false
            }],
            order : [[2, 'asc']],
            drawCallback: function() {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        $('.select2').select2()
        $(".delete-user").click(function() {
            Swal.fire({
                title: "Voulez-vous vraiment supprimer cet utilisateur ?",
                text: "Cet utilisateur sera définitivement supprimer !",
                icon: "warning",
                showCancelButton: !0,
                confirmButtonColor: "#1cbb8c",
                cancelButtonColor: "#ff3d60",
                confirmButtonText: "Oui, supprimer!",
                cancelButtonText: "Annuler",
            }).then(function(t) {
                t.value &&
                    Swal.fire("Supprimer!", "", "success");
            });
        })
    </script>
{% endblock down-script %}