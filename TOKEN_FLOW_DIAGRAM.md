# Diagramme de Flux - Gestion des Tokens PTC Care

## 🔄 Flux Complet de Gestion des Tokens

### **Scénario 1 : Création d'un Nouvel Utilisateur**

```mermaid
graph TD
    A[Admin/Agent crée utilisateur] --> B[POST /api/create-health-agent/ ou /api/create-patient/]
    B --> C[Création User Django]
    C --> D[Création Profile]
    D --> E[Création UserPasswordStatus<br/>must_change_password=True]
    E --> F[Appel EmailNotificationService.send_welcome_email]
    F --> G[get_or_create PasswordResetToken]
    G --> H[Génération token 64 chars]
    H --> I[Sauvegarde en base<br/>expires_at = now + 48h]
    I --> J[Envoi email avec lien<br/>/change-password/{token}/]
    J --> K[Utilisateur reçoit email]
    
    style E fill:#ffeb3b
    style G fill:#4caf50
    style I fill:#2196f3
```

### **Scénario 2 : Demande de Réinitialisation**

```mermaid
graph TD
    A[Utilisateur oublie mot de passe] --> B[POST /api/request-password-reset/]
    B --> C[Vérification rate limiting<br/>max 3/heure]
    C --> D{Email existe?}
    D -->|Oui| E[Invalidation anciens tokens<br/>is_used=True]
    D -->|Non| F[Message générique<br/>sécurité]
    E --> G[Création nouveau PasswordResetToken]
    G --> H[Token sécurisé 64 chars<br/>secrets.token_urlsafe]
    H --> I[Sauvegarde expires_at = now + 48h]
    I --> J[Envoi email réinitialisation]
    J --> K[Utilisateur reçoit email]
    F --> L[Retour même message succès]
    K --> L
    
    style C fill:#ff9800
    style E fill:#f44336
    style G fill:#4caf50
```

### **Scénario 3 : Utilisation du Token**

```mermaid
graph TD
    A[Utilisateur clique lien email] --> B[GET /change-password/{token}/]
    B --> C[Validation token en base]
    C --> D{Token valide?}
    D -->|Non| E[Erreur: Token invalide/expiré]
    D -->|Oui| F[Affichage formulaire changement]
    F --> G[Utilisateur saisit nouveau mot de passe]
    G --> H[POST /api/change-password/]
    H --> I[Validation token + mot de passe]
    I --> J[user.set_password nouvelle_mdp]
    J --> K[reset_token.mark_as_used<br/>is_used=True]
    K --> L[user.password_status.mark_password_changed<br/>must_change_password=False]
    L --> M[Envoi email confirmation]
    M --> N[Redirection vers login]
    
    style D fill:#ff9800
    style K fill:#f44336
    style L fill:#4caf50
```

## 🗃️ Structure des Tables

### **Table: ptcapp_password_reset_token**

```
┌─────────────────────────────────────────────────────────────┐
│                    PasswordResetToken                       │
├─────────────────────────────────────────────────────────────┤
│ id (PK)              │ AutoField                            │
│ user_id (FK)         │ OneToOne → auth_user.id              │
│ token                │ CharField(64) UNIQUE                 │
│ created_at           │ DateTimeField (auto_now_add)         │
│ expires_at           │ DateTimeField (now + 48h)            │
│ is_used              │ BooleanField (default=False)         │
│ force_password_change│ BooleanField (default=True)          │
└─────────────────────────────────────────────────────────────┘
```

### **Table: ptcapp_user_password_status**

```
┌─────────────────────────────────────────────────────────────┐
│                   UserPasswordStatus                        │
├─────────────────────────────────────────────────────────────┤
│ id (PK)              │ AutoField                            │
│ user_id (FK)         │ OneToOne → auth_user.id              │
│ must_change_password │ BooleanField (default=False)         │
│ password_changed_at  │ DateTimeField (null=True)            │
│ first_login_completed│ BooleanField (default=False)         │
│ created_at           │ DateTimeField (auto_now_add)         │
│ updated_at           │ DateTimeField (auto_now)             │
└─────────────────────────────────────────────────────────────┘
```

## 🔗 Relations et Contraintes

### **Relations entre Modèles**

```
auth_user (Django)
    ├── OneToOne → ptcapp_password_reset_token
    ├── OneToOne → ptcapp_user_password_status  
    └── OneToOne → ptcapp_profile

Contraintes:
- Un utilisateur = Un token maximum
- Un utilisateur = Un statut de mot de passe
- Token unique dans toute la base
- Expiration automatique après 48h
```

### **États des Tokens**

```mermaid
stateDiagram-v2
    [*] --> Created : Création utilisateur/demande reset
    Created --> Valid : Token généré et sauvé
    Valid --> Used : Changement mot de passe réussi
    Valid --> Expired : 48h écoulées
    Used --> [*] : Token définitivement invalidé
    Expired --> [*] : Token définitivement invalidé
    
    note right of Valid : is_used=False<br/>expires_at > now()
    note right of Used : is_used=True
    note right of Expired : expires_at <= now()
```

## 📊 Cycle de Vie Détaillé

### **Phase 1: Création (Nouveaux Utilisateurs)**

| Étape | Action | Table Modifiée | Champ Modifié |
|-------|--------|----------------|---------------|
| 1 | Création User | `auth_user` | Tous les champs user |
| 2 | Création Profile | `ptcapp_profile` | Informations personnelles |
| 3 | Création Status | `ptcapp_user_password_status` | `must_change_password=True` |
| 4 | Création Token | `ptcapp_password_reset_token` | `token`, `expires_at` |
| 5 | Envoi Email | - | Lien avec token |

### **Phase 2: Réinitialisation (Mot de Passe Oublié)**

| Étape | Action | Table Modifiée | Champ Modifié |
|-------|--------|----------------|---------------|
| 1 | Demande Reset | - | Validation email |
| 2 | Rate Limiting | Cache Django | Compteur par email |
| 3 | Invalidation | `ptcapp_password_reset_token` | `is_used=True` (anciens) |
| 4 | Nouveau Token | `ptcapp_password_reset_token` | Nouveau token |
| 5 | Envoi Email | - | Lien de réinitialisation |

### **Phase 3: Utilisation (Changement Effectif)**

| Étape | Action | Table Modifiée | Champ Modifié |
|-------|--------|----------------|---------------|
| 1 | Validation Token | `ptcapp_password_reset_token` | Vérification validité |
| 2 | Changement MDP | `auth_user` | `password` (hashé) |
| 3 | Invalidation Token | `ptcapp_password_reset_token` | `is_used=True` |
| 4 | Mise à jour Status | `ptcapp_user_password_status` | `must_change_password=False` |
| 5 | Confirmation | - | Email de confirmation |

## 🔍 Requêtes SQL Utiles

### **1. Tokens Actifs par Utilisateur**
```sql
SELECT 
    u.username,
    u.email,
    prt.token,
    prt.created_at,
    prt.expires_at,
    CASE 
        WHEN prt.expires_at > NOW() AND prt.is_used = FALSE THEN 'VALIDE'
        WHEN prt.expires_at <= NOW() THEN 'EXPIRÉ'
        WHEN prt.is_used = TRUE THEN 'UTILISÉ'
    END as statut
FROM ptcapp_password_reset_token prt
JOIN auth_user u ON prt.user_id = u.id
ORDER BY prt.created_at DESC;
```

### **2. Utilisateurs Devant Changer leur Mot de Passe**
```sql
SELECT 
    u.username,
    u.email,
    ups.must_change_password,
    ups.first_login_completed,
    ups.password_changed_at,
    prt.token IS NOT NULL as has_token
FROM ptcapp_user_password_status ups
JOIN auth_user u ON ups.user_id = u.id
LEFT JOIN ptcapp_password_reset_token prt ON u.id = prt.user_id 
    AND prt.is_used = FALSE 
    AND prt.expires_at > NOW()
WHERE ups.must_change_password = TRUE;
```

### **3. Statistiques des Tokens**
```sql
SELECT 
    COUNT(*) as total_tokens,
    SUM(CASE WHEN is_used = TRUE THEN 1 ELSE 0 END) as tokens_utilises,
    SUM(CASE WHEN expires_at <= NOW() THEN 1 ELSE 0 END) as tokens_expires,
    SUM(CASE WHEN is_used = FALSE AND expires_at > NOW() THEN 1 ELSE 0 END) as tokens_valides
FROM ptcapp_password_reset_token;
```

## 🛠️ Maintenance et Monitoring

### **Commandes de Maintenance Recommandées**

#### 1. **Nettoyage des Tokens Expirés**
```python
# management/commands/clean_expired_tokens.py
from django.core.management.base import BaseCommand
from ptcapp.models.password_reset import PasswordResetToken
from django.utils import timezone

class Command(BaseCommand):
    def handle(self, *args, **options):
        expired_tokens = PasswordResetToken.objects.filter(
            models.Q(expires_at__lt=timezone.now()) | models.Q(is_used=True)
        )
        count = expired_tokens.count()
        expired_tokens.delete()
        self.stdout.write(f"Supprimé {count} tokens expirés/utilisés")
```

#### 2. **Rapport de Sécurité**
```python
# Tokens non utilisés depuis plus de 7 jours
old_unused_tokens = PasswordResetToken.objects.filter(
    is_used=False,
    created_at__lt=timezone.now() - timedelta(days=7)
)

# Utilisateurs bloqués en changement obligatoire
blocked_users = UserPasswordStatus.objects.filter(
    must_change_password=True,
    created_at__lt=timezone.now() - timedelta(days=7)
)
```

---

**Ce système garantit** :
- ✅ **Sécurité** : Tokens uniques et expiration automatique
- ✅ **Traçabilité** : Historique complet des changements
- ✅ **Intégrité** : Relations cohérentes entre modèles
- ✅ **Maintenance** : Nettoyage automatique possible
