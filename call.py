#!/usr/bin/env python

"""\
Demo: dial a number (simple example using polling to check call status)

Simple demo app that makes a voice call and plays sone DTMF tones (if supported by modem)
when the call is answered, and hangs up the call.
It polls the call status to see if the call has been answered

Note: you need to modify the NUMBER variable for this to work
"""

from __future__ import print_function

import sys, time, logging, wave, serial
import serial



PORT = 'COM26'
BAUDRATE = 9600
NUMBER = '61135301' # Number to dial - CHANGE THIS TO A REAL NUMBER
PIN = None # SIM card PIN (if any)

from gsmmodem.modem import GsmModem
from gsmmodem.exceptions import InterruptedException, CommandError

def main():
    if NUMBER == None or NUMBER == '00000':
        print('Error: Please change the NUMBER variable\'s value before running this example.')
        sys.exit(1)
    print('Initializing modem...')
    #logging.basicConfig(format='%(levelname)s: %(message)s', level=logging.DEBUG)
    ser = serial.Serial('COM26', 9800)
    if not ser.isOpen():
        ser.open()
    modem = GsmModem(PORT, BAUDRATE)
    modem.connect(PIN)
    print('Waiting for network coverage...')
    modem.waitForNetworkCoverage(30)
    print('Dialing number: {0}'.format(NUMBER))
    music = wave.open('voice.wav','r')
    call = modem.dial(NUMBER)
    print('Waiting for call to be answered/rejected')
    wasAnswered = False
    while call.active:
        if call.answered:
            wasAnswered = True
            print('Call has been answered; waiting a while...')
            # Wait for a bit - some older modems struggle to send DTMF tone immediately after answering a call
            time.sleep(3.0)
            try:
                if call.active: # Call could have been ended by remote party while we waited in the time.sleep() call
                    # call.sendDtmfTone('9515')
                    modem.write('AT^DDSETEX=2')
                    ser = serial.Serial()
                    ser.port = "COM24"
                    ser.baudrate = 115200
                    # ser.bytesize = serial.EIGHTBITS  # number of bits per bytes
                    ser.timeout = 1  # non-block read
                    ser.writeTimeout = None  # timeout for write
                    chunk=320
                    frame = music.readframes(chunk)      
                    if not ser.isOpen():
                        ser.open() 
                    while frame != b'':
                        if ser.isOpen():
                            ser.write(frame)
                            frame = music.readframes(chunk)  
                            time.sleep(0.035)
                    music.close
            except InterruptedException as e:
                # Call was ended during playback

                print('DTMF playback interrupted: {0} ({1} Error {2})'.format(e, e.cause.type, e.cause.code))
            except CommandError as e:
                print('DTMF playback failed: {0}'.format(e))
            finally:
                if call.active: # Call is still active
                    print('Hanging up call...')
                    modem.write('AT+CHUP')
                    call.hangup()
                else: # Call is no longer active (remote party ended it)
                    print('Call has been ended by remote party')
        else:
            # Wait a bit and check again
            time.sleep(0.5)
    if not wasAnswered:
        print('Call was not answered by remote party')
    print('Done.')
    modem.close()

if __name__ == '__main__':
    main() 