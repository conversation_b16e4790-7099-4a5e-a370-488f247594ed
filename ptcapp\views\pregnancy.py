from django.shortcuts import redirect, render,get_object_or_404,HttpResponseRedirect
from django.http import JsonResponse

# relative import of forms
from ptcapp.models import Pregnancy, Profile, MotherPregnancy


def create_view(request):
    if request.method == 'POST':
        patient = get_object_or_404(Profile, id=request.POST['pregnancy_patient'])
        start_date = request.POST['start_date']
        priority = request.POST['priority']
        other_informations = request.POST['other_informations']

        pregnancy = Pregnancy()
        pregnancy.start_date = start_date
        pregnancy.situation = priority
        pregnancy.description = other_informations
        pregnancy.state = "ongoing"
        pregnancy.save()

        mother_pregnancy = MotherPregnancy()
        mother_pregnancy.mother = patient
        mother_pregnancy.pregnancy = pregnancy
        mother_pregnancy.save()

        return JsonResponse({'pregnancy':int(pregnancy.id), 'success' : True})
    return JsonResponse({'success' : False})



def list_view(request):
    context ={}
 
    context["dataset"] = Pregnancy.objects.all()
    return render(request, "pregnancy/list.html", context)


def detail_view(request, id):
    
    context ={}
 
    context["data"] = Pregnancy.objects.get(id = id)
         
    return render(request, "pregnancy/detail.html", context)


def update_view(request, id):

    context ={}
 
    patient = get_object_or_404(Profile, id=request.POST['pregnancy_patient'])
    start_date = request.POST['start_date']
    priority = request.POST['priority']
    other_informations = request.POST['other_informations']

    pregnancy = get_object_or_404(Pregnancy, id=id)
    pregnancy.start_date = start_date
    pregnancy.situation = priority
    pregnancy.description = other_informations
    pregnancy.end_date = request.POST['end_date']
    pregnancy.child_number = request.POST['children_state']
    pregnancy.mother_state= request.POST['mother_state']
    pregnancy.interruption_cause = request.POST['interruption_cause']
    pregnancy.children_state = request.POST['children_state']
    pregnancy.state = request.POST['statut']
    pregnancy.save()

    return redirect("/docteur/appointment/")
    

def delete_view(request, id):
    context ={}
 
    obj = get_object_or_404(Pregnancy, id = id)
 
    if request.method =="POST":
        obj.delete()
        return HttpResponseRedirect("/")
 
    return render(request, "pregnancy/delete.html", context)
