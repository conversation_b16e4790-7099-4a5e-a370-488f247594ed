# Documentation - Endpoint de Demande de Réinitialisation de Mot de Passe

## 🔐 Vue d'Ensemble

L'endpoint `POST /api/request-password-reset/` permet aux utilisateurs de demander une réinitialisation de leur mot de passe en cas d'oubli. Il génère un token sécurisé et envoie un email avec un lien de réinitialisation.

## 📋 Spécifications Techniques

### Endpoint
```
POST /api/request-password-reset/
```

### Headers Requis
```
Content-Type: application/json
```

### Paramètres de Requête
```json
{
  "email": "string"  // Adresse email de l'utilisateur (requis)
}
```

### Réponses

#### Succès (200)
```json
{
  "status": "success",
  "message": "Si cette adresse email existe, un lien de réinitialisation a été envoyé"
}
```

#### Erreurs
```json
// Email manquant (400)
{
  "error": "Adresse email requise"
}

// Format email invalide (400)
{
  "error": "Format d'adresse email invalide"
}

// JSON invalide (400)
{
  "error": "Données JSON invalides"
}

// Erreur serveur (500)
{
  "error": "Erreur interne du serveur"
}
```

## 🛡️ Mesures de Sécurité

### 1. Protection contre l'Énumération d'Emails
- **Même réponse** pour emails existants et inexistants
- **Message générique** : "Si cette adresse email existe..."
- **Pas de révélation** de l'existence ou non d'un compte

### 2. Rate Limiting
- **Limite** : 3 demandes par email par heure
- **Implémentation** : Cache Django avec expiration automatique
- **Comportement** : Retourne toujours le même message de succès

### 3. Génération de Token Sécurisé
- **Longueur** : 64 caractères (48 bytes en base64url)
- **Méthode** : `secrets.token_urlsafe()` (cryptographiquement sécurisé)
- **Unicité** : Chaque token est unique et imprévisible

### 4. Expiration et Usage Unique
- **Durée de vie** : 48 heures
- **Usage unique** : Token invalidé après utilisation
- **Nettoyage automatique** : Anciens tokens invalidés lors de nouvelle demande

## 🔄 Flux de Fonctionnement

### Diagramme de Flux
```
Utilisateur saisit email
        ↓
POST /api/request-password-reset/
        ↓
Validation format email
        ↓
Vérification rate limiting
        ↓
Recherche utilisateur (silencieuse)
        ↓
Si utilisateur existe:
  ├── Invalider anciens tokens
  ├── Générer nouveau token sécurisé
  ├── Sauvegarder en base (48h expiration)
  └── Envoyer email avec lien
        ↓
Retourner message générique de succès
```

### Processus Détaillé

#### 1. Validation des Données
```python
# Vérification présence email
if not email:
    return 400 "Adresse email requise"

# Validation format basique
if '@' not in email or '.' not in email.split('@')[-1]:
    return 400 "Format d'adresse email invalide"
```

#### 2. Rate Limiting
```python
cache_key = f"password_reset_rate_limit_{email.lower()}"
current_count = cache.get(cache_key, 0)

if current_count >= 3:
    return success_message  # Même message pour sécurité

cache.set(cache_key, current_count + 1, 3600)  # 1 heure
```

#### 3. Gestion des Tokens
```python
# Invalider anciens tokens
PasswordResetToken.objects.filter(
    user=user,
    is_used=False,
    expires_at__gt=timezone.now()
).update(is_used=True)

# Créer nouveau token
token = secrets.token_urlsafe(48)  # 64 caractères
reset_token = PasswordResetToken.objects.create(
    user=user,
    token=token,
    expires_at=timezone.now() + timedelta(hours=48),
    is_used=False
)
```

## 📧 Intégration Email

### Service d'Email
L'endpoint utilise `EmailNotificationService.send_password_reset_email()` pour envoyer les emails de réinitialisation.

### Templates d'Email

#### Template Texte (`password_reset.txt`)
```
Bonjour {{ full_name }},

Vous avez demandé la réinitialisation de votre mot de passe pour votre compte PTC Care.

Pour créer un nouveau mot de passe, cliquez sur le lien suivant :
{{ reset_url }}

Ce lien est valide pendant {{ token_expiry_hours }} heures.
```

#### Template HTML (`password_reset.html`)
- Design responsive et professionnel
- Bouton d'action proéminent
- Informations de sécurité
- Instructions claires

### URL de Réinitialisation
```
Format: {base_url}/change-password/{token}/
Exemple: http://localhost:8000/change-password/abc123.../
```

## 🧪 Tests et Validation

### Script de Test Automatisé
Le fichier `test_password_reset_endpoint.py` contient des tests complets :

#### Tests Inclus
1. **Email valide** : Vérification création token et envoi email
2. **Email inexistant** : Vérification message générique
3. **Rate limiting** : Test de la limite de 3 demandes/heure
4. **JSON invalide** : Validation gestion erreurs
5. **Email manquant** : Test paramètres requis

#### Exécution des Tests
```bash
python test_password_reset_endpoint.py
```

### Tests Manuels avec cURL

#### Demande Valide
```bash
curl -X POST http://localhost:8000/api/request-password-reset/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'
```

#### Test Rate Limiting
```bash
# Exécuter 4 fois rapidement
for i in {1..4}; do
  curl -X POST http://localhost:8000/api/request-password-reset/ \
    -H "Content-Type: application/json" \
    -d '{"email": "<EMAIL>"}'
  echo "Demande $i"
done
```

## 📱 Intégration Mobile

### Implémentation Android
```kotlin
class PasswordResetService @Inject constructor(
    private val apiClient: ApiClient
) {
    suspend fun requestPasswordReset(email: String): PasswordResetResult {
        try {
            val response = apiClient.post<PasswordResetResponse>(
                "/api/request-password-reset/",
                PasswordResetRequest(email)
            )
            
            return PasswordResetResult.Success(response.message)
        } catch (e: Exception) {
            return PasswordResetResult.Error(e.message ?: "Erreur de connexion")
        }
    }
}

data class PasswordResetRequest(val email: String)
data class PasswordResetResponse(val status: String, val message: String)

sealed class PasswordResetResult {
    data class Success(val message: String) : PasswordResetResult()
    data class Error(val message: String) : PasswordResetResult()
}
```

### Implémentation iOS
```swift
class PasswordResetService {
    func requestPasswordReset(email: String) async -> PasswordResetResult {
        do {
            let request = PasswordResetRequest(email: email)
            let response: PasswordResetResponse = try await apiClient.post(
                "/api/request-password-reset/",
                body: request
            )
            
            return .success(response.message)
        } catch {
            return .error(error.localizedDescription)
        }
    }
}

struct PasswordResetRequest: Codable {
    let email: String
}

struct PasswordResetResponse: Codable {
    let status: String
    let message: String
}

enum PasswordResetResult {
    case success(String)
    case error(String)
}
```

### Interface Utilisateur Mobile

#### Écran de Demande de Réinitialisation
```kotlin
@Composable
fun ForgotPasswordScreen() {
    var email by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(false) }
    var message by remember { mutableStateOf<String?>(null) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "Mot de passe oublié ?",
            style = MaterialTheme.typography.h5
        )
        
        Text(
            text = "Saisissez votre adresse email pour recevoir un lien de réinitialisation",
            style = MaterialTheme.typography.body2,
            modifier = Modifier.padding(vertical = 16.dp)
        )
        
        OutlinedTextField(
            value = email,
            onValueChange = { email = it },
            label = { Text("Adresse email") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
            modifier = Modifier.fillMaxWidth()
        )
        
        Button(
            onClick = {
                isLoading = true
                // Appeler le service de réinitialisation
            },
            enabled = email.isNotBlank() && !isLoading,
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 16.dp)
        ) {
            if (isLoading) {
                CircularProgressIndicator(size = 16.dp)
            } else {
                Text("Envoyer le lien")
            }
        }
        
        message?.let { msg ->
            Text(
                text = msg,
                color = MaterialTheme.colors.primary,
                modifier = Modifier.padding(top = 16.dp)
            )
        }
    }
}
```

## 📊 Monitoring et Logs

### Logs Générés
```python
# Succès
logger.info(f"Email de réinitialisation envoyé avec succès pour {email}")

# Rate limiting
logger.warning(f"Rate limit dépassé pour l'email: {email}")

# Email inexistant
logger.info(f"Tentative de réinitialisation pour un email inexistant: {email}")

# Erreurs
logger.error(f"Erreur lors de l'envoi de l'email de réinitialisation pour {email}: {str(error)}")
```

### Métriques Recommandées
- **Nombre de demandes** par heure/jour
- **Taux de succès** d'envoi d'emails
- **Taux d'utilisation** des tokens générés
- **Tentatives sur emails inexistants**

## 🔧 Configuration

### Variables d'Environnement
```python
# settings.py
PTCCARE_BASE_URL = 'https://your-domain.com'  # URL de base pour les liens
EMAIL_HOST_USER = '<EMAIL>'      # Email d'envoi
DEFAULT_FROM_EMAIL = 'PTC Care <<EMAIL>>'
```

### Cache Configuration
```python
# Pour le rate limiting
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.redis.RedisCache',
        'LOCATION': 'redis://127.0.0.1:6379/1',
    }
}
```

## ✅ Checklist de Déploiement

### Tests Pré-Déploiement
- [ ] Tests automatisés passent (5/5)
- [ ] Rate limiting fonctionnel
- [ ] Emails envoyés et reçus
- [ ] Tokens générés correctement
- [ ] Sécurité validée (pas d'énumération)

### Configuration Production
- [ ] HTTPS activé
- [ ] Variables d'environnement configurées
- [ ] Cache Redis configuré
- [ ] Monitoring des logs activé
- [ ] Tests de charge effectués

---

**Version** : 1.0  
**Dernière mise à jour** : 2024-06-15  
**Statut** : ✅ Prêt pour production avec sécurité renforcée
