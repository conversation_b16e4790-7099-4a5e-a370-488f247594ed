import logging
import os
from django.core.mail import send_mail, EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
from django.contrib.auth.models import User
from ptcapp.models.password_reset import PasswordResetToken, UserPasswordStatus
from ptcapp.models.profile import Profile

logger = logging.getLogger(__name__)


class EmailNotificationService:
    """
    Service pour gérer l'envoi d'emails de notification pour les nouveaux utilisateurs.
    """
    
    @staticmethod
    def send_new_user_credentials(user, password, role_name):
        """
        Envoie les identifiants de connexion à un nouvel utilisateur.
        
        Args:
            user (User): L'utilisateur créé
            password (str): Le mot de passe temporaire
            role_name (str): Le nom du rôle (admin, docteur, assistant, patient)
            
        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            # <PERSON><PERSON><PERSON> le token de changement de mot de passe
            token, created = PasswordResetToken.objects.get_or_create(
                user=user,
                defaults={'force_password_change': True}
            )
            
            # Créer le statut de mot de passe
            password_status, created = UserPasswordStatus.objects.get_or_create(
                user=user,
                defaults={'must_change_password': True}
            )
            
            # Récupérer le profil pour les informations personnelles
            try:
                profile = Profile.objects.get(user=user)
                full_name = f"{profile.firstname} {profile.lastname}"
            except Profile.DoesNotExist:
                full_name = user.get_full_name() or user.username
            
            # Générer l'URL de changement de mot de passe
            # Utiliser le domaine Heroku en production
            if 'DYNO' in os.environ:
                base_url = 'https://ptccare-web-ae382d4ad8cc.herokuapp.com'
            else:
                base_url = getattr(settings, 'PTCCARE_BASE_URL', 'http://localhost:8000')
            password_change_url = f"{base_url}/change-password/{token.token}/"
            
            # Contexte pour le template
            context = {
                'user': user,
                'full_name': full_name,
                'username': user.username,
                'email': user.email,
                'password': password,
                'role_name': role_name,
                'password_change_url': password_change_url,
                'token_expiry_hours': getattr(settings, 'PASSWORD_RESET_TOKEN_EXPIRY_HOURS', 48),
                'support_email': '<EMAIL>'
            }
            
            # Rendu des templates
            subject = f"Bienvenue sur PTC Care - Vos identifiants de connexion"
            
            # Template texte
            text_content = render_to_string(
                'emails/new_user_credentials.txt',
                context
            )
            
            # Template HTML
            html_content = render_to_string(
                'emails/new_user_credentials.html',
                context
            )
            
            # Envoi de l'email
            msg = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[user.email]
            )
            msg.attach_alternative(html_content, "text/html")
            msg.send()
            
            logger.info(f"Email de bienvenue envoyé à {user.email} ({user.username})")
            return True, "Email envoyé avec succès"
            
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de l'email à {user.email}: {str(e)}")
            return False, f"Erreur lors de l'envoi: {str(e)}"
    
    @staticmethod
    def send_password_changed_confirmation(user):
        """
        Envoie une confirmation de changement de mot de passe.
        
        Args:
            user (User): L'utilisateur qui a changé son mot de passe
            
        Returns:
            tuple: (success: bool, message: str)
        """
        try:
            # Récupérer le profil
            try:
                profile = Profile.objects.get(user=user)
                full_name = f"{profile.firstname} {profile.lastname}"
            except Profile.DoesNotExist:
                full_name = user.get_full_name() or user.username
            
            context = {
                'user': user,
                'full_name': full_name,
                'support_email': '<EMAIL>'
            }
            
            subject = "PTC Care - Mot de passe modifié avec succès"
            
            # Template texte
            text_content = render_to_string(
                'emails/password_changed_confirmation.txt',
                context
            )
            
            # Template HTML
            html_content = render_to_string(
                'emails/password_changed_confirmation.html',
                context
            )
            
            # Envoi de l'email
            msg = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[user.email]
            )
            msg.attach_alternative(html_content, "text/html")
            msg.send()
            
            logger.info(f"Confirmation de changement de mot de passe envoyée à {user.email}")
            return True, "Confirmation envoyée avec succès"
            
        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de la confirmation à {user.email}: {str(e)}")
            return False, f"Erreur lors de l'envoi: {str(e)}"
    
    @staticmethod
    def get_role_display_name(user):
        """
        Retourne le nom d'affichage du rôle de l'utilisateur.
        
        Args:
            user (User): L'utilisateur
            
        Returns:
            str: Le nom du rôle en français
        """
        role_names = {
            'admin': 'Administrateur',
            'docteur': 'Médecin',
            'assistant': 'Assistant médical',
            'patient': 'Patient',
            'mother': 'Patiente'
        }
        
        if user.groups.exists():
            group_name = user.groups.first().name
            return role_names.get(group_name, group_name.capitalize())
        
        return 'Utilisateur'

    @staticmethod
    def send_password_reset_email(user, reset_token):
        """
        Envoie un email de réinitialisation de mot de passe.

        Args:
            user (User): L'utilisateur qui demande la réinitialisation
            reset_token (PasswordResetToken): Le token de réinitialisation

        Returns:
            bool: True si l'email a été envoyé avec succès, False sinon
        """
        try:
            # Récupérer le profil
            try:
                profile = Profile.objects.get(user=user)
                full_name = f"{profile.firstname} {profile.lastname}"
            except Profile.DoesNotExist:
                full_name = user.get_full_name() or user.username

            # Générer l'URL de réinitialisation
            # Utiliser le domaine Heroku en production
            if 'DYNO' in os.environ:
                base_url = 'https://ptccare-web-ae382d4ad8cc.herokuapp.com'
            else:
                base_url = getattr(settings, 'PTCCARE_BASE_URL', 'http://localhost:8000')
            reset_url = f"{base_url}/change-password/{reset_token.token}/"

            context = {
                'user': user,
                'full_name': full_name,
                'reset_url': reset_url,
                'token_expiry_hours': 48,
                'support_email': '<EMAIL>'
            }

            subject = "PTC Care - Réinitialisation de votre mot de passe"

            # Template texte
            text_content = render_to_string(
                'emails/password_reset.txt',
                context
            )

            # Template HTML
            html_content = render_to_string(
                'emails/password_reset.html',
                context
            )

            # Envoi de l'email
            msg = EmailMultiAlternatives(
                subject=subject,
                body=text_content,
                from_email=settings.DEFAULT_FROM_EMAIL,
                to=[user.email]
            )
            msg.attach_alternative(html_content, "text/html")
            msg.send()

            logger.info(f"Email de réinitialisation de mot de passe envoyé à {user.email}")
            return True

        except Exception as e:
            logger.error(f"Erreur lors de l'envoi de l'email de réinitialisation à {user.email}: {str(e)}")
            return False
