from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.core.exceptions import ValidationError
import json
import random
from datetime import datetime
from django.utils import timezone
from django.contrib.auth.models import User, Group
from django.db import transaction, models
from django.contrib.auth.decorators import login_required

from ..models import (
    Profile, Pregnancy, Appointment, Hospital, Service,
    Speciality, Language, MotherPregnancy, MapDoctorPatient,
    MapMotherChild, MapPregnancyChild, OtherInformation
)
from ptcapp.models.password_reset import UserPasswordStatus, PasswordResetToken
from ptcapp.services.email_service import EmailNotificationService
from ..helpers.authentification_helper import authenticated_user

@csrf_exempt
def login_api(request):
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        data = json.loads(request.body)
        # Support pour les deux formats : email ou username (rétrocompatibilité)
        email = data.get('email') or data.get('username')
        password = data.get('password')

        if not email or not password:
            return JsonResponse({'error': 'Adresse email et mot de passe requis'}, status=400)

        # Recherche de l'utilisateur par email ou username
        user = User.objects.filter(
            models.Q(email__iexact=email) | models.Q(username__iexact=email)
        ).first()

        if not user or not user.check_password(password):
            return JsonResponse({'error': 'Adresse email ou mot de passe incorrect'}, status=401)
        
        profile = Profile.objects.filter(user=user).first()
        if not profile:
            return JsonResponse({'error': 'Profil introuvable'}, status=404)

        role = 'admin' if user.groups.filter(name='admin').exists() else 'agent' if user.groups.filter(name='docteur').exists() or user.groups.filter(name='assistant').exists() else 'patient'

        # Vérifier le statut de changement de mot de passe
        password_status = UserPasswordStatus.objects.filter(user=user).first()
        must_change_password = password_status.must_change_password if password_status else False

        # Récupérer le token de réinitialisation actif s'il existe
        password_reset_token = None
        if must_change_password:
            try:
                reset_token = PasswordResetToken.objects.get(
                    user=user,
                    is_used=False,
                    expires_at__gt=timezone.now()
                )
                password_reset_token = reset_token.token
            except PasswordResetToken.DoesNotExist:
                # Aucun token valide trouvé
                pass

        # Construire la réponse
        response_data = {
            'status': 'success',
            'user_id': user.id,
            'username': user.username,
            'profile_id': profile.id,
            'role': role,
            'name': f"{profile.firstname} {profile.lastname}",
            'hospital_id': profile.hospital.id if profile.hospital else None,
            'service_id': profile.service.id if profile.service else None,
            'speciality_id': profile.speciality.id if profile.speciality else None,
            'must_change_password': must_change_password
        }

        # Ajouter le token de réinitialisation s'il existe
        if password_reset_token:
            response_data['password_reset_token'] = password_reset_token

        return JsonResponse(response_data)
            
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Données JSON invalides'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
def get_initial_data(request):
    """Récupérer les données de référence pour synchronisation initiale"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
    
    try:
        hospitals = list(Hospital.objects.values('id', 'name', 'location'))
        services = list(Service.objects.values('id', 'name', 'description'))
        specialities = list(Speciality.objects.values('id', 'name', 'description'))
        languages = list(Language.objects.values('id', 'name', 'path'))
        
        return JsonResponse({
            'status': 'success',
            'hospitals': hospitals,
            'services': services,
            'specialities': specialities,
            'languages': languages
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@transaction.atomic
def create_health_agent(request):
    """Créer un agent de santé (médecin ou assistant)"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
    
    try:
        data = json.loads(request.body)
        
        # Vérification de l'authentification
        auth_token = request.headers.get('Authorization')
        if not auth_token:
            return JsonResponse({'error': 'Non autorisé'}, status=401)
        
        try:
            # Extraire l'ID utilisateur du token (simplifié pour l'exemple)
            user_id = int(auth_token.split(' ')[1])
            admin_user = User.objects.get(id=user_id)
            if not admin_user.groups.filter(name='admin').exists():
                return JsonResponse({'error': 'Accès refusé'}, status=403)
        except (ValueError, User.DoesNotExist):
            return JsonResponse({'error': 'Token invalide'}, status=401)
        
        # Création de l'agent
        group_name = data.get('role')
        if group_name not in ['docteur', 'assistant']:
            return JsonResponse({'error': 'Rôle invalide'}, status=400)
            
        group = Group.objects.get(name=group_name)
        
        # Génération des identifiants
        username = group_name[0:3].upper()+"-"+str(random.randint(10000000, 99999999))
        password = User.objects.make_random_password()
        
        # Création de l'utilisateur
        user = User.objects.create_user(
            username=username,
            email=data.get('email', ''),
            password=password
        )
        user.groups.add(group)
        
        # Création du profil
        hospital = Hospital.objects.get(id=data['hospital_id']) if data.get('hospital_id') else None
        service = Service.objects.get(id=data['service_id']) if data.get('service_id') else None
        speciality = Speciality.objects.get(id=data['speciality_id']) if data.get('speciality_id') else None
        
        profile = Profile.objects.create(
            user=user,
            firstname=data['firstname'],
            lastname=data['lastname'],
            tel=data['tel'],
            sexe=data.get('sexe', 'M'),
            address=data.get('address', ''),
            hospital=hospital,
            service=service,
            speciality=speciality,
            created_by=admin_user
        )

        # Créer le statut de changement de mot de passe obligatoire
        UserPasswordStatus.objects.create(
            user=user,
            must_change_password=True
        )

        # Envoyer l'email de bienvenue avec les identifiants
        role_name = EmailNotificationService.get_role_display_name(user)
        email_success, email_message = EmailNotificationService.send_new_user_credentials(
            user, password, role_name
        )

        response_data = {
            'status': 'success',
            'message': 'Agent créé avec succès',
            'agent_id': profile.id,
            'username': username,
            'password': password,
            'email_sent': email_success
        }

        if not email_success:
            response_data['email_error'] = email_message

        return JsonResponse(response_data)
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Données JSON invalides'}, status=400)
    except KeyError as e:
        return JsonResponse({'error': f'Champ requis manquant: {str(e)}'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@transaction.atomic
def create_patient(request):
    """Créer un nouveau patient"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
    
    try:
        data = json.loads(request.body)
        
        # Vérification de l'authentification (simplifié)
        auth_token = request.headers.get('Authorization')
        if not auth_token:
            return JsonResponse({'error': 'Non autorisé'}, status=401)
        
        try:
            user_id = int(auth_token.split(' ')[1])
            agent_user = User.objects.get(id=user_id)
            if not (agent_user.groups.filter(name='docteur').exists() or 
                    agent_user.groups.filter(name='assistant').exists() or
                    agent_user.groups.filter(name='admin').exists()):
                return JsonResponse({'error': 'Accès refusé'}, status=403)
            agent_profile = Profile.objects.get(user=agent_user)
        except (ValueError, User.DoesNotExist, Profile.DoesNotExist):
            return JsonResponse({'error': 'Token invalide'}, status=401)
        
        # Création du patient
        group = Group.objects.get(name='patient')
        
        # Génération des identifiants
        username = group.name[0:3].upper()+"-"+str(random.randint(10000000, 99999999))
        password = User.objects.make_random_password()
        
        # Création de l'utilisateur
        user = User.objects.create_user(
            username=username,
            email=data.get('email', ''),
            password=password
        )
        user.groups.add(group)
        
        # Récupération de la langue
        language = Language.objects.get(id=data['language_id']) if data.get('language_id') else None
        
        # Création du profil patient
        profile = Profile.objects.create(
            user=user,
            firstname=data['firstname'],
            lastname=data['lastname'],
            tel=data['tel'],
            sexe=data.get('sexe', 'F'),
            birth_date=datetime.strptime(data['birth_date'], '%Y-%m-%d').date() if data.get('birth_date') else None,
            address=data.get('address', ''),
            language=language,
            occupation=data.get('occupation', ''),
            assurance=data.get('assurance', ''),
            special_marks=data.get('special_marks', ''),
            personal_history=data.get('personal_history', ''),
            family_history=data.get('family_history', ''),
            allergies=data.get('allergies', ''),
            vaccinations=data.get('vaccinations', ''),
            child=data.get('is_child', False),
            study_level=data.get('study_level', ''),
            husband_name=data.get('husband_name', ''),
            husband_tel=data.get('husband_tel', ''),
            created_by=agent_user
        )
        
        # Créer le statut de changement de mot de passe obligatoire
        UserPasswordStatus.objects.create(
            user=user,
            must_change_password=True
        )

        # Envoyer l'email de bienvenue avec les identifiants
        role_name = EmailNotificationService.get_role_display_name(user)
        email_success, email_message = EmailNotificationService.send_new_user_credentials(
            user, password, role_name
        )

        # Relation médecin-patient si c'est un médecin
        if agent_user.groups.filter(name='docteur').exists():
            MapDoctorPatient.objects.create(
                doctor=agent_profile,
                patient=profile
            )
        
        # Informations supplémentaires
        other_informations = data.get('other_informations', {})
        for key, value in other_informations.items():
            OtherInformation.objects.create(
                profile=profile,
                key=key,
                value=value
            )
        
        # Créer une grossesse si la patiente est enceinte et non enfant
        if data.get('is_pregnant', False) and not data.get('is_child', False):
            pregnancy = Pregnancy.objects.create(
                state='ongoing',
                situation=data.get('pregnancy_situation', 'Normal'),
                description=data.get('pregnancy_description', ''),
                term=data.get('pregnancy_term', ''),
                start_date=datetime.strptime(data.get('pregnancy_start_date', data.get('birth_date')), '%Y-%m-%d').date(),
                mother_state=data.get('mother_state', 'Normal')
            )
            
            MotherPregnancy.objects.create(
                mother=profile,
                pregnancy=pregnancy
            )
        
        # Relation mère-enfant si c'est un enfant
        if data.get('is_child', False) and data.get('mother_id'):
            try:
                mother = Profile.objects.get(id=data['mother_id'])
                MapMotherChild.objects.create(
                    mother=mother,
                    child=profile
                )
            except Profile.DoesNotExist:
                pass
        
        response_data = {
            'status': 'success',
            'message': 'Patient créé avec succès',
            'patient_id': profile.id,
            'username': username,
            'password': password,
            'email_sent': email_success
        }

        if not email_success:
            response_data['email_error'] = email_message

        return JsonResponse(response_data)
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Données JSON invalides'}, status=400)
    except KeyError as e:
        return JsonResponse({'error': f'Champ requis manquant: {str(e)}'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@transaction.atomic
def sync_mobile_data(request):
    """Synchroniser les données collectées hors ligne"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
    
    try:
        data = json.loads(request.body)
        auth_token = request.headers.get('Authorization')
        
        if not auth_token:
            return JsonResponse({'error': 'Non autorisé'}, status=401)
        
        # Validation de l'utilisateur (simplifié)
        try:
            user_id = int(auth_token.split(' ')[1])
            agent_user = User.objects.get(id=user_id)
            if not (agent_user.groups.filter(name='docteur').exists() or 
                   agent_user.groups.filter(name='assistant').exists() or
                   agent_user.groups.filter(name='admin').exists()):
                return JsonResponse({'error': 'Accès refusé'}, status=403)
        except (ValueError, User.DoesNotExist):
            return JsonResponse({'error': 'Token invalide'}, status=401)
        
        # Processus de synchronisation
        patients_data = data.get('patients', [])
        pregnancies_data = data.get('pregnancies', [])
        appointments_data = data.get('appointments', [])
        
        processed = {
            'patients': [],
            'pregnancies': [],
            'appointments': []
        }
        
        # Synchroniser les patients
        for patient_data in patients_data:
            # Vérifier si le patient existe déjà via l'ID mobile
            mobile_id = patient_data.get('mobile_id')
            
            # Création ou mise à jour du patient
            profile, created = Profile.objects.update_or_create(
                firstname=patient_data['firstname'],
                lastname=patient_data['lastname'],
                tel=patient_data['tel'],
                defaults={
                    'sexe': patient_data.get('sexe', 'F'),
                    'birth_date': datetime.strptime(patient_data.get('birth_date', '2000-01-01'), '%Y-%m-%d').date(),
                    'address': patient_data.get('address', ''),
                    'occupation': patient_data.get('occupation', ''),
                    'assurance': patient_data.get('assurance', ''),
                    'special_marks': patient_data.get('special_marks', ''),
                    'personal_history': patient_data.get('personal_history', ''),
                    'family_history': patient_data.get('family_history', ''),
                    'allergies': patient_data.get('allergies', ''),
                    'vaccinations': patient_data.get('vaccinations', ''),
                    'child': patient_data.get('is_child', False),
                    'study_level': patient_data.get('study_level', ''),
                    'husband_name': patient_data.get('husband_name', ''),
                    'husband_tel': patient_data.get('husband_tel', '')
                }
            )
            
            processed['patients'].append({
                'mobile_id': mobile_id,
                'server_id': profile.id,
                'created': created
            })
        
        # Synchroniser les grossesses
        for pregnancy_data in pregnancies_data:
            mobile_mother_id = pregnancy_data.get('mother_mobile_id')
            server_mother_id = pregnancy_data.get('mother_id')
            
            # Trouver la mère
            mother = None
            if server_mother_id:
                mother = Profile.objects.filter(id=server_mother_id, child=False).first()
            elif mobile_mother_id:
                # Rechercher dans les patients déjà traités
                for p in processed['patients']:
                    if p['mobile_id'] == mobile_mother_id:
                        mother = Profile.objects.filter(id=p['server_id'], child=False).first()
                        break
            
            if not mother:
                continue
                
            # Création ou mise à jour de la grossesse
            pregnancy, created = Pregnancy.objects.update_or_create(
                start_date=datetime.strptime(pregnancy_data.get('start_date', '2000-01-01'), '%Y-%m-%d').date(),
                defaults={
                    'state': pregnancy_data.get('state', 'ongoing'),
                    'situation': pregnancy_data.get('situation', 'Normal'),
                    'description': pregnancy_data.get('description', ''),
                    'term': pregnancy_data.get('term', ''),
                    'mother_state': pregnancy_data.get('mother_state', 'Normal'),
                    'children_state': pregnancy_data.get('children_state', '')
                }
            )
            
            # Lier la grossesse à la mère
            MotherPregnancy.objects.get_or_create(mother=mother, pregnancy=pregnancy)
            
            processed['pregnancies'].append({
                'mobile_id': pregnancy_data.get('mobile_id'),
                'server_id': pregnancy.id,
                'created': created
            })
        
        # Synchroniser les rendez-vous
        for appointment_data in appointments_data:
            patient_id = appointment_data.get('patient_id')
            doctor_id = appointment_data.get('doctor_id')
            
            patient = Profile.objects.filter(id=patient_id).first()
            doctor = Profile.objects.filter(id=doctor_id).first()
            
            if not patient or not doctor:
                continue
                
            appointment, created = Appointment.objects.update_or_create(
                consul_date=datetime.strptime(appointment_data.get('consul_date'), '%Y-%m-%d').date(),
                consul_hour=datetime.strptime(appointment_data.get('consul_hour'), '%H:%M').time(),
                patient=patient,
                doctor=doctor,
                defaults={
                    'appointment_type': appointment_data.get('appointment_type', ''),
                    'state': appointment_data.get('state', 'EN ATTENTE'),
                    'consul_data': appointment_data.get('consul_data', ''),
                    'consul_resume': appointment_data.get('consul_resume', ''),
                    'consul_decisions': appointment_data.get('consul_decisions', '')
                }
            )
            
            processed['appointments'].append({
                'mobile_id': appointment_data.get('mobile_id'),
                'server_id': appointment.id,
                'created': created
            })
        
        return JsonResponse({
            'status': 'success',
            'message': 'Données synchronisées avec succès',
            'processed': processed
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Données JSON invalides'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
def get_patients(request):
    """Récupérer la liste des patients pour un agent de santé"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
    
    try:
        auth_token = request.headers.get('Authorization')
        if not auth_token:
            return JsonResponse({'error': 'Non autorisé'}, status=401)
        
        try:
            user_id = int(auth_token.split(' ')[1])
            agent_user = User.objects.get(id=user_id)
            agent_profile = Profile.objects.get(user=agent_user)
        except (ValueError, User.DoesNotExist, Profile.DoesNotExist):
            return JsonResponse({'error': 'Token invalide'}, status=401)
        
        patients = []
        
        # Si c'est un médecin, récupérer ses patients
        if agent_user.groups.filter(name='docteur').exists():
            doctor_patients = MapDoctorPatient.objects.filter(doctor=agent_profile)
            for dp in doctor_patients:
                patient = dp.patient
                patients.append({
                    'id': patient.id,
                    'firstname': patient.firstname,
                    'lastname': patient.lastname,
                    'tel': patient.tel,
                    'sexe': patient.sexe,
                    'birth_date': patient.birth_date.strftime('%Y-%m-%d') if patient.birth_date else None,
                    'address': patient.address,
                    'is_child': bool(patient.child),
                    'created_at': patient.created_at.strftime('%Y-%m-%d %H:%M:%S')
                })
        # Pour les assistants et admins, tous les patients
        elif agent_user.groups.filter(name='assistant').exists() or agent_user.groups.filter(name='admin').exists():
            all_patients = Profile.objects.filter(user__groups__name='patient')
            for patient in all_patients:
                patients.append({
                    'id': patient.id,
                    'firstname': patient.firstname,
                    'lastname': patient.lastname,
                    'tel': patient.tel,
                    'sexe': patient.sexe,
                    'birth_date': patient.birth_date.strftime('%Y-%m-%d') if patient.birth_date else None,
                    'address': patient.address,
                    'is_child': bool(patient.child),
                    'created_at': patient.created_at.strftime('%Y-%m-%d %H:%M:%S')
                })
        
        return JsonResponse({
            'status': 'success',
            'patients': patients
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
def get_health_centers(request):
    """Récupérer la liste des centres de santé"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
    
    try:
        hospitals = Hospital.objects.all()
        hospitals_data = []
        
        for hospital in hospitals:
            hospitals_data.append({
                'id': hospital.id,
                'name': hospital.name,
                'location': hospital.location
            })
        
        return JsonResponse({
            'status': 'success',
            'health_centers': hospitals_data
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
@transaction.atomic
def create_health_center(request):
    """Créer un nouveau centre de santé"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
    
    try:
        data = json.loads(request.body)
        
        # Vérification de l'authentification
        auth_token = request.headers.get('Authorization')
        if not auth_token:
            return JsonResponse({'error': 'Non autorisé'}, status=401)
        
        try:
            user_id = int(auth_token.split(' ')[1])
            admin_user = User.objects.get(id=user_id)
            if not admin_user.groups.filter(name='admin').exists():
                return JsonResponse({'error': 'Accès refusé'}, status=403)
        except (ValueError, User.DoesNotExist):
            return JsonResponse({'error': 'Token invalide'}, status=401)
        
        # Création du centre de santé
        hospital = Hospital.objects.create(
            name=data['name'],
            location=data.get('location', '')
        )
        
        return JsonResponse({
            'status': 'success',
            'message': 'Centre de santé créé avec succès',
            'health_center_id': hospital.id
        })
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Données JSON invalides'}, status=400)
    except KeyError as e:
        return JsonResponse({'error': f'Champ requis manquant: {str(e)}'}, status=400)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@csrf_exempt
def get_agents(request):
    """Récupérer la liste des agents de santé"""
    if request.method != 'GET':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
    
    try:
        # Vérification de l'authentification
        auth_token = request.headers.get('Authorization')
        if not auth_token:
            return JsonResponse({'error': 'Non autorisé'}, status=401)
        
        try:
            user_id = int(auth_token.split(' ')[1])
            admin_user = User.objects.get(id=user_id)
            if not admin_user.groups.filter(name='admin').exists():
                return JsonResponse({'error': 'Accès refusé'}, status=403)
        except (ValueError, User.DoesNotExist):
            return JsonResponse({'error': 'Token invalide'}, status=401)
        
        # Récupérer tous les médecins et assistants
        doctors = Profile.objects.filter(user__groups__name='docteur')
        assistants = Profile.objects.filter(user__groups__name='assistant')
        
        agents = []
        
        for doctor in doctors:
            agents.append({
                'id': doctor.id,
                'firstname': doctor.firstname,
                'lastname': doctor.lastname,
                'tel': doctor.tel,
                'role': 'docteur',
                'hospital': doctor.hospital.name if doctor.hospital else None,
                'service': doctor.service.name if doctor.service else None,
                'speciality': doctor.speciality.name if doctor.speciality else None
            })
        
        for assistant in assistants:
            agents.append({
                'id': assistant.id,
                'firstname': assistant.firstname,
                'lastname': assistant.lastname,
                'tel': assistant.tel,
                'role': 'assistant',
                'hospital': assistant.hospital.name if assistant.hospital else None,
                'service': assistant.service.name if assistant.service else None
            })
        
        return JsonResponse({
            'status': 'success',
            'agents': agents
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)