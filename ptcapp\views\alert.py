from django.shortcuts import redirect, render,get_object_or_404,HttpResponseRedirect

# relative import of forms
from ptcapp.models import Alert
from ptcapp.forms.alert import AlertForm


def create_view(request):
    context = {}
    form = AlertForm(request.POST or None)
    if form.is_valid():
        form.save()
        return HttpResponseRedirect("/alert")

    context['form'] = form
    return render(request, "alert/form.html", context)



def list_view(request):
    context ={}
 
    context["dataset"] = Alert.objects.all()
    return render(request, "alert/list.html", context)


def detail_view(request, id):
    
    context ={}
 
    context["data"] = Alert.objects.get(id = id)
         
    return render(request, "alert/detail.html", context)


def update_view(request, id):

    context ={}
 
    obj = get_object_or_404(Alert, id = id)
 
    form = AlertForm(request.POST, instance = obj)
 
    if form.is_valid():
        form.save()
        return HttpResponseRedirect("/")
 
    context["form"] = form
 
    return render(request, "alert/update.html", context)
    

def delete_view(request, id):
    context ={}
 
    obj = get_object_or_404(Alert, id = id)
 
    if request.method =="POST":
        obj.delete()
        return HttpResponseRedirect("/")
 
    return render(request, "alert/delete.html", context)
