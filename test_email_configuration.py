#!/usr/bin/env python
"""
Script de test complet pour valider la configuration email de PTC Care
avec les identifiants SMTP fournis.
"""

import os
import sys
import django
from django.conf import settings

# Configuration de Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ptccare.settings')
django.setup()

from django.core.mail import send_mail, EmailMultiAlternatives
from django.contrib.auth.models import User, Group
from ptcapp.models.profile import Profile
from ptcapp.models.password_reset import PasswordResetToken, UserPasswordStatus
from ptcapp.services.email_service import EmailNotificationService
import random


def test_smtp_connection():
    """Teste la connexion SMTP de base."""
    print("🔌 TEST DE CONNEXION SMTP")
    print("=" * 50)
    
    print(f"📧 Configuration Email:")
    print(f"   Host: {settings.EMAIL_HOST}")
    print(f"   Port: {settings.EMAIL_PORT}")
    print(f"   TLS: {settings.EMAIL_USE_TLS}")
    print(f"   User: {settings.EMAIL_HOST_USER}")
    print(f"   From: {settings.DEFAULT_FROM_EMAIL}")
    
    try:
        # Test d'envoi simple
        send_mail(
            subject='Test PTC Care - Connexion SMTP',
            message='Ceci est un test de connexion SMTP pour PTC Care.',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[settings.EMAIL_HOST_USER],
            fail_silently=False,
        )
        print("✅ Connexion SMTP réussie - Email de test envoyé")
        return True
        
    except Exception as e:
        print(f"❌ Erreur de connexion SMTP: {str(e)}")
        return False


def test_user_creation_email(user_type='patient', test_email=None):
    """Teste l'envoi d'email lors de la création d'un utilisateur."""
    print(f"\n📧 TEST CRÉATION UTILISATEUR - {user_type.upper()}")
    print("=" * 50)
    
    # Email de test (utilise l'email configuré par défaut)
    if not test_email:
        test_email = settings.EMAIL_HOST_USER
    
    # Nettoyer les utilisateurs de test existants
    User.objects.filter(email=test_email).delete()
    
    try:
        # Créer l'utilisateur selon le type
        group = Group.objects.get(name=user_type)
        username = user_type[0:3].upper() + "-" + str(random.randint(10000000, 99999999))
        password = User.objects.make_random_password()
        
        user = User.objects.create_user(
            username=username,
            email=test_email,
            password=password
        )
        user.groups.add(group)
        
        # Créer le profil
        profile = Profile.objects.create(
            user=user,
            firstname='Test',
            lastname=f'{user_type.capitalize()}',
            tel='+22912345000',
            sexe='M' if user_type != 'patient' else 'F',
            address=f'123 Test {user_type.capitalize()} Street'
        )
        
        print(f"✅ Utilisateur créé:")
        print(f"   Type: {user_type}")
        print(f"   Username: {username}")
        print(f"   Email: {test_email}")
        print(f"   Password: {password}")
        
        # Créer le statut de changement obligatoire
        status = UserPasswordStatus.objects.create(
            user=user,
            must_change_password=True
        )
        
        # Tester l'envoi d'email
        role_name = EmailNotificationService.get_role_display_name(user)
        success, message = EmailNotificationService.send_new_user_credentials(
            user, password, role_name
        )
        
        if success:
            print(f"✅ Email de bienvenue envoyé avec succès")
            
            # Vérifier la création du token
            try:
                token = PasswordResetToken.objects.get(user=user)
                print(f"✅ Token créé: {token.token[:20]}...")
                print(f"   URL de changement: {settings.PTCCARE_BASE_URL}/change-password/{token.token}/")
                print(f"   Expire le: {token.expires_at}")
                
                return user, token, password
                
            except PasswordResetToken.DoesNotExist:
                print("❌ Token non créé")
                return None, None, None
        else:
            print(f"❌ Erreur d'envoi d'email: {message}")
            return None, None, None
            
    except Exception as e:
        print(f"❌ Erreur lors de la création: {str(e)}")
        return None, None, None


def test_password_change_email(user):
    """Teste l'envoi d'email de confirmation de changement de mot de passe."""
    print(f"\n🔒 TEST EMAIL CONFIRMATION CHANGEMENT")
    print("=" * 50)
    
    try:
        # Simuler le changement de mot de passe
        new_password = 'NewSecurePassword123!'
        user.set_password(new_password)
        user.save()
        
        # Mettre à jour le statut
        try:
            status = UserPasswordStatus.objects.get(user=user)
            status.mark_password_changed()
        except UserPasswordStatus.DoesNotExist:
            UserPasswordStatus.objects.create(
                user=user,
                must_change_password=False,
                first_login_completed=True
            )
        
        # Envoyer l'email de confirmation
        success, message = EmailNotificationService.send_password_changed_confirmation(user)
        
        if success:
            print(f"✅ Email de confirmation envoyé avec succès")
            return True
        else:
            print(f"❌ Erreur d'envoi de confirmation: {message}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test de confirmation: {str(e)}")
        return False


def test_all_user_types():
    """Teste l'envoi d'emails pour tous les types d'utilisateurs."""
    print(f"\n👥 TEST TOUS TYPES D'UTILISATEURS")
    print("=" * 50)
    
    user_types = ['admin', 'docteur', 'assistant', 'patient']
    results = []
    created_users = []
    
    for user_type in user_types:
        print(f"\n🔍 Test pour {user_type}...")
        user, token, password = test_user_creation_email(user_type)
        
        if user:
            results.append((user_type, True))
            created_users.append(user)
            
            # Test de confirmation pour ce type
            confirmation_success = test_password_change_email(user)
            print(f"   Confirmation: {'✅' if confirmation_success else '❌'}")
        else:
            results.append((user_type, False))
    
    # Résumé
    print(f"\n📊 RÉSUMÉ PAR TYPE:")
    for user_type, success in results:
        status = "✅ RÉUSSI" if success else "❌ ÉCHOUÉ"
        print(f"   {user_type.capitalize()}: {status}")
    
    # Nettoyer les utilisateurs de test
    print(f"\n🧹 Nettoyage des utilisateurs de test...")
    for user in created_users:
        user.delete()
    print(f"   {len(created_users)} utilisateurs supprimés")
    
    return results


def test_template_rendering():
    """Teste le rendu des templates d'email."""
    print(f"\n🎨 TEST RENDU DES TEMPLATES")
    print("=" * 50)
    
    try:
        from django.template.loader import render_to_string
        
        # Créer un utilisateur de test pour le contexte
        test_user = User(
            username='TEST-12345678',
            email='<EMAIL>',
            first_name='Test',
            last_name='User'
        )
        
        # Contexte de test
        context = {
            'user': test_user,
            'full_name': 'Test User',
            'username': 'TEST-12345678',
            'email': '<EMAIL>',
            'password': 'TempPassword123',
            'role_name': 'Patient',
            'password_change_url': 'http://localhost:8000/change-password/test-token/',
            'token_expiry_hours': 48,
            'support_email': '<EMAIL>'
        }
        
        # Test template de bienvenue HTML
        try:
            html_content = render_to_string('emails/new_user_credentials.html', context)
            print("✅ Template HTML bienvenue rendu avec succès")
            print(f"   Longueur: {len(html_content)} caractères")
        except Exception as e:
            print(f"❌ Erreur template HTML bienvenue: {str(e)}")
        
        # Test template de bienvenue texte
        try:
            text_content = render_to_string('emails/new_user_credentials.txt', context)
            print("✅ Template texte bienvenue rendu avec succès")
            print(f"   Longueur: {len(text_content)} caractères")
        except Exception as e:
            print(f"❌ Erreur template texte bienvenue: {str(e)}")
        
        # Test template de confirmation HTML
        try:
            html_confirm = render_to_string('emails/password_changed_confirmation.html', context)
            print("✅ Template HTML confirmation rendu avec succès")
            print(f"   Longueur: {len(html_confirm)} caractères")
        except Exception as e:
            print(f"❌ Erreur template HTML confirmation: {str(e)}")
        
        # Test template de confirmation texte
        try:
            text_confirm = render_to_string('emails/password_changed_confirmation.txt', context)
            print("✅ Template texte confirmation rendu avec succès")
            print(f"   Longueur: {len(text_confirm)} caractères")
        except Exception as e:
            print(f"❌ Erreur template texte confirmation: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur générale de rendu: {str(e)}")
        return False


def main():
    """Fonction principale de test."""
    print("🧪 TEST COMPLET DE CONFIGURATION EMAIL PTC CARE")
    print("Identifiants SMTP: <EMAIL>")
    print("=" * 80)
    
    tests = [
        ("Connexion SMTP", test_smtp_connection),
        ("Rendu des templates", test_template_rendering),
        ("Tous types d'utilisateurs", test_all_user_types),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔍 Exécution: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Erreur critique dans {test_name}: {str(e)}")
            results.append((test_name, False))
    
    # Résumé final
    print("\n" + "=" * 80)
    print("📊 RÉSUMÉ FINAL DES TESTS")
    print("=" * 80)
    
    passed = 0
    for test_name, result in results:
        if isinstance(result, list):  # Pour le test de tous les types
            success_count = sum(1 for _, success in result if success)
            total_count = len(result)
            status = f"✅ {success_count}/{total_count} RÉUSSIS" if success_count == total_count else f"⚠️ {success_count}/{total_count} RÉUSSIS"
            if success_count == total_count:
                passed += 1
        else:
            status = "✅ RÉUSSI" if result else "❌ ÉCHOUÉ"
            if result:
                passed += 1
        
        print(f"  {test_name}: {status}")
    
    print(f"\n📈 Résultat global: {passed}/{len(tests)} tests réussis")
    
    if passed == len(tests):
        print("\n🎉 CONFIGURATION EMAIL ENTIÈREMENT OPÉRATIONNELLE !")
        print("✅ Le système peut maintenant envoyer des emails en production")
        print("📧 Vérifiez votre boîte email pour les messages de test")
    else:
        print("\n⚠️  Certains tests ont échoué")
        print("🔧 Vérifiez la configuration SMTP et les logs d'erreur")
    
    print(f"\n📧 Email de test utilisé: {settings.EMAIL_HOST_USER}")
    print("💡 Vérifiez votre boîte de réception et le dossier spam")


if __name__ == '__main__':
    main()
