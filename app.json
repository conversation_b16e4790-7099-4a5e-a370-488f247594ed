{"name": "PTC Care", "description": "Application de gestion des soins prénataux avec API mobile", "repository": "https://github.com/votre-username/ptccare-web", "logo": "https://ptccare.herokuapp.com/static/images/logo.png", "keywords": ["django", "healthcare", "prenatal", "mobile-api", "jwt"], "stack": "heroku-22", "buildpacks": [{"url": "heroku/python"}], "formation": {"web": {"quantity": 1, "size": "basic"}, "worker": {"quantity": 1, "size": "basic"}}, "addons": [{"plan": "heroku-postgresql:essential-0"}, {"plan": "heroku-redis:mini"}], "env": {"SECRET_KEY": {"description": "Clé secrète Django pour la sécurité", "generator": "secret"}, "DEBUG": {"description": "Mode debug (False pour production)", "value": "False"}, "ADMIN_EMAIL": {"description": "<PERSON>ail de l'administrateur principal", "value": "<EMAIL>"}, "ADMIN_PASSWORD": {"description": "Mot de passe de l'administrateur principal", "value": "admin123"}, "EMAIL_HOST_USER": {"description": "Email SMTP pour l'envoi d'emails", "required": false}, "EMAIL_HOST_PASSWORD": {"description": "Mot de passe email SMTP", "required": false}, "DEFAULT_FROM_EMAIL": {"description": "Email expéditeur par défaut", "value": "<EMAIL>"}, "SMS_ENABLED": {"description": "Activer les fonctionnalités SMS", "value": "False"}, "FIREBASE_PROJECT_ID": {"description": "ID du projet Firebase", "required": false}, "FIREBASE_PRIVATE_KEY": {"description": "Clé privée Firebase", "required": false}, "FIREBASE_CLIENT_EMAIL": {"description": "Email client Firebase", "required": false}}, "scripts": {"postdeploy": "python manage.py migrate && python manage.py collectstatic --noinput"}}