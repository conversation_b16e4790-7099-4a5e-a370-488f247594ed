from django.shortcuts import redirect, render,get_object_or_404,HttpResponseRedirect
from django.http import JsonResponse

# relative import of forms
from ptcapp.models import Service
from ptcapp.forms.service import ServiceForm
from ptcapp.models.hospital import Hospital


def create_view(request):
    if request.method == 'POST':
        name = request.POST['name']
        description = request.POST['description']
        hospital = get_object_or_404(Hospital, id=request.POST['hospital']) 
        #hospital_id = request.POST.get('hospital', 1);
        service = Service.objects.create(name=name, description = description, hospital = hospital)
        #service.save()
        return JsonResponse({'service':int(service.id), 'success' : True})
    return JsonResponse({'service':int(service.id), 'success' : False})


def update_view(request, id):
    obj = get_object_or_404(Service, id = id)
    
    if request.method == 'POST':
        obj.name = request.POST['name']
        obj.description = request.POST['description']
        obj.hospital = get_object_or_404(Hospital, id=request.POST['hospital']) 
        obj.save()
        return JsonResponse({'service':int(obj.id), 'success' : True})
    return JsonResponse({'service':int(obj.id), 'success' : False})
    

def delete_view(request, id):
    obj = get_object_or_404(Service, id = id)
 
    if request.method =="POST":
        obj.delete()
        return JsonResponse({'success' : True})
    return JsonResponse({'service':int(obj.id), 'success' : False})
