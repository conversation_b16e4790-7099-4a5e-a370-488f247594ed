# Guide d'Intégration PTC Care API - Application Mobile Personnalisée

## 📋 Vue d'Ensemble

Ce guide vous accompagne dans l'intégration des APIs PTC Care pour votre application mobile avec gestion d'authentification multi-rôles, dashboard admin/agent et synchronisation hors ligne.

## 🔗 Mapping des Rôles

### Correspondance Rôles API ↔ App Mobile
| Rôle API | Rôle App Mobile | Permissions |
|----------|-----------------|-------------|
| `admin` | **Administrateur** | Gestion établissements + agents |
| `docteur` | **Administrateur** | Gestion établissements + agents |
| `assistant` | **Agent de Santé** | Gestion patients uniquement |
| `patient` | N/A | Non applicable |

## 🔐 Implémentation Authentification

### 1. Configuration API Client

#### Android (Kotlin)
```kotlin
object ApiConfig {
    const val BASE_URL = "http://your-server.com" // Remplacez par votre URL
    const val TIMEOUT_SECONDS = 30L
}

// Modèles de données
data class LoginRequest(
    val email: String,
    val password: String
)

data class LoginResponse(
    val status: String,
    val user_id: Int,
    val username: String,
    val profile_id: Int,
    val role: String, // "admin", "agent", "patient"
    val name: String,
    val hospital_id: Int?,
    val service_id: Int?,
    val speciality_id: Int?
)

data class ApiError(
    val error: String,
    val message: String? = null
)

// Service API
interface PTCCareApiService {
    @POST("/api/login/")
    suspend fun login(@Body request: LoginRequest): Response<LoginResponse>
    
    @POST("/api/change-password/")
    suspend fun changePassword(@Body request: ChangePasswordRequest): Response<ApiResponse>
    
    @GET("/api/initial-data/")
    suspend fun getInitialData(): Response<InitialDataResponse>
    
    @GET("/api/health-centers/")
    suspend fun getHealthCenters(): Response<HealthCentersResponse>
    
    @POST("/api/create-health-center/")
    suspend fun createHealthCenter(
        @Header("Authorization") token: String,
        @Body request: CreateHealthCenterRequest
    ): Response<CreateHealthCenterResponse>
    
    @GET("/api/agents/")
    suspend fun getAgents(@Header("Authorization") token: String): Response<AgentsResponse>
    
    @POST("/api/create-health-agent/")
    suspend fun createHealthAgent(
        @Header("Authorization") token: String,
        @Body request: CreateHealthAgentRequest
    ): Response<CreateAgentResponse>
    
    @GET("/api/patients/")
    suspend fun getPatients(@Header("Authorization") token: String): Response<PatientsResponse>
    
    @POST("/api/create-patient/")
    suspend fun createPatient(
        @Header("Authorization") token: String,
        @Body request: CreatePatientRequest
    ): Response<CreatePatientResponse>
    
    @POST("/api/sync-mobile-data/")
    suspend fun syncMobileData(
        @Header("Authorization") token: String,
        @Body request: SyncDataRequest
    ): Response<SyncDataResponse>
}
```

### 2. Gestionnaire d'Authentification

```kotlin
class AuthManager(
    private val apiService: PTCCareApiService,
    private val secureStorage: SecureStorage
) {
    
    suspend fun login(email: String, password: String): Result<UserSession> {
        return try {
            val response = apiService.login(LoginRequest(email, password))
            
            if (response.isSuccessful) {
                val loginData = response.body()!!
                
                // Mapper les rôles pour votre app
                val appRole = when (loginData.role) {
                    "admin", "docteur" -> UserRole.ADMINISTRATOR
                    "assistant" -> UserRole.AGENT
                    else -> UserRole.PATIENT
                }
                
                val userSession = UserSession(
                    userId = loginData.user_id,
                    username = loginData.username,
                    name = loginData.name,
                    role = appRole,
                    token = "Bearer ${loginData.user_id}",
                    profileId = loginData.profile_id,
                    hospitalId = loginData.hospital_id,
                    serviceId = loginData.service_id,
                    specialityId = loginData.speciality_id
                )
                
                // Sauvegarder en sécurité
                secureStorage.saveUserSession(userSession)
                
                Result.success(userSession)
            } else {
                val error = response.errorBody()?.let { 
                    Gson().fromJson(it.string(), ApiError::class.java) 
                }
                Result.failure(Exception(error?.error ?: "Erreur de connexion"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    suspend fun changePassword(
        email: String, 
        currentPassword: String, 
        newPassword: String
    ): Result<String> {
        return try {
            val request = ChangePasswordRequest(
                email = email,
                current_password = currentPassword,
                new_password = newPassword
            )
            
            val response = apiService.changePassword(request)
            
            if (response.isSuccessful) {
                Result.success("Mot de passe changé avec succès")
            } else {
                val error = response.errorBody()?.let { 
                    Gson().fromJson(it.string(), ApiError::class.java) 
                }
                Result.failure(Exception(error?.error ?: "Erreur"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    fun getCurrentUser(): UserSession? {
        return secureStorage.getUserSession()
    }
    
    fun logout() {
        secureStorage.clearUserSession()
    }
    
    fun isLoggedIn(): Boolean {
        return secureStorage.getUserSession() != null
    }
}

// Modèles de données
data class UserSession(
    val userId: Int,
    val username: String,
    val name: String,
    val role: UserRole,
    val token: String,
    val profileId: Int,
    val hospitalId: Int?,
    val serviceId: Int?,
    val specialityId: Int?
)

enum class UserRole {
    ADMINISTRATOR, AGENT, PATIENT
}
```

### 3. Intercepteur pour Gestion du Changement de Mot de Passe

```kotlin
class PasswordChangeInterceptor(
    private val authManager: AuthManager
) : Interceptor {
    
    override fun intercept(chain: Interceptor.Chain): Response {
        val response = chain.proceed(chain.request())
        
        // Vérifier si changement de mot de passe requis
        if (response.code == 403) {
            try {
                val errorBody = response.body?.string()
                val error = Gson().fromJson(errorBody, ApiError::class.java)
                
                if (error.error == "password_change_required") {
                    // Déclencher l'écran de changement de mot de passe
                    PasswordChangeManager.showPasswordChangeScreen()
                }
            } catch (e: Exception) {
                // Ignore parsing errors
            }
        }
        
        return response
    }
}
```

## 📊 Implémentation Dashboard

### Dashboard Administrateur

```kotlin
class AdminDashboardViewModel(
    private val apiService: PTCCareApiService,
    private val authManager: AuthManager,
    private val localDatabase: LocalDatabase
) : ViewModel() {
    
    private val _dashboardData = MutableLiveData<AdminDashboardData>()
    val dashboardData: LiveData<AdminDashboardData> = _dashboardData
    
    suspend fun loadDashboardData() {
        try {
            val user = authManager.getCurrentUser()!!
            
            // Données locales pour les compteurs
            val localAgents = localDatabase.agentDao().countAgents()
            val localHealthCenters = localDatabase.healthCenterDao().countHealthCenters()
            
            // Données serveur pour synchronisation
            val agentsResponse = apiService.getAgents(user.token)
            val healthCentersResponse = apiService.getHealthCenters()
            
            if (agentsResponse.isSuccessful && healthCentersResponse.isSuccessful) {
                val serverAgents = agentsResponse.body()?.agents?.size ?: 0
                val serverHealthCenters = healthCentersResponse.body()?.health_centers?.size ?: 0
                
                _dashboardData.value = AdminDashboardData(
                    localAgentsCount = localAgents,
                    localHealthCentersCount = localHealthCenters,
                    serverAgentsCount = serverAgents,
                    serverHealthCentersCount = serverHealthCenters,
                    syncPending = localAgents != serverAgents || localHealthCenters != serverHealthCenters
                )
            }
        } catch (e: Exception) {
            // Gestion d'erreur
        }
    }
}

data class AdminDashboardData(
    val localAgentsCount: Int,
    val localHealthCentersCount: Int,
    val serverAgentsCount: Int,
    val serverHealthCentersCount: Int,
    val syncPending: Boolean
)
```

### Dashboard Agent de Santé

```kotlin
class AgentDashboardViewModel(
    private val apiService: PTCCareApiService,
    private val authManager: AuthManager,
    private val localDatabase: LocalDatabase
) : ViewModel() {
    
    private val _dashboardData = MutableLiveData<AgentDashboardData>()
    val dashboardData: LiveData<AgentDashboardData> = _dashboardData
    
    suspend fun loadDashboardData() {
        try {
            val user = authManager.getCurrentUser()!!
            
            // Données locales
            val localPatients = localDatabase.patientDao().countPatients()
            val pendingSync = localDatabase.syncQueueDao().countPendingSync()
            
            // Données serveur
            val patientsResponse = apiService.getPatients(user.token)
            val serverPatients = if (patientsResponse.isSuccessful) {
                patientsResponse.body()?.patients?.size ?: 0
            } else 0
            
            _dashboardData.value = AgentDashboardData(
                localPatientsCount = localPatients,
                serverPatientsCount = serverPatients,
                pendingSyncCount = pendingSync,
                syncNeeded = localPatients != serverPatients || pendingSync > 0
            )
        } catch (e: Exception) {
            // Gestion d'erreur
        }
    }
}

data class AgentDashboardData(
    val localPatientsCount: Int,
    val serverPatientsCount: Int,
    val pendingSyncCount: Int,
    val syncNeeded: Boolean
)
```

## 🏥 Gestion des Établissements (Admin)

```kotlin
class HealthCenterManager(
    private val apiService: PTCCareApiService,
    private val authManager: AuthManager,
    private val localDatabase: LocalDatabase
) {
    
    suspend fun getHealthCenters(): Result<List<HealthCenter>> {
        return try {
            // Essayer d'abord les données locales
            val localCenters = localDatabase.healthCenterDao().getAllHealthCenters()
            
            // Puis synchroniser avec le serveur
            val response = apiService.getHealthCenters()
            if (response.isSuccessful) {
                val serverCenters = response.body()?.health_centers?.map { serverCenter ->
                    HealthCenter(
                        id = serverCenter.id,
                        name = serverCenter.name,
                        location = serverCenter.location,
                        syncStatus = SyncStatus.SYNCED
                    )
                } ?: emptyList()
                
                // Mettre à jour la base locale
                localDatabase.healthCenterDao().insertAll(serverCenters)
                
                Result.success(serverCenters)
            } else {
                // Retourner les données locales en cas d'échec
                Result.success(localCenters)
            }
        } catch (e: Exception) {
            // Retourner les données locales en cas d'exception
            val localCenters = localDatabase.healthCenterDao().getAllHealthCenters()
            Result.success(localCenters)
        }
    }
    
    suspend fun createHealthCenter(name: String, location: String): Result<HealthCenter> {
        return try {
            val user = authManager.getCurrentUser()!!
            val request = CreateHealthCenterRequest(name, location)
            
            val response = apiService.createHealthCenter(user.token, request)
            
            if (response.isSuccessful) {
                val result = response.body()!!
                val healthCenter = HealthCenter(
                    id = result.health_center_id,
                    name = name,
                    location = location,
                    syncStatus = SyncStatus.SYNCED
                )
                
                // Sauvegarder localement
                localDatabase.healthCenterDao().insert(healthCenter)
                
                Result.success(healthCenter)
            } else {
                // Sauvegarder localement pour sync ultérieure
                val tempId = System.currentTimeMillis().toInt()
                val healthCenter = HealthCenter(
                    id = tempId,
                    name = name,
                    location = location,
                    syncStatus = SyncStatus.PENDING
                )
                
                localDatabase.healthCenterDao().insert(healthCenter)
                localDatabase.syncQueueDao().addToQueue(
                    SyncQueueItem(
                        type = "health_center",
                        localId = tempId,
                        data = Gson().toJson(request)
                    )
                )
                
                Result.success(healthCenter)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
```

## 👥 Gestion des Agents (Admin)

```kotlin
class AgentManager(
    private val apiService: PTCCareApiService,
    private val authManager: AuthManager,
    private val localDatabase: LocalDatabase
) {
    
    suspend fun createHealthAgent(agentData: CreateHealthAgentRequest): Result<Agent> {
        return try {
            val user = authManager.getCurrentUser()!!
            
            val response = apiService.createHealthAgent(user.token, agentData)
            
            if (response.isSuccessful) {
                val result = response.body()!!
                val agent = Agent(
                    id = result.agent_id,
                    firstname = agentData.firstname,
                    lastname = agentData.lastname,
                    email = agentData.email,
                    tel = agentData.tel,
                    role = agentData.role,
                    username = result.username,
                    emailSent = result.email_sent,
                    syncStatus = SyncStatus.SYNCED
                )
                
                // Sauvegarder localement
                localDatabase.agentDao().insert(agent)
                
                Result.success(agent)
            } else {
                // Sauvegarder localement pour sync ultérieure
                val tempId = System.currentTimeMillis().toInt()
                val agent = Agent(
                    id = tempId,
                    firstname = agentData.firstname,
                    lastname = agentData.lastname,
                    email = agentData.email,
                    tel = agentData.tel,
                    role = agentData.role,
                    username = "",
                    emailSent = false,
                    syncStatus = SyncStatus.PENDING
                )
                
                localDatabase.agentDao().insert(agent)
                localDatabase.syncQueueDao().addToQueue(
                    SyncQueueItem(
                        type = "health_agent",
                        localId = tempId,
                        data = Gson().toJson(agentData)
                    )
                )
                
                Result.success(agent)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

data class CreateHealthAgentRequest(
    val firstname: String,
    val lastname: String,
    val tel: String,
    val email: String,
    val role: String, // "docteur" ou "assistant"
    val sexe: String,
    val address: String,
    val hospital_id: Int,
    val service_id: Int,
    val speciality_id: Int
)
```

## 👩‍⚕️ Gestion des Patients (Agent)

```kotlin
class PatientManager(
    private val apiService: PTCCareApiService,
    private val authManager: AuthManager,
    private val localDatabase: LocalDatabase
) {
    
    suspend fun createPatient(patientData: CreatePatientRequest): Result<Patient> {
        return try {
            val user = authManager.getCurrentUser()!!
            
            val response = apiService.createPatient(user.token, patientData)
            
            if (response.isSuccessful) {
                val result = response.body()!!
                val patient = Patient(
                    id = result.patient_id,
                    firstname = patientData.firstname,
                    lastname = patientData.lastname,
                    email = patientData.email,
                    tel = patientData.tel,
                    sexe = patientData.sexe,
                    birthDate = patientData.birth_date,
                    address = patientData.address,
                    isPregnant = patientData.is_pregnant,
                    pregnancyInfo = patientData.pregnancy_info,
                    familyInfo = patientData.family_info,
                    emailSent = result.email_sent,
                    syncStatus = SyncStatus.SYNCED
                )
                
                // Sauvegarder localement
                localDatabase.patientDao().insert(patient)
                
                Result.success(patient)
            } else {
                // Sauvegarder localement pour sync ultérieure
                val tempId = System.currentTimeMillis().toInt()
                val mobileId = UUID.randomUUID().toString()
                
                val patient = Patient(
                    id = tempId,
                    mobileId = mobileId,
                    firstname = patientData.firstname,
                    lastname = patientData.lastname,
                    email = patientData.email,
                    tel = patientData.tel,
                    sexe = patientData.sexe,
                    birthDate = patientData.birth_date,
                    address = patientData.address,
                    isPregnant = patientData.is_pregnant,
                    pregnancyInfo = patientData.pregnancy_info,
                    familyInfo = patientData.family_info,
                    emailSent = false,
                    syncStatus = SyncStatus.PENDING
                )
                
                localDatabase.patientDao().insert(patient)
                localDatabase.syncQueueDao().addToQueue(
                    SyncQueueItem(
                        type = "patient",
                        localId = tempId,
                        mobileId = mobileId,
                        data = Gson().toJson(patientData)
                    )
                )
                
                Result.success(patient)
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}

// Modèle de données pour le formulaire en 4 étapes
data class CreatePatientRequest(
    // Étape 1: Informations Personnelles
    val firstname: String,
    val lastname: String,
    val sexe: String,
    val birth_date: String,
    val tel: String,
    val address: String,
    val email: String,
    val language_id: Int,
    
    // Étape 2: Informations de Grossesse
    val is_pregnant: Boolean,
    val pregnancy_situation: String?,
    val pregnancy_description: String?,
    val pregnancy_term: String?,
    val pregnancy_start_date: String?,
    
    // Étape 3: Informations Familiales et Sociales
    val occupation: String?,
    val study_level: String?,
    val husband_name: String?,
    val husband_tel: String?,
    val assurance: String?,
    
    // Étape 4: Informations Médicales
    val personal_history: String?,
    val family_history: String?,
    val allergies: String?,
    val vaccinations: String?,
    val special_marks: String?,
    
    // Informations additionnelles
    val is_child: Boolean,
    val mother_id: Int?,
    val other_informations: Map<String, Any>?
) {
    // Propriétés dérivées pour faciliter l'usage
    val pregnancy_info: PregnancyInfo?
        get() = if (is_pregnant) {
            PregnancyInfo(
                situation = pregnancy_situation,
                description = pregnancy_description,
                term = pregnancy_term,
                startDate = pregnancy_start_date
            )
        } else null
    
    val family_info: FamilyInfo
        get() = FamilyInfo(
            occupation = occupation,
            studyLevel = study_level,
            husbandName = husband_name,
            husbandTel = husband_tel,
            assurance = assurance
        )
}

data class PregnancyInfo(
    val situation: String?,
    val description: String?,
    val term: String?,
    val startDate: String?
)

data class FamilyInfo(
    val occupation: String?,
    val studyLevel: String?,
    val husbandName: String?,
    val husbandTel: String?,
    val assurance: String?
)
```

## 🔄 Gestionnaire de Synchronisation

```kotlin
class SyncManager(
    private val apiService: PTCCareApiService,
    private val authManager: AuthManager,
    private val localDatabase: LocalDatabase
) {
    
    suspend fun syncData(): Result<SyncResult> {
        return try {
            val user = authManager.getCurrentUser()!!
            val pendingItems = localDatabase.syncQueueDao().getAllPendingSync()
            
            if (pendingItems.isEmpty()) {
                return Result.success(SyncResult(0, 0, emptyList()))
            }
            
            // Préparer les données pour la synchronisation
            val syncRequest = prepareSyncRequest(pendingItems)
            
            val response = apiService.syncMobileData(user.token, syncRequest)
            
            if (response.isSuccessful) {
                val result = response.body()!!
                
                // Mettre à jour les IDs locaux avec les IDs serveur
                updateLocalIdsWithServerIds(result.processed)
                
                // Marquer les items comme synchronisés
                markItemsAsSynced(pendingItems)
                
                Result.success(SyncResult(
                    totalItems = pendingItems.size,
                    syncedItems = result.processed.size,
                    processed = result.processed
                ))
            } else {
                Result.failure(Exception("Erreur de synchronisation"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
    
    private fun prepareSyncRequest(pendingItems: List<SyncQueueItem>): SyncDataRequest {
        val patients = mutableListOf<PatientSyncData>()
        val pregnancies = mutableListOf<PregnancySyncData>()
        val appointments = mutableListOf<AppointmentSyncData>()
        
        pendingItems.forEach { item ->
            when (item.type) {
                "patient" -> {
                    val patientData = Gson().fromJson(item.data, CreatePatientRequest::class.java)
                    patients.add(PatientSyncData(
                        mobile_id = item.mobileId,
                        firstname = patientData.firstname,
                        lastname = patientData.lastname,
                        tel = patientData.tel,
                        sexe = patientData.sexe,
                        birth_date = patientData.birth_date,
                        address = patientData.address,
                        is_child = patientData.is_child
                    ))
                }
                // Ajouter d'autres types si nécessaire
            }
        }
        
        return SyncDataRequest(
            patients = patients,
            pregnancies = pregnancies,
            appointments = appointments
        )
    }
    
    private suspend fun updateLocalIdsWithServerIds(processed: List<ProcessedItem>) {
        processed.forEach { item ->
            when {
                item.mobile_id.startsWith("patient_") -> {
                    localDatabase.patientDao().updateServerId(item.mobile_id, item.server_id)
                }
                // Ajouter d'autres types
            }
        }
    }
    
    private suspend fun markItemsAsSynced(items: List<SyncQueueItem>) {
        items.forEach { item ->
            localDatabase.syncQueueDao().markAsSynced(item.id)
        }
    }
}

data class SyncResult(
    val totalItems: Int,
    val syncedItems: Int,
    val processed: List<ProcessedItem>
)
```

## ⚙️ Configuration et Utilisation

### 1. Configuration Initiale

```kotlin
// Dans votre Application class
class PTCCareApp : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // Configuration Retrofit
        val retrofit = Retrofit.Builder()
            .baseUrl(ApiConfig.BASE_URL)
            .addConverterFactory(GsonConverterFactory.create())
            .client(createOkHttpClient())
            .build()
        
        val apiService = retrofit.create(PTCCareApiService::class.java)
        
        // Initialisation des managers
        val authManager = AuthManager(apiService, SecureStorage(this))
        val syncManager = SyncManager(apiService, authManager, localDatabase)
        
        // Planification de la synchronisation automatique
        scheduleSyncWork()
    }
    
    private fun createOkHttpClient(): OkHttpClient {
        return OkHttpClient.Builder()
            .connectTimeout(ApiConfig.TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .readTimeout(ApiConfig.TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .addInterceptor(PasswordChangeInterceptor(authManager))
            .build()
    }
}
```

### 2. Navigation Conditionnelle

```kotlin
// Dans votre MainActivity
class MainActivity : AppCompatActivity() {
    
    private lateinit var authManager: AuthManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        authManager = AuthManager(/* dependencies */)
        
        if (authManager.isLoggedIn()) {
            val user = authManager.getCurrentUser()!!
            when (user.role) {
                UserRole.ADMINISTRATOR -> {
                    // Naviguer vers le dashboard Admin
                    startActivity(Intent(this, AdminDashboardActivity::class.java))
                }
                UserRole.AGENT -> {
                    // Naviguer vers le dashboard Agent
                    startActivity(Intent(this, AgentDashboardActivity::class.java))
                }
                else -> {
                    // Déconnexion des patients
                    authManager.logout()
                    startActivity(Intent(this, LoginActivity::class.java))
                }
            }
        } else {
            // Naviguer vers l'écran de connexion
            startActivity(Intent(this, LoginActivity::class.java))
        }
        
        finish()
    }
}
```

## 🔒 Sécurité et Stockage

### Stockage Sécurisé

```kotlin
class SecureStorage(private val context: Context) {
    
    private val encryptedPrefs = EncryptedSharedPreferences.create(
        "secure_prefs",
        masterKey,
        context,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )
    
    private val masterKey = MasterKey.Builder(context)
        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
        .build()
    
    fun saveUserSession(session: UserSession) {
        encryptedPrefs.edit()
            .putString("user_session", Gson().toJson(session))
            .apply()
    }
    
    fun getUserSession(): UserSession? {
        val json = encryptedPrefs.getString("user_session", null)
        return json?.let { Gson().fromJson(it, UserSession::class.java) }
    }
    
    fun clearUserSession() {
        encryptedPrefs.edit().remove("user_session").apply()
    }
}
```

Ce guide vous donne tous les éléments nécessaires pour intégrer l'API PTC Care dans votre application mobile avec la gestion des rôles spécifiques et la synchronisation hors ligne.
