{% extends 'direction/layout.html' %} 
{% load static %} 
{% load layout %} 
{% block up-style %}

<link href="{% static 'libs/select2/css/select2.min.css' %}" rel="stylesheet" type="text/css" />
<style>
    #imgProfil{
        min-width: 300px !important;
        min-height: 300px !important;
        max-width: 300px !important;
        max-height: 300px !important;
    }

    label.required::after{
        content: "*";
        color: red;
        padding-left: 3px
    }

    .swal2-content{
        color:white !important;
    }
</style>
{% endblock up-style %} 
{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white text-left">
                <span class="h2 text-capitalize">Mon profil</span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8 col-sm-12">
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <h4>Nom & Prénom</h4>
                                <p>{{profile.lastname}} {{profile.firstname}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Adresse</h4>
                                <p>{{profile.address}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Email</h4>
                                <p>{{profile.user.email}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Téléphone</h4>
                                <p>{{profile.tel}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Fonction</h4>
                                <p class="text-capitalize">
                                    {{ profile.user|user_group }}
                                </p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Hôpital</h4>
                                <p>{{profile.hospital.name}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Service</h4>
                                <p>{{profile.service.name}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Spécialité</h4>
                                <p>{{profile.speciality.name}}</p>
                            </div>
                            
                            {% if 'docteur' == profile.user|user_group %}
                                <div class="col-md-6 col-sm-12">
                                    <h2>Attribuer un assistant</h2>
                                    <form action="#" method="post">
                                        <div class="d-flex justify-space-between">
                                            {% csrf_token %}
                                            <input type="hidden" name="_method" id="method" value="post">
                                            <input type="hidden" name="profile_id" id="profile_id" value="">
                                            <select class="form-control select2" name="assistant" id="assistant">
                                                <option value="0" class="none">Pas d'assitant</option>
                                                {% for assistant in assistants %}
                                                    {% if 'assistant' == assistant.user|user_group %}                                                  
                                                    <option value="{{assistant.pk}}" {% if assistant.pk == profile.assistant_id %} selected {% endif %}>{{assistant.user|auth_fullname}}</option>                                                  
                                                    {% endif %}
                                                {% endfor %}
                                            </select>
                                            <button id="form-submit" class="btn btn-primary mx-3">Confirmer</button>
                                        </div>
                                    </form>
                                </div>                           
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-12">
                        <div class="d-flex align-items-center justify-content-center">
                            <form name="profile_pic" action="#" method="post" enctype="multipart/form-data">
                                
                                <img {% if profile.picture == NULL %} src="{% static 'images/default-profile.png' %}" {% else %} src="{% static profile.picture %}" {% endif %} class="rounded-circle w-100 h-100" alt="img-7" id="imgProfil">
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %} 
{% block down-script %}

<script src="{% static 'libs/select2/js/select2.min.js'%}"></script>
<script>
    $('.select2').select2()

    function openImage() {
        document.getElementById("imgInp").click();
    }

    // function upload(response){
    //     $('#form-submit2').submit();
    //     if(response.success){
    //         Swal.fire("Mis à jour!", "", "success");
    //         window.location.reload()
    //     }
    // }
    
    const sendBlob = async function(blobURL, url) {
        
        let blob = await fetch(blobURL).then(r => r.blob());
        
        var form = new FormData();
        form.append('csrfmiddlewaretoken', $('input[name="csrfmiddlewaretoken"]').val());
        form.append('imgInp', blob);
        console.log(blob);
        $.ajax({
            url: url,
            type: 'POST',
            data: form,
            processData: false,
            contentType: false,
            success: function (response) {
                if(response.success){
                    $('#method').val('put')
                    if($('#method').val() == 'put'){
                        Swal.fire("Mis à jour!", "", "success");
                    }else if($('#method').val() == 'post'){
                        Swal.fire("Création!", "", "success");
                    }
                    window.location.reload();
                }
            },
            error: function () {
            }
        });
    }


    $('input[name=profil]').change( function () {
        if($('#method').val() == 'put'){
            var url = "/profile/picture/"+"{{profile.pk}}"
        }else if($('#method').val() == 'post'){
            var url = "/profile/picture/"+"{{profile.pk}}"
        }
        const input = document.querySelector('#imgInp');
        const file = input.files[0];
        const urls = URL.createObjectURL(file);

        sendBlob(urls, url);
    });

    $('#form-submit').click(function(e){
        $('#method').val('put')
            e.preventDefault()
            if($('#method').val() == 'put'){
                var url = "/profile/assistant/"+"{{profile.id}}"
            }
            $.post(
                url,
                {
                    csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val(),
                    assistant :  $('#assistant').val(),
                },
                function(response){
                    if(response.success){
                        if($('#method').val() == 'put'){
                            Swal.fire("Mis à jour!", "", "success");
                        }else if($('#method').val() == 'post'){
                            Swal.fire("Création!", "", "success");
                        }
                        window.location.reload()
                    }
                }
            );
    })
    
</script>
{% endblock down-script %}