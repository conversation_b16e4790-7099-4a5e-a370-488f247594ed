from django.db import models
from .profile import Profile


class Pregnancy(models.Model):
    state = models.Char<PERSON><PERSON>(null = True, blank = True, max_length=15)
    situation = models.CharField(max_length=50)
    description = models.TextField(max_length=255)
    term = models.TextField(max_length= 50, null = True, blank=True)
    start_date = models.DateField(null = True, blank = True)
    end_date = models.DateTimeField(null = True, blank = True)
    mother_state = models.CharField(null = True, blank = True, max_length= 100)
    children_state = models.CharField(null = True, blank = True, max_length= 100)
    child_number = models.CharField(null = True, blank = True, max_length= 100)
    interruption_cause = models.CharField(null = True, blank = True, max_length= 100)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    

    
    
