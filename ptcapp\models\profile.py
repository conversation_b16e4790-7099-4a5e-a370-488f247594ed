from django.contrib.auth.models import User
from django.db import models



from .hospital import Hospital
from .service import Service
from .speciality import Speciality
from .language import Language
from django.contrib.auth.models import User


class Profile(models.Model):
    MAN = "M"
    WOMAN = "F"

    CHOICES = (
        (MAN, MAN),
        (WOMAN, WOMAN)
    )

    user = models.OneToOneField(User, null= True, related_name='user',on_delete=models.SET_NULL)
    firstname = models.Char<PERSON>ield(max_length= 50)
    lastname = models.CharField(max_length= 250)
    tel = models.CharField(max_length= 250)
    sexe = models.Char<PERSON>ield(max_length=100, choices=CHOICES, default=WOMAN)
    birth_date = models.DateField(null= True)
    address = models.CharField(max_length= 250)
    language = models.ForeignKey(Language,null=True, on_delete=models.SET_NULL)
    speciality = models.ForeignKey(Speciality,null=True, on_delete=models.SET_NULL)
    service = models.Foreign<PERSON>ey(Service, null=True, on_delete=models.SET_NULL)
    hospital = models.ForeignKey(Hospital, null= True, on_delete=models.SET_NULL)
    occupation = models.CharField(max_length= 50, null= True, blank=True)
    assurance = models.CharField(max_length= 50, null= True, blank=True)
    special_marks = models.TextField(max_length= 250, null= True, blank=True)
    personal_history = models.TextField(max_length= 250, null= True, blank=True)
    family_history = models.TextField(max_length= 250, null= True, blank=True)
    allergies = models.TextField(max_length= 200, null= True, blank=True)
    vaccinations = models.TextField(max_length= 200, null= True, blank=True)
    picture = models.CharField(max_length= 150, null= True, blank=True)
    child = models.BooleanField( null= True, blank= True)
    archived = models.BooleanField(default=False)
    alive = models.BooleanField(default=True)
    dead_at = models.DateField(null= True, blank= True)
    #pregnancy = models.ForeignKey(Pregnancy, null= True, on_delete=models.SET_NULL)
    assistant = models.ForeignKey(User, null=True, related_name='assistant', on_delete=models.SET_NULL, blank=True)
    created_by = models.ForeignKey(User, null=True, related_name='created_by', on_delete=models.SET_NULL, blank=True)
    archived_by = models.ForeignKey(User, null=True, related_name='archived_by', on_delete=models.SET_NULL, blank=True)

    #nouveaux attributs retenus à partir du fichier excel

    study_level = models.CharField(max_length= 250, null= True, blank=True)
    husband_name = models.CharField(max_length= 250, null= True, blank=True)
    husband_tel = models.CharField(max_length= 250, null= True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)