{% extends 'direction/layout.html' %} 
{% load static %}
{% load layout %}

{% block up-style %}
    <!-- DataTables -->
    <link href="{% static 'libs/select2/css/select2.min.css' %}" rel="stylesheet" type="text/css" />
    <link href="{% static 'libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css' %}" rel="stylesheet" type="text/css" />
    <link href="{% static 'libs/bootstrap-datepicker/css/bootstrap-datepicker.min.css' %}" rel="stylesheet" type="text/css" />
    <style>
        .swal2-content{
            color:white !important;
        }
        .action a{
            margin: 0 10px;
        }
    </style>
{% endblock up-style %}
{% block action_button %}
<a href="{% url 'admin.personnel.create' %}" id="addPersonnel" class="btn btn-primary mx-3">Ajouter Agent</a>
{% endblock action_button %}
{% block content %}
    {% csrf_token %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white text-left">
                    <span class="h2 text-capitalize">Filtre sur les membres du personnel</span>
                </div>
                <div class="card-body">
                    <form action="{% url 'admin.indexFilters' %}" method="get">
                        <div class="row">
                            <div class="col-md-3 col-sm-12">
                                <label for="created_by" class="form-label">Créé par</label>
                                <select class="select2 form-control" name="created_by" id="created_by">
                                    <option value="all" selected>Tout</option>
                                    {% for personnel in initpersonnels %}
                                    <option value="{{personnel.user.id}}" {% if request.GET.created_by != "all" and request.GET.created_by|toInt == personnel.user.id %}selected{% endif %}>{{personnel.user|auth_fullname}} </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="fullname" class="form-label">Nom & Prénoms</label>
                                <input class="form-control" type="text" name="fullname" placeholder="Nom & Prénoms" id="fullname" value="{% if request.GET.fullname %}{{request.GET.fullname}}{% endif %}">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="function" class="form-label">Fonction</label>
                                {% comment %} <input class="form-control" type="text" name="function" placeholder="Fonction" id="function"> {% endcomment %}
                                <select class="form-control select2" name="function" id="function">
                                    <option value="all">Tout</option>
                                    {% for group in groups %}
                                    {% if group.name != "patient" %}
                                    <option value="{{group.name}}" {% if request.GET.function != "all" and request.GET.function == group.name %}selected{% endif %}> {{group.name}} </option>
                                    {% endif %}
                                    {% endfor %}
                                </select>
                            </div>
                            {% comment %} <div class="col-md-3 col-sm-12">
                                <label for="created_at" class="form-label">Date d'inscriptation</label>
                                <input class="form-control" type="date" name="created_at" placeholder="Date d'inscriptation" id="created_at">
                            </div> {% endcomment %}
                            <div class="col-md-3 col-sm-12">
                                <label for="function" class="form-label">Date de création</label>
                                <div class="input-daterange input-group" id="datepicker6" data-date-format="yyyy-mm-dd" data-date-autoclose="true" data-provide="datepicker" data-date-container='#datepicker6'>
                                    <input type="text" class="form-control" name="start" placeholder="Du" value="{% if request.GET.start %}{{request.GET.start}}{% endif %}" />
                                    <input type="text" class="form-control" name="end" placeholder="Au" value="{% if request.GET.end %}{{request.GET.end}}{% endif %}" />
                                </div>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col">
                                <input class="btn btn-success w-auto" type="submit" value="Appliquer">
                                <a href="{% url 'admin.index' %}" class="btn btn-primary w-auto">Réinitialiser</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-center">
                    <span class="h2 text-capitalize text-white"> liste des membres du personnel</span>
                </div>
                <div class="card-body">
                    <table
                        id="datatable"
                        class="table table-striped dt-responsive nowrap"
                        style="border-collapse: collapse; border-spacing: 0; width: 100%"
                        >
                        <thead>
                            <tr>
                                <th>Créé par</th>
                                <th>Nom & Prénoms</th>
                                <th>Fonction</th>
                                <th>Hôpital</th>
                                <th>Date d'inscription</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        <tbody>
                            {% for personnel in personnels %}
                                {% if request.GET.function and request.GET.function != "all" %}
                                    {% if personnel.user.groups.first.name == request.GET.function %}
                                        <tr>
                                            <td> {{personnel.created_by|auth_fullname}} </td>
                                            <td>{{personnel.lastname}} {{personnel.firstname}}</td>
                                            <td class="text-capitalize">
                                                {% for group in personnel.user.groups.all %}
                                                    {{group.name}}
                                                {% endfor %}
                                            </td>
                                            <td>{{personnel.hospital.name}}</td>
                                            <td>{{personnel.created_at|date:"d-m-Y"}}</td>
                                            <td class="action">
                                                <a href="{% url 'admin.personnel.show' id=personnel.id %}" data-toggle="tooltip" data-placement="right" title="Détails"><i class=" ri-eye-fill" ></i></a>
                                                <a href="{% url 'admin.personnel.edit' id=personnel.id %}" data-toggle="tooltip" data-placement="right" title="Modifier"><i class=" ri-pencil-fill" ></i></a>
                                                <a href="#" class="delete-user" data-user-id="{{personnel.id}}" data-toggle="tooltip" data-placement="right" title="Supprimer"><i class=" ri-delete-bin-fill" ></i></a>
                                            </td>
                                        </tr>
                                    {% endif %}
                                {% else %}
                                    <tr>
                                        <td> {{personnel.created_by|auth_fullname}} </td>
                                        <td>{{personnel.lastname}} {{personnel.firstname}}</td>
                                        <td class="text-capitalize">
                                            {% for group in personnel.user.groups.all %}
                                                {{group.name}}
                                            {% endfor %}
                                        </td>
                                        <td>{{personnel.hospital.name}}</td>
                                        <td>{{personnel.created_at|date:"d-m-Y"}}</td>
                                        <td>
                                            <a href="{% url 'admin.personnel.show' id=personnel.id %}" data-toggle="tooltip" data-placement="right" title="Détails"><i class=" ri-eye-fill" ></i></a>
                                            <a href="{% url 'admin.personnel.edit' id=personnel.id %}" data-toggle="tooltip" data-placement="right" title="Modifier"><i class=" ri-pencil-fill" ></i></a>
                                            <a href="#" class="delete-user" data-user-id="{{personnel.id}}" data-toggle="tooltip" data-placement="right" title="Supprimer"><i class=" ri-delete-bin-fill" ></i></a>
                                        </td>
                                    </tr>
                                {% endif %}
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}

{% block down-script %}
    <script src="//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json"></script>
    <script src="{% static 'libs/select2/js/select2.min.js'%}"></script>
    <!-- Required datatable js -->
    <script src="{% static 'libs/datatables.net/js/jquery.dataTables.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js'%}"></script>
    <!-- Responsive examples -->
    <script src="{% static 'libs/datatables.net-responsive/js/dataTables.responsive.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js'%}"></script>
    <script src="{% static 'libs/bootstrap-datepicker/js/bootstrap-datepicker.min.js'%}"></script>
    <!-- Datatable init js -->
    {% comment %} <script src="{% static 'js/pages/datatables.init.js' %}"></script> {% endcomment %}
    <script>
        {% if new_credential != None %}
            Swal.fire({
                title: "Attention, Identifiant à conserver !!!",
                html: "<p style=\"color:black;\"><b>Nom d'utilisateur:</b> {{new_credential.username}} <br> <b>Mot de passe:</b> {{new_credential.password}}</p>",
                icon: "info",
                showCancelButton: !1
            }).then(function(t){
                if(t.value){
                    {% for msg in messages %}
                        Swal.fire({
                            position: "top-end",
                            text: "{{msg}}",
                            showConfirmButton: !1,
                            timer: 1000,
                            background:"rgba(63,255,106,0.69)"
                        });
                    {% endfor %}    
                }
            })
        {% else %}
            {% for msg in messages %}
                Swal.fire({
                    position: "top-end",
                    text: "{{msg}}",
                    showConfirmButton: !1,
                    timer: 1000,
                    background:"rgba(63,255,106,0.69)"
                });
            {% endfor %} 
        {% endif %}
        var dt = $("#datatable").DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
            },
            "columnDefs": [ {
                "targets": [5],
                "orderable": false
            }],
            order : [[1, 'asc']],
            drawCallback: function() {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        $('.select2').select2()
        $(".delete-user").click(function(e) {
            var id = $(this).data('userId')
            Swal.fire({
                title: "Voulez-vous vraiment supprimer cet utilisateur ?",
                text: "Cet utilisateur sera définitivement supprimer !",
                icon: "warning",
                showCancelButton: !0,
                confirmButtonColor: "#1cbb8c",
                cancelButtonColor: "#ff3d60",
                confirmButtonText: "Oui, supprimer!",
                cancelButtonText: "Annuler",
            }).then(function(t) {
                if(t.value){
                    $.post(
                        "/profile/delete/"+id,
                        {
                            csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val(),
                        },
                        function(response){
                            if(response.success){
                                Swal.fire({
                                    position: "top-end",
                                    text: "Agent supprimé avec succès",
                                    showConfirmButton: !1,
                                    timer: 1000,
                                    background: "#f27474"
                                });
                                setTimeout( function(){
                                    window.location.reload()
                                },1100)
                            }
                        }
                    );
                }
            });
        })
    </script>
{% endblock down-script %}