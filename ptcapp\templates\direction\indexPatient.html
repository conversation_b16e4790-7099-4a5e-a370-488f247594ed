{% extends 'direction/layout.html' %} 
{% load static %}
{% load layout %}

{% block up-style %}
    <!-- DataTables -->
    <link href="{% static 'libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css' %}" rel="stylesheet" type="text/css" />
    <style>
        .swal2-content{
            color:white !important;
        }
        .action a{
            margin: 0 10px;
        }
    </style>
{% endblock up-style %}

{% block action_button %}
<a href="{% url 'admin.patient.create' %}" id="addPersonnel" class="btn btn-primary mx-3">Ajouter un patient</a>
{% endblock action_button %}
{% block content %}
    {% csrf_token %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white text-left">
                    <span class="h2 text-capitalize">Filtre sur patients</span>
                </div>
                <div class="card-body">
                    <form action="{% url 'admin.filters' %}" method="get">
                        <div class="row">
                            <div class="col-md-3 col-sm-12">
                                <label for="fullname" class="form-label">Nom & Prénoms</label>
                                <input class="form-control" type="text" name="fullname" placeholder="Nom & Prénoms" id="fullname">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="last_appointment" class="form-label">Dernière Consultation</label>
                                <input class="form-control" type="date" name="last_appointment" placeholder="Dernière Consultation" id="last_appointment">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="next_appointment" class="form-label">Prochaine Consultation</label>
                                <input class="form-control" type="date" name="next_appointment" placeholder="Prochaine Consultation" id="next_appointment">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="next_appointment" class="form-label text-white">Button d'application</label>
                                <input class=" form-control btn btn-success " type="submit" value="Appliquer">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-center">
                    <span class="h2 text-capitalize text-white"> liste des patients</span>
                </div>
                <div class="card-body">
                    <table
                        id="datatable"
                        class="table table-striped dt-responsive nowrap"
                        style="border-collapse: collapse; border-spacing: 0; width: 100%"
                        >
                        <thead>
                            <tr>
                                <th></th>
                                <th>Nom & Prénoms</th>
                                <th>Date de création</th>
                                <th>Dernière Consultation</th>
                                <th>Prochaine Consultation</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        <tbody>
                            {% for patient in patients %}
                                <tr {% if patient.archived == True %}style="color: #FF0000"{% endif %}> 
                                    <td>{% if patient.child == 1 %} Enfant {% else %} Mère{% endif %}</td>
                                    
                                    <td >{{patient.user|auth_fullname}} </td>
                                    
                                    <td>{{patient.created_at|date:"d-m-Y"}}</td>
                                    <td>{{patient|last_appointment}}</td>
                                    <td>{{patient|next_appointment}}</td>
                                    <td class="action"><a href="{% url 'admin.record.show' id=patient.id %}" data-toggle="tooltip" data-placement="right" title="Détails"><i class="ri-eye-fill" ></i></a>
                                        <a href="{% url 'admin.patient.edit' id=patient.id %}" data-toggle="tooltip" data-placement="right" title="Détails"><i class="ri-pencil-fill" ></i></a>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}

{% block down-script %}
    <script src="//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json"></script>
    <!-- Required datatable js -->
    <script src="{% static 'libs/datatables.net/js/jquery.dataTables.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js'%}"></script>
    <!-- Responsive examples -->
    <script src="{% static 'libs/datatables.net-responsive/js/dataTables.responsive.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js'%}"></script>
    <!-- Datatable init js -->
    {% comment %} <script src="{% static 'js/pages/datatables.init.js' %}"></script> {% endcomment %}
    <script>
        {% if new_credential != None %}
            Swal.fire({
                title: "Attention, Identifiant à conserver !!!",
                html: "<p style=\"color:black;\"><b>Nom d'utilisateur:</b> {{new_credential.username}} <br> <b>Mot de passe:</b> {{new_credential.password}}</p>",
                icon: "info",
                showCancelButton: !1
            }).then(function(t){
                if(t.value){
                    {% for msg in messages %}
                        Swal.fire({
                            position: "top-end",
                            text: "{{msg}}",
                            showConfirmButton: !1,
                            timer: 1000,
                            background:"rgba(63,255,106,0.69)"
                        });
                    {% endfor %}    
                }
            })
        {% else %}
            {% for msg in messages %}
                Swal.fire({
                    position: "top-end",
                    text: "{{msg}}",
                    showConfirmButton: !1,
                    timer: 1000,
                    background:"rgba(63,255,106,0.69)"
                });
            {% endfor %} 
        {% endif %}
        var dt = $("#datatable").DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
            },
            "columnDefs": [ {
                "targets": [5, 0],
                "orderable": false
            }],
            order : [[4, 'desc']],
            drawCallback: function() {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })

        $(".archive").click(function(e) {
            var id = $(this).data('userId')
            Swal.fire({
                title: "Voulez-vous vraiment archiver ce dossier ?",
                text: "Ce dossier pourra être retiré des archives plus tard.",
                icon: "warning",
                showCancelButton: !0,
                confirmButtonColor: "#1cbb8c",
                cancelButtonColor: "#ff3d60",
                confirmButtonText: "Oui, archiver!",
                cancelButtonText: "Annuler",
            }).then(function(t) {
                if(t.value){
                    $.post(
                        "/docteur/dossier/archive/"+id,
                        {
                            csrfmiddlewaretoken: "{{ csrf_token }}",
                        },
                        function(response){
                            if(response.success){
                                Swal.fire({
                                    position: "top-end",
                                    text: "Patient archivé avec succès",
                                    showConfirmButton: !1,
                                    timer: 1000,
                                    background: "#f27474"
                                });
                                setTimeout( function(){
                                    window.location.reload()
                                },1100)
                            }
                        }
                    );
                }
            });
        })
    </script>
{% endblock down-script %}