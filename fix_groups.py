#!/usr/bin/env python
"""
Script pour créer les groupes manquants sur Heroku
"""
import os
import django

# Configuration Django
if 'DYNO' in os.environ:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ptccare.settings_production')
else:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ptccare.settings')

django.setup()

from django.contrib.auth.models import Group
from ptcapp.models import Language, Hospital, Service, Speciality

def main():
    print("🔧 Création des groupes d'utilisateurs...")
    
    # Créer les groupes nécessaires
    groups = ['admin', 'docteur', 'assistant', 'patient']
    for group_name in groups:
        group, created = Group.objects.get_or_create(name=group_name)
        if created:
            print(f'✅ Groupe {group_name} créé')
        else:
            print(f'ℹ️ Groupe {group_name} existe déjà')
    
    print("\n📊 Vérification des données de base...")
    
    print(f"Groupes: {Group.objects.count()}")
    print(f"Langues: {Language.objects.count()}")
    print(f"Hôpitaux: {Hospital.objects.count()}")
    print(f"Services: {Service.objects.count()}")
    print(f"Spécialités: {Speciality.objects.count()}")
    
    print("\n🎉 Correction terminée !")

if __name__ == "__main__":
    main()
