<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if token_change %}Définir votre mot de passe{% else %}Changement de mot de passe obligatoire{% endif %} - PTC Care</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .change-password-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 500px;
            width: 100%;
        }
        .header {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .form-container {
            padding: 40px;
        }
        .form-group {
            margin-bottom: 25px;
        }
        .form-control {
            border: 2px solid #ecf0f1;
            border-radius: 10px;
            padding: 15px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border: none;
            border-radius: 10px;
            padding: 15px 30px;
            font-size: 16px;
            font-weight: 600;
            width: 100%;
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }
        .alert {
            border-radius: 10px;
            border: none;
            margin-bottom: 20px;
        }
        .password-requirements {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 25px;
        }
        .requirement {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
        }
        .requirement i {
            margin-right: 10px;
            width: 16px;
        }
        .requirement.valid {
            color: #27ae60;
        }
        .requirement.invalid {
            color: #e74c3c;
        }
        .input-group {
            position: relative;
        }
        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #7f8c8d;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="change-password-container">
        <div class="header">
            <i class="fas fa-lock fa-3x mb-3"></i>
            <h2>Changement de mot de passe obligatoire</h2>
            <p class="mb-0">Pour votre sécurité, vous devez changer votre mot de passe</p>
        </div>
        
        <div class="form-container">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Attention :</strong> Vous ne pourrez pas accéder aux autres fonctionnalités tant que vous n'aurez pas changé votre mot de passe.
            </div>
            
            <form method="post" id="changePasswordForm">
                {% csrf_token %}
                
                <div class="form-group">
                    <label for="current_password" class="form-label">
                        <i class="fas fa-key me-2"></i>Mot de passe actuel
                    </label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                        <span class="password-toggle" onclick="togglePassword('current_password')">
                            <i class="fas fa-eye"></i>
                        </span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="new_password" class="form-label">
                        <i class="fas fa-lock me-2"></i>Nouveau mot de passe
                    </label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                        <span class="password-toggle" onclick="togglePassword('new_password')">
                            <i class="fas fa-eye"></i>
                        </span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password" class="form-label">
                        <i class="fas fa-check-double me-2"></i>Confirmer le nouveau mot de passe
                    </label>
                    <div class="input-group">
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        <span class="password-toggle" onclick="togglePassword('confirm_password')">
                            <i class="fas fa-eye"></i>
                        </span>
                    </div>
                </div>
                
                <div class="password-requirements">
                    <h6><i class="fas fa-shield-alt me-2"></i>Exigences du mot de passe :</h6>
                    <div class="requirement" id="length-req">
                        <i class="fas fa-times"></i>
                        Au moins 8 caractères
                    </div>
                    <div class="requirement" id="uppercase-req">
                        <i class="fas fa-times"></i>
                        Au moins une lettre majuscule
                    </div>
                    <div class="requirement" id="lowercase-req">
                        <i class="fas fa-times"></i>
                        Au moins une lettre minuscule
                    </div>
                    <div class="requirement" id="number-req">
                        <i class="fas fa-times"></i>
                        Au moins un chiffre
                    </div>
                    <div class="requirement" id="special-req">
                        <i class="fas fa-times"></i>
                        Au moins un caractère spécial (!@#$%^&*)
                    </div>
                    <div class="requirement" id="match-req">
                        <i class="fas fa-times"></i>
                        Les mots de passe correspondent
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                    <i class="fas fa-save me-2"></i>Changer le mot de passe
                </button>
            </form>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = field.nextElementSibling.querySelector('i');
            
            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
        
        function validatePassword() {
            const password = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            // Validation des exigences
            const requirements = {
                'length-req': password.length >= 8,
                'uppercase-req': /[A-Z]/.test(password),
                'lowercase-req': /[a-z]/.test(password),
                'number-req': /\d/.test(password),
                'special-req': /[!@#$%^&*(),.?":{}|<>]/.test(password),
                'match-req': password === confirmPassword && password.length > 0
            };
            
            let allValid = true;
            
            for (const [reqId, isValid] of Object.entries(requirements)) {
                const element = document.getElementById(reqId);
                const icon = element.querySelector('i');
                
                if (isValid) {
                    element.classList.add('valid');
                    element.classList.remove('invalid');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-check');
                } else {
                    element.classList.add('invalid');
                    element.classList.remove('valid');
                    icon.classList.remove('fa-check');
                    icon.classList.add('fa-times');
                    allValid = false;
                }
            }
            
            // Activer/désactiver le bouton
            document.getElementById('submitBtn').disabled = !allValid;
        }
        
        // Écouter les changements dans les champs de mot de passe
        document.getElementById('new_password').addEventListener('input', validatePassword);
        document.getElementById('confirm_password').addEventListener('input', validatePassword);
    </script>
</body>
</html>
