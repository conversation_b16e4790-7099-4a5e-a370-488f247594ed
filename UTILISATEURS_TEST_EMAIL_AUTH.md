# Utilisateurs de Test - Authentification par Email

## 📋 Résumé

Ce document contient les identifiants des utilisateurs de test créés pour valider le nouveau système d'authentification par email dans l'application PTC Care.

## ✅ Statut de Création

**Date de création :** 2025-06-14  
**Statut :** ✅ Tous les utilisateurs créés avec succès  
**Tests d'authentification :** ✅ 4/4 réussis  
**Tests de redirection :** ✅ Toutes les redirections fonctionnent  

## 👥 Utilisateurs de Test Créés

### 🔹 ADMINISTRATEUR
- **Email :** `<EMAIL>`
- **Mot de passe :** `TestPTC2024!`
- **Username :** `ADM-TEST001`
- **Nom :** Administrateur Test
- **Téléphone :** +22912345001
- **Redirection :** `/admin/`
- **Permissions :** Accès complet à l'administration

### 🔹 DOCTEUR
- **Email :** `<EMAIL>`
- **Mot de passe :** `TestPTC2024!`
- **Username :** `DOC-TEST001`
- **Nom :** Docteur Médecin
- **Téléphone :** +22912345002
- **Hôpital :** Hôpital de Test PTC
- **Service :** Service de Test
- **Spécialité :** Médecine Générale
- **Redirection :** `/docteur/`
- **Permissions :** Gestion des patients, consultations

### 🔹 ASSISTANT MÉDICAL
- **Email :** `<EMAIL>`
- **Mot de passe :** `TestPTC2024!`
- **Username :** `ASS-TEST001`
- **Nom :** Assistant Médical
- **Téléphone :** +22912345003
- **Hôpital :** Hôpital de Test PTC
- **Service :** Service de Test
- **Redirection :** `/assistant/`
- **Permissions :** Assistance médicale, gestion des rendez-vous

### 🔹 PATIENT
- **Email :** `<EMAIL>`
- **Mot de passe :** `TestPTC2024!`
- **Username :** `PAT-TEST001`
- **Nom :** Patient Exemple
- **Téléphone :** +22912345004
- **Date de naissance :** 15/05/1990
- **Profession :** Enseignante
- **Assurance :** CNSS Bénin
- **Redirection :** `/patient/`
- **Permissions :** Consultation de son dossier médical

## 🧪 Tests de Validation Effectués

### ✅ Tests d'Authentification
- **Authentification Django :** 4/4 réussis
- **Connexion web :** 4/4 réussis
- **API Login :** 4/4 réussis
- **Redirections :** 4/4 correctes

### ✅ Tests de Fonctionnalités
- **Permissions par rôle :** Validées
- **Profils complets :** Validés
- **Relations médecin-patient :** Établies
- **Réponses API :** Correctes

### ✅ Tests de Compatibilité
- **Connexion insensible à la casse :** Validée
- **Rétrocompatibilité username :** Validée
- **Support email et username :** Validé

## 🔧 Instructions de Test

### 1. Test Interface Web
```
1. Démarrer le serveur : python manage.py runserver
2. Aller sur : http://localhost:8000/
3. Utiliser un des emails ci-dessus avec le mot de passe : TestPTC2024!
4. Vérifier la redirection vers la page appropriée
```

### 2. Test API
```bash
# Test avec curl
curl -X POST http://localhost:8000/api/login/ \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "TestPTC2024!"}'

# Réponse attendue
{
  "status": "success",
  "user_id": 8,
  "username": "ADM-TEST001",
  "profile_id": 8,
  "role": "admin",
  "name": "Administrateur Test",
  "hospital_id": 1,
  "service_id": 1,
  "speciality_id": null
}
```

### 3. Test Rétrocompatibilité
```
- Essayer de se connecter avec le username au lieu de l'email
- Exemple : ADM-TEST001 avec le mot de passe TestPTC2024!
- Les deux méthodes doivent fonctionner
```

### 4. Test Insensibilité à la Casse
```
- Essayer : <EMAIL>
- Essayer : <EMAIL>
- Essayer : <EMAIL>
- Toutes les variantes doivent fonctionner
```

## 🎯 Scénarios de Test Recommandés

### Scénario 1 : Connexion Administrateur
1. Se connecter avec `<EMAIL>`
2. Vérifier l'accès à `/admin/`
3. Tester la création d'un nouveau personnel
4. Vérifier la liste des patients

### Scénario 2 : Connexion Docteur
1. Se connecter avec `<EMAIL>`
2. Vérifier l'accès à `/docteur/`
3. Consulter la liste des patients
4. Vérifier la relation avec le patient de test

### Scénario 3 : Connexion Assistant
1. Se connecter avec `<EMAIL>`
2. Vérifier l'accès à `/assistant/`
3. Tester la gestion des rendez-vous
4. Consulter les patients

### Scénario 4 : Connexion Patient
1. Se connecter avec `<EMAIL>`
2. Vérifier l'accès à `/patient/`
3. Consulter son dossier médical
4. Vérifier les informations personnelles

## 🔒 Sécurité

### Mot de Passe
- **Format :** `TestPTC2024!`
- **Complexité :** Majuscule, minuscule, chiffre, caractère spécial
- **Longueur :** 12 caractères
- **Usage :** Uniquement pour les tests

### Recommandations
- ⚠️ **Ne pas utiliser en production**
- 🔄 Changer les mots de passe après les tests
- 🗑️ Supprimer les comptes de test après validation
- 🔐 Utiliser des mots de passe forts en production

## 📊 Données de Référence Créées

### Hôpital de Test
- **Nom :** Hôpital de Test PTC
- **Adresse :** 123 Rue de Test, Porto-Novo
- **Téléphone :** 22912345678

### Service de Test
- **Nom :** Service de Test
- **Description :** Service de test pour les utilisateurs de démonstration

### Spécialité de Test
- **Nom :** Médecine Générale
- **Description :** Spécialité de médecine générale pour les tests

### Langue
- **Nom :** Français
- **Code :** fr

## 🧹 Nettoyage

Pour supprimer tous les utilisateurs de test :
```python
# Exécuter dans le shell Django
from django.contrib.auth.models import User

test_emails = [
    '<EMAIL>',
    '<EMAIL>', 
    '<EMAIL>',
    '<EMAIL>'
]

User.objects.filter(email__in=test_emails).delete()
```

## 📞 Support

En cas de problème avec les utilisateurs de test :
1. Vérifier que le serveur Django est démarré
2. Vérifier que la base de données est accessible
3. Re-exécuter le script `create_test_users.py`
4. Consulter les logs d'erreur Django

---

**Créé le :** 2025-06-14  
**Dernière mise à jour :** 2025-06-14  
**Version :** 1.0  
**Statut :** ✅ Validé et fonctionnel
