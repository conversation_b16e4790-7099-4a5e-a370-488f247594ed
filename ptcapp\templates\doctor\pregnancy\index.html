{% extends 'doctor/layout.html' %} 
{% load static %}
{% load layout %}
{% load i18n %}

{% block up-style %}
    <!-- DataTables -->
    <link href="{% static 'libs/select2/css/select2.min.css' %}" rel="stylesheet" type="text/css" />
    <link href="{% static 'libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css' %}" rel="stylesheet" type="text/css" />
    <style>
        .swal2-content{
            color:white !important;
        }
    </style>
{% endblock up-style %}
{% block action_button %}
<a href="javascript:;" id="addPregnancy" 
data-bs-toggle="modal" 
data-bs-target=".bs-example-modal-center"  class="btn btn-primary mx-3">Ajouter une grossesse</a>
{% endblock action_button %}
{% block content %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white text-left">
                    <span class="h2 text-capitalize">Filtre sur les grossesses</span>
                </div>
                <div class="card-body">

                    <form action="{% url 'doctor.pregnancyfilters' %}" method="get">
                        
                        <div class="row">
                            <div class="col-md-3 col-sm-12">
                                <label for="fullname" class="form-label">Patient(e)</label>
                                <input class="form-control" type="text" name="fullname" placeholder="Nom & Prénoms" id="fullname">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="statut" class="form-label">Statut</label>
                                <select class="form-control select2" name="statut" id="statut">
                                    <option value="all">Tout</option>
                                    <option value="ongoing" selected>En cours</option>
                                    <option value="interrupted">Interrompue</option>
                                    <option value="ended">À terme</option>
                                    
                                </select>
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="filter_start_date" class="form-label">Date de début</label>
                                <input class="form-control" type="date" name="start_date" placeholder="Date de début" id="filter_start_date">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="" class="form-label text-white">Appliquer</label>
                                <input class="form-control btn btn-success" type="submit" value="Appliquer">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-center">
                    <span class="h2 text-capitalize text-white"> liste des Grossesses</span>
                </div>
                <div class="card-body">
                    <table
                        id="datatable"
                        class="table table-striped dt-responsive nowrap"
                        style="border-collapse: collapse; border-spacing: 0; width: 100%"
                        >
                        <thead>
                            <tr>
                                <th>Patiente</th>
                                <th>Statut</th>
                                <th>Priorité</th>
                                <th>Début de la grossesse</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        <tbody>
                            {% for pregnancy in moth_pregs %}
                                <tr>
                                    <td>{{pregnancy.mother.user|auth_fullname}}</td>
                                    <td>
                                        {% if pregnancy.pregnancy.state == 'ongoing' %}
                                        <span class="bg-warning rounded-3 p-2 text-white">En cours</span>
                                        {% endif %}
                                        {% if pregnancy.pregnancy.state == 'interrupted' %}
                                        <span class="bg-danger rounded-3 p-2 text-white">Interrompue</span>
                                        {% endif %}
                                        {% if pregnancy.pregnancy.state == 'ended' %}
                                        <span class="bg-success rounded-3 p-2 text-white">À terme</span>
                                        {% endif %}
                                    </td>
                                    {% if pregnancy.pregnancy.situation == 'mid' %}
                                    <td>Moyenne</span>
                                    {% endif %}
                                    {% if pregnancy.pregnancy.situation == 'high' %}
                                    <td>Haute</span>
                                    {% endif %}
                                    {% if pregnancy.pregnancy.situation == 'low' %}
                                    <td>Basse</span>
                                    {% endif %}
                                    <td>{{pregnancy.pregnancy.start_date}}</td>
                                    <td>
                                        <a href="{% url 'doctor.pregnancy.edit' id=pregnancy.pregnancy.id %}" data-toggle="tooltip" data-placement="right" title="Modifier"><i class=" ri-pencil-fill" ></i></a>
                                        {% comment %} <a href="#" class="delete-appointment" data-appointment-id="{{appointment.pk}}" data-toggle="tooltip" data-placement="right" title="Supprimer"><i class=" ri-delete-bin-fill" ></i></a> {% endcomment %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade bs-example-modal-center" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-title">Enregistrer une grossesse</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="#" method="post">
                        {% csrf_token %}
                        {% comment %} <div class="row">
                            <div class="d-flex  justify-content-end">
                                <div class="form-check mx-2">
                                    <label for="current_doc" class="form-check-label required">Dossier actuel</label>
                                    <input class="form-check-input" type="radio" name="group" value="current_doc" id="current_doc" checked>
                                </div>
                                <div class="form-check mx-2">
                                    <label for="new_doc" class="form-check-label required">Nouveau Dossier</label>
                                    <input class="form-check-input" type="radio" name="group" value="new_doc" id="new_doc">
                                </div>
                            </div>
                        </div> {% endcomment %}
                        <div class="row">
                            <div class="col-md-12 col-sm-12 mt-4">
                                {% comment %} <fieldset class="h-100"> {% endcomment %}
                                    {% comment %} <legend>Informations Générales</legend> {% endcomment %}
                                    <input type="hidden" name="_method" id="method" value="post">
                                    {% comment %} <div class="my-3">
                                        <label for="num_record" class="form-label required">Numéro du dossier</label>
                                        <input class="form-control" type="text" name="num_record" value="DOS-47852" id="num_record" required="required" disabled>
                                        <input class="form-control" type="hidden" name="current_num_record" value="DOS-47852" id="current_num_record" required="required">
                                    </div> {% endcomment %}
                                    <div class="my-3">
                                        <label for="pregnancy_patient" class="form-label required">Sélectionner patiente</label>
                                        <select class="select2 form-control" name="pregnancy_patient" id="pregnancy_patient" required="required">*
                                            {% for patient in patients %}
                                            {% if patient %}
                                            <option value="{{patient.id}}">{{patient.user|auth_fullname}}</option>
                                            {% endif %}
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="my-3">
                                        <label for="priority" class="form-label required">Priorité de la grossesse</label>
                                        <select class="select2 form-control" name="priority" id="priority" required="required">*           
                                            <option value="low">Basse</option>
                                            <option value="mid">Moyenne</option>
                                            <option value="high">Haute</option>
                                        </select>
                                    </div>
                                    
                                    {% comment %} <div class="my-3">
                                        <label for="illness" class="form-label required">Maux ressentis</label>
                                        <input class="form-control" type="text" name="illness" placeholder="Maux ressentis" id="illness" required="required">
                                    </div> {% endcomment %}
                                {% comment %} </fieldset> {% endcomment %}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 col-sm-12">
                                <label for="start_date" class="form-label required">Date de début</label>
                                <input class="form-control" type="date" name="start_date" min="{{date|date:"Y-m-d"}}" placeholder="Date de début" id="start_date" required>
                            </div>
                            
                        </div>
                        <div class="my-3">
                            <label for="other_informations" class="form-label required">Autres informations</label>
                            {% comment %} <a href="#" style="font-size:20px"><i class="ri-add-box-fill"></i></a> {% endcomment %}
                            <input class="form-control" type="text" name="other_informations" placeholder="Autres informations" id="other_informations" required="required">
                        </div>
                        <div class="row mt-2">
                            <div class="col">
                                <input class="btn btn-success w-auto" id="form-submit" type="submit" value="Sauvegarder">
                            </div>
                        </div>
                    </form>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

{% endblock content %}

{% block down-script %}
    <script src="//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json"></script>
    <script src="{% static 'libs/select2/js/select2.min.js'%}"></script>
    <!-- Required datatable js -->
    <script src="{% static 'libs/datatables.net/js/jquery.dataTables.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js'%}"></script>
    <!-- Responsive examples -->
    <script src="{% static 'libs/datatables.net-responsive/js/dataTables.responsive.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js'%}"></script>
    <!-- Datatable init js -->
    {% comment %} <script src="{% static 'js/pages/datatables.init.js' %}"></script> {% endcomment %}
    <script>

        {% for msg in messages %}
                Swal.fire({
                    position: "top-end",
                    text: "{{msg}}",
                    showConfirmButton: !1,
                    timer: 1000,
                    background:"rgba(63,255,106,0.69)"
                });
            {% endfor %} 
        var dt = $("#datatable").DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
            },
            "columnDefs": [ {
                "targets": [4],
                "orderable": false
            }],
            order : [[2, 'asc'], [3, 'asc']],
            drawCallback: function() {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        $('.select2').select2()
        $(".delete-appointment").click(function() {
            Swal.fire({
                title: "Voulez-vous vraiment supprimer cet utilisateur ?",
                text: "Cet utilisateur sera définitivement supprimer !",
                icon: "warning",
                showCancelButton: !0,
                confirmButtonColor: "#1cbb8c",
                cancelButtonColor: "#ff3d60",
                confirmButtonText: "Oui, supprimer!",
                cancelButtonText: "Annuler",
            }).then(function(t) {
                t.value &&
                    Swal.fire("Supprimer!", "", "success");
            });
        })

        $('#new_doc').click(function(){
            $('#num_record').val('DOS-')
            $('#num_record').attr('disabled', false)
        })

        $('#current_doc').click(function(){
            $('#num_record').val($('#current_num_record').val())
            $('#num_record').attr('disabled', true)
        })


        $('#form-submit').click(function(e){
            e.preventDefault()
            $.post(
                "{% url 'pregnancy.create' %}",
                {
                    csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val(),
                    pregnancy_patient : $('#pregnancy_patient').val(),
                    priority : $('#priority').val(),
                    start_date : $('#start_date').val(),
                    other_informations : $('#other_informations').val()
                },
                function(response){
                    if(response.success){
                        $('.bs-example-modal-center').modal('toggle');
                        if($('#method').val() == 'put'){
                            Swal.fire("Mis à jour!", "", "success");
                        }else if($('#method').val() == 'post'){
                            Swal.fire("Création!", "", "success");
                        }
                        window.location.reload()
                    }
                }
            );
        })
    </script>
{% endblock down-script %}