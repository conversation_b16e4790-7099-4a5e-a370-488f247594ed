# Résumé - Configuration de Test API PTC Care

## 📁 Fichiers de Test Créés

### 🔧 Configuration Postman
1. **`PTC_Care_API_Collection.postman_collection.json`** - Collection complète avec tous les endpoints
2. **`PTC_Care_Environment.postman_environment.json`** - Environnement de développement configuré

### 📖 Documentation
3. **`API_TESTING_GUIDE.md`** - Guide complet de test étape par étape
4. **`API_TESTING_SUMMARY.md`** - Ce résumé

### 🧪 Scripts de Test
5. **`test_all_api_endpoints.py`** - Test automatisé complet (nécessite requests)
6. **`simple_api_test.py`** - Test simple avec requests
7. **`test_api_with_curl.py`** - Test avec urllib (sans dépendances)

## 🚀 Configuration Rapide dans Postman

### Étape 1: Import de la Collection
1. Ouvrir **Postman**
2. Cliquer sur **Import**
3. Sélectionner `PTC_Care_API_Collection.postman_collection.json`
4. Confirmer l'import

### Étape 2: Import de l'Environnement
1. Cliquer sur **Import**
2. Sélectionner `PTC_Care_Environment.postman_environment.json`
3. Sélectionner l'environnement **"PTC Care - Development"** dans le menu déroulant

### Étape 3: Démarrage du Serveur
```bash
# Dans le terminal avec environnement virtuel activé
.\env\Scripts\activate
python manage.py runserver
```

## 🧪 Plan de Test Recommandé

### Phase 1: Tests de Base (5 min)
1. **GET /api/initial-data/** - Données de référence
2. **GET /api/health-centers/** - Centres de santé
3. **POST /api/login/** - Authentification

### Phase 2: Tests de Création (10 min)
4. **POST /api/create-health-agent/** - Création médecin
5. **POST /api/create-patient/** - Création patient
6. **Vérification emails** dans `<EMAIL>`

### Phase 3: Tests de Données (5 min)
7. **GET /api/agents/** - Liste agents
8. **GET /api/patients/** - Liste patients

### Phase 4: Tests de Sécurité (5 min)
9. **Test sans authentification** - Erreur 401
10. **POST /api/change-password/** - Changement mot de passe

## 📊 Endpoints API Disponibles

| Endpoint | Méthode | Auth | Description |
|----------|---------|------|-------------|
| `/api/initial-data/` | GET | Non | Données de référence |
| `/api/health-centers/` | GET | Non | Centres de santé |
| `/api/login/` | POST | Non | Connexion |
| `/api/change-password/` | POST | Non | Changement mot de passe |
| `/api/create-health-agent/` | POST | Admin | Création agent |
| `/api/create-patient/` | POST | Agent+ | Création patient |
| `/api/agents/` | GET | Requis | Liste agents |
| `/api/patients/` | GET | Requis | Liste patients |
| `/api/sync-mobile-data/` | POST | Agent+ | Sync mobile |

## 🔐 Configuration d'Authentification

### Variables d'Environnement Postman
- **`base_url`** : `http://localhost:8000`
- **`user_id`** : Automatiquement défini après login
- **`auth_token`** : `Bearer {{user_id}}`

### Headers Requis
```
Content-Type: application/json
Authorization: Bearer {{user_id}}
Accept: application/json
```

## 📧 Validation des Emails

### Configuration Actuelle
- **Email SMTP** : `<EMAIL>`
- **Serveur** : Gmail SMTP (smtp.gmail.com:587)
- **Sécurité** : TLS activé

### Tests de Notification
1. **Créer un utilisateur** via API
2. **Vérifier email reçu** dans la boîte configurée
3. **Tester le lien** de changement de mot de passe
4. **Confirmer email** de confirmation après changement

## 🔧 Dépannage

### Problèmes Courants

#### Serveur non accessible
```bash
# Vérifier que le serveur tourne
python manage.py runserver
# Tester dans le navigateur
http://localhost:8000/api/initial-data/
```

#### Erreur d'authentification
- Exécuter d'abord **POST /api/login/**
- Vérifier que `user_id` est défini dans les variables
- Format token : `Bearer {user_id}`

#### Emails non reçus
- Vérifier configuration SMTP dans settings.py
- Contrôler les logs Django pour erreurs
- Tester avec script simple d'envoi

### Logs Utiles
```bash
# Voir les logs du serveur Django
# Les erreurs apparaissent dans le terminal du serveur
```

## 📱 Tests pour Mobile

### Headers Spécifiques Mobile
```
Content-Type: application/json
Authorization: Bearer {user_id}
User-Agent: PTC-Care-Mobile/1.0
Accept: application/json
```

### Scénarios de Test Mobile
1. **Connexion** → Sauvegarder token
2. **Création patient** → Vérifier email envoyé
3. **Changement mot de passe forcé** → Gérer erreur 403
4. **Synchronisation** → Tester mapping IDs

### Gestion des Erreurs
- **401** : Token invalide → Rediriger vers login
- **403** : Changement mot de passe requis → Rediriger vers changement
- **400** : Données invalides → Afficher erreur

## ✅ Checklist de Validation

### Tests de Base
- [ ] API répond sur localhost:8000
- [ ] Données initiales récupérées
- [ ] Centres de santé listés

### Tests d'Authentification
- [ ] Login par email fonctionne
- [ ] Login par username fonctionne
- [ ] Token sauvegardé automatiquement

### Tests de Création avec Email
- [ ] Médecin créé avec email envoyé
- [ ] Patient créé avec email envoyé
- [ ] Emails reçus dans boîte configurée
- [ ] Liens de changement fonctionnels

### Tests de Sécurité
- [ ] Accès non autorisé bloqué (401)
- [ ] Changement mot de passe forcé (403)
- [ ] Validation des rôles

### Tests Mobile
- [ ] Headers mobile acceptés
- [ ] Gestion erreurs appropriée
- [ ] Synchronisation fonctionnelle

## 🎯 Résultats Attendus

### Succès Complet
- **10/10 endpoints** fonctionnels
- **Emails automatiques** envoyés
- **Sécurité** opérationnelle
- **Mobile** compatible

### Prêt pour Intégration
- ✅ **APIs stables** et documentées
- ✅ **Authentification** robuste
- ✅ **Notifications** automatiques
- ✅ **Gestion erreurs** appropriée

---

**Durée totale des tests** : 25-30 minutes  
**Prérequis** : Serveur Django + Configuration email  
**Outils recommandés** : Postman, Insomnia, ou Swagger UI  
**Statut** : ✅ Prêt pour tests et intégration mobile
