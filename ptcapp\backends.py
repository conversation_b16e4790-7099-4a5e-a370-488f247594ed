from django.contrib.auth.backends import BaseBackend
from django.contrib.auth.models import User
from django.db.models import Q


class EmailBackend(BaseBackend):
    """
    Backend d'authentification personnalisé pour permettre la connexion avec l'adresse email
    au lieu du nom d'utilisateur.
    """
    
    def authenticate(self, request, username=None, password=None, email=None, **kwargs):
        """
        Authentifie un utilisateur avec son adresse email et son mot de passe.
        
        Args:
            request: La requête HTTP
            username: Le nom d'utilisateur (ignoré dans ce backend)
            password: Le mot de passe
            email: L'adresse email (utilisée comme identifiant principal)
            **kwargs: Arguments supplémentaires
            
        Returns:
            User: L'utilisateur authentifié ou None si l'authentification échoue
        """
        # Si email n'est pas fourni, on utilise username comme email
        # (pour maintenir la compatibilité avec les appels existants)
        if email is None:
            email = username
            
        if email is None or password is None:
            return None
            
        try:
            # Rechercher l'utilisateur par email
            user = User.objects.get(
                Q(email__iexact=email) | Q(username__iexact=email)
            )
            
            # Vérifier le mot de passe
            if user.check_password(password) and self.user_can_authenticate(user):
                return user
                
        except User.DoesNotExist:
            # Exécuter le hashage par défaut pour éviter les attaques de timing
            User().set_password(password)
            return None
            
        return None
    
    def user_can_authenticate(self, user):
        """
        Vérifie si l'utilisateur peut s'authentifier.
        
        Args:
            user: L'utilisateur à vérifier
            
        Returns:
            bool: True si l'utilisateur peut s'authentifier, False sinon
        """
        is_active = getattr(user, 'is_active', None)
        return is_active or is_active is None
    
    def get_user(self, user_id):
        """
        Récupère un utilisateur par son ID.
        
        Args:
            user_id: L'ID de l'utilisateur
            
        Returns:
            User: L'utilisateur ou None s'il n'existe pas
        """
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
