"""
API d'authentification mobile pour PTCCare Flutter
Gestion sécurisée de l'authentification avec JWT pour l'application mobile
"""

import json
import jwt
from datetime import datetime, timedelta
from django.conf import settings
from django.contrib.auth import authenticate
from django.contrib.auth.models import User, Group
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils import timezone
from django.core.exceptions import ValidationError

from ..models import Profile, Hospital, Service, Speciality, Language
from ptcapp.models.password_reset import UserPasswordStatus, PasswordResetToken
from ..helpers.authentification_helper import authenticated_user
from ..backends import EmailBackend
from ..decorators.cors_decorators import mobile_api_cors

# Configuration JWT
JWT_SECRET = getattr(settings, 'SECRET_KEY', 'ptccare-mobile-secret-key')
JWT_ALGORITHM = 'HS256'
JWT_EXPIRATION_HOURS = 24 * 7  # 7 jours pour mobile

def generate_jwt_token(user, profile):
    """Générer un token JWT pour l'utilisateur"""
    payload = {
        'user_id': user.id,
        'username': user.username,
        'profile_id': profile.id,
        'role': user.groups.first().name if user.groups.exists() else 'patient',
        'hospital_id': profile.hospital.id if profile.hospital else None,
        'exp': datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS),
        'iat': datetime.utcnow(),
        'iss': 'ptccare-mobile'
    }
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

def verify_jwt_token(token):
    """Vérifier et décoder un token JWT"""
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

@csrf_exempt
@mobile_api_cors
def mobile_login(request):
    """
    Authentification mobile avec JWT
    POST /api/mobile/auth/login
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        data = json.loads(request.body)
        email = data.get('email') or data.get('username')
        password = data.get('password')
        device_info = data.get('device_info', {})

        if not email or not password:
            return JsonResponse({
                'error': 'Email et mot de passe requis',
                'code': 'MISSING_CREDENTIALS'
            }, status=400)

        # Authentification avec le backend email
        backend = EmailBackend()
        user = backend.authenticate(None, username=email, password=password)
        if not user:
            return JsonResponse({
                'error': 'Identifiants invalides',
                'code': 'INVALID_CREDENTIALS'
            }, status=401)

        # Récupérer le profil
        try:
            profile = Profile.objects.get(user=user)
        except Profile.DoesNotExist:
            return JsonResponse({
                'error': 'Profil utilisateur non trouvé',
                'code': 'PROFILE_NOT_FOUND'
            }, status=404)

        # Vérifier le rôle (seuls admin, docteur, assistant autorisés sur mobile)
        allowed_roles = ['admin', 'docteur', 'assistant']
        user_role = user.groups.first().name if user.groups.exists() else None
        
        if user_role not in allowed_roles:
            return JsonResponse({
                'error': 'Accès mobile non autorisé pour ce rôle',
                'code': 'ROLE_NOT_ALLOWED'
            }, status=403)

        # Vérifier le statut de changement de mot de passe
        password_status = UserPasswordStatus.objects.filter(user=user).first()
        must_change_password = password_status.must_change_password if password_status else False

        # Générer le token JWT
        jwt_token = generate_jwt_token(user, profile)

        # Préparer les données de réponse
        response_data = {
            'status': 'success',
            'message': 'Connexion réussie',
            'token': jwt_token,
            'token_type': 'Bearer',
            'expires_in': JWT_EXPIRATION_HOURS * 3600,  # en secondes
            'user': {
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'profile_id': profile.id,
                'role': user_role,
                'name': f"{profile.firstname} {profile.lastname}",
                'firstname': profile.firstname,
                'lastname': profile.lastname,
                'tel': profile.tel,
                'hospital_id': profile.hospital.id if profile.hospital else None,
                'hospital_name': profile.hospital.name if profile.hospital else None,
                'service_id': profile.service.id if profile.service else None,
                'service_name': profile.service.name if profile.service else None,
                'speciality_id': profile.speciality.id if profile.speciality else None,
                'speciality_name': profile.speciality.name if profile.speciality else None,
                'must_change_password': must_change_password
            }
        }

        # Ajouter le token de réinitialisation si nécessaire
        if must_change_password:
            try:
                reset_token = PasswordResetToken.objects.get(
                    user=user,
                    is_used=False,
                    expires_at__gt=timezone.now()
                )
                response_data['password_reset_token'] = reset_token.token
            except PasswordResetToken.DoesNotExist:
                pass

        return JsonResponse(response_data)

    except json.JSONDecodeError:
        return JsonResponse({
            'error': 'Données JSON invalides',
            'code': 'INVALID_JSON'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'error': f'Erreur serveur: {str(e)}',
            'code': 'SERVER_ERROR'
        }, status=500)

@csrf_exempt
@mobile_api_cors
def mobile_refresh_token(request):
    """
    Rafraîchir le token JWT
    POST /api/mobile/auth/refresh
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        # Récupérer le token depuis l'en-tête Authorization
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return JsonResponse({
                'error': 'Token manquant',
                'code': 'MISSING_TOKEN'
            }, status=401)

        token = auth_header.split(' ')[1]
        payload = verify_jwt_token(token)

        if not payload:
            return JsonResponse({
                'error': 'Token invalide ou expiré',
                'code': 'INVALID_TOKEN'
            }, status=401)

        # Récupérer l'utilisateur et le profil
        try:
            user = User.objects.get(id=payload['user_id'])
            profile = Profile.objects.get(id=payload['profile_id'])
        except (User.DoesNotExist, Profile.DoesNotExist):
            return JsonResponse({
                'error': 'Utilisateur non trouvé',
                'code': 'USER_NOT_FOUND'
            }, status=404)

        # Générer un nouveau token
        new_token = generate_jwt_token(user, profile)

        return JsonResponse({
            'status': 'success',
            'message': 'Token rafraîchi',
            'token': new_token,
            'token_type': 'Bearer',
            'expires_in': JWT_EXPIRATION_HOURS * 3600
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Erreur serveur: {str(e)}',
            'code': 'SERVER_ERROR'
        }, status=500)

@csrf_exempt
@mobile_api_cors
def mobile_logout(request):
    """
    Déconnexion mobile (invalidation côté client)
    POST /api/mobile/auth/logout
    """
    if request.method != 'POST':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    # Pour JWT, la déconnexion est principalement côté client
    # On peut ajouter une blacklist de tokens si nécessaire
    return JsonResponse({
        'status': 'success',
        'message': 'Déconnexion réussie'
    })

@csrf_exempt
@mobile_api_cors
def mobile_verify_token(request):
    """
    Vérifier la validité d'un token
    GET /api/mobile/auth/verify
    """
    if request.method != 'GET':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)

    try:
        auth_header = request.headers.get('Authorization')
        if not auth_header or not auth_header.startswith('Bearer '):
            return JsonResponse({
                'error': 'Token manquant',
                'code': 'MISSING_TOKEN'
            }, status=401)

        token = auth_header.split(' ')[1]
        payload = verify_jwt_token(token)

        if not payload:
            return JsonResponse({
                'error': 'Token invalide ou expiré',
                'code': 'INVALID_TOKEN'
            }, status=401)

        return JsonResponse({
            'status': 'success',
            'message': 'Token valide',
            'user_id': payload['user_id'],
            'role': payload['role'],
            'expires_at': payload['exp']
        })

    except Exception as e:
        return JsonResponse({
            'error': f'Erreur serveur: {str(e)}',
            'code': 'SERVER_ERROR'
        }, status=500)
