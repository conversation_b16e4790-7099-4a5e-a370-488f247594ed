# PTC Care Mobile Integration - Quick Reference

## 🚀 Essential Endpoints for Mobile Apps

### **1. User Login** 
```http
POST /api/login/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Success Response:**
```json
{
  "status": "success",
  "user_id": 123,
  "username": "PAT-12345678",
  "role": "patient",
  "name": "<PERSON>",
  "must_change_password": false,
  "password_reset_token": "abc123..." // Only if must_change_password=true
}
```

### **2. Password Reset Request**
```http
POST /api/request-password-reset/
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Si cette adresse email existe, un lien de réinitialisation a été envoyé"
}
```

### **3. Password Change with Token**
```http
POST /api/change-password/
Content-Type: application/json

{
  "email": "<EMAIL>",
  "token": "abc123def456...",
  "new_password": "NewPassword123!"
}
```

**Success Response:**
```json
{
  "status": "success",
  "message": "Mot de passe changé avec succès"
}
```

---

## 📱 Mobile Integration Flow

### **Login Flow with Password Change Detection**

```javascript
// 1. Login Request
const loginResponse = await fetch('/api/login/', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ email, password })
});

const userData = await loginResponse.json();

// 2. Handle Response
if (userData.must_change_password) {
  if (userData.password_reset_token) {
    // Direct password change
    navigateToPasswordChange(userData.password_reset_token);
  } else {
    // Request new token
    await requestPasswordReset(userData.email);
  }
} else {
  // Normal login
  saveUserData(userData);
  navigateToMainScreen();
}
```

### **Password Requirements**
- ✅ Minimum 8 characters
- ✅ At least one uppercase letter
- ✅ At least one lowercase letter  
- ✅ At least one digit
- ✅ At least one special character (!@#$%^&*)

---

## 🔧 Android Integration (Kotlin)

### **Data Classes**
```kotlin
data class LoginRequest(
    val email: String,
    val password: String
)

data class LoginResponse(
    val status: String,
    val user_id: Int,
    val username: String,
    val role: String,
    val name: String,
    val must_change_password: Boolean,
    val password_reset_token: String? = null
)
```

### **API Service**
```kotlin
class AuthService {
    suspend fun login(email: String, password: String): LoginResponse {
        return apiClient.post("/api/login/", LoginRequest(email, password))
    }
    
    suspend fun requestPasswordReset(email: String): String {
        val response = apiClient.post<Map<String, String>>(
            "/api/request-password-reset/", 
            mapOf("email" to email)
        )
        return response["message"] ?: ""
    }
    
    suspend fun changePassword(email: String, token: String, newPassword: String): String {
        val response = apiClient.post<Map<String, String>>(
            "/api/change-password/",
            mapOf(
                "email" to email,
                "token" to token,
                "new_password" to newPassword
            )
        )
        return response["message"] ?: ""
    }
}
```

### **Login Activity**
```kotlin
class LoginActivity : AppCompatActivity() {
    private fun performLogin() {
        lifecycleScope.launch {
            try {
                val response = authService.login(email, password)
                
                when {
                    response.must_change_password && response.password_reset_token != null -> {
                        navigateToPasswordChange(response.password_reset_token)
                    }
                    response.must_change_password -> {
                        showPasswordResetDialog()
                    }
                    else -> {
                        saveUserData(response)
                        navigateToMainActivity()
                    }
                }
            } catch (e: Exception) {
                showError(e.message)
            }
        }
    }
}
```

---

## 🍎 iOS Integration (Swift)

### **Data Models**
```swift
struct LoginRequest: Codable {
    let email: String
    let password: String
}

struct LoginResponse: Codable {
    let status: String
    let user_id: Int
    let username: String
    let role: String
    let name: String
    let must_change_password: Bool
    let password_reset_token: String?
}
```

### **API Service**
```swift
class AuthService {
    func login(email: String, password: String) async throws -> LoginResponse {
        let request = LoginRequest(email: email, password: password)
        return try await apiClient.post("/api/login/", body: request)
    }
    
    func requestPasswordReset(email: String) async throws -> String {
        let response: [String: String] = try await apiClient.post(
            "/api/request-password-reset/",
            body: ["email": email]
        )
        return response["message"] ?? ""
    }
    
    func changePassword(email: String, token: String, newPassword: String) async throws -> String {
        let response: [String: String] = try await apiClient.post(
            "/api/change-password/",
            body: [
                "email": email,
                "token": token,
                "new_password": newPassword
            ]
        )
        return response["message"] ?? ""
    }
}
```

### **Login View Controller**
```swift
class LoginViewController: UIViewController {
    private func performLogin() {
        Task {
            do {
                let response = try await authService.login(email: email, password: password)
                
                await MainActor.run {
                    switch (response.must_change_password, response.password_reset_token) {
                    case (true, let token?) where !token.isEmpty:
                        navigateToPasswordChange(token: token)
                    case (true, _):
                        showPasswordResetAlert()
                    default:
                        saveUserData(response)
                        navigateToMainScreen()
                    }
                }
            } catch {
                await MainActor.run {
                    showError(error.localizedDescription)
                }
            }
        }
    }
}
```

---

## 🔒 Security Best Practices

### **Secure Storage**

#### **Android**
```kotlin
// Use EncryptedSharedPreferences
val sharedPreferences = EncryptedSharedPreferences.create(
    "ptc_care_prefs",
    MasterKeys.getOrCreate(MasterKeys.AES256_GCM_SPEC),
    context,
    EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
    EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
)
```

#### **iOS**
```swift
// Use Keychain
import KeychainAccess

let keychain = Keychain(service: "com.ptccare.app")
keychain["user_data"] = userData
```

### **Network Security**
- ✅ Always use HTTPS in production
- ✅ Implement certificate pinning
- ✅ Add request timeouts (30 seconds recommended)
- ✅ Implement retry logic with exponential backoff

### **Error Handling**
```kotlin
// Android Example
sealed class ApiResult<T> {
    data class Success<T>(val data: T) : ApiResult<T>()
    data class Error<T>(val message: String, val code: Int) : ApiResult<T>()
    data class NetworkError<T>(val exception: Throwable) : ApiResult<T>()
}
```

---

## 🧪 Testing Checklist

### **Authentication Tests**
- [ ] Valid login with normal user
- [ ] Valid login with password change required
- [ ] Invalid credentials (401 error)
- [ ] Missing fields (400 error)
- [ ] Password reset request
- [ ] Password change with valid token
- [ ] Password change with invalid token
- [ ] Password validation (weak passwords)
- [ ] Network error handling
- [ ] Token expiration handling

### **Integration Tests**
- [ ] Deep link handling for password reset
- [ ] Biometric authentication (if implemented)
- [ ] Secure storage functionality
- [ ] Offline mode handling
- [ ] App state restoration after login

---

## 📊 Error Codes Reference

| Status Code | Description | Action |
|-------------|-------------|---------|
| 200 | Success | Continue with response data |
| 400 | Bad Request | Show validation errors |
| 401 | Unauthorized | Show "Invalid credentials" |
| 404 | Not Found | Show "User not found" |
| 405 | Method Not Allowed | Check HTTP method |
| 500 | Internal Server Error | Show generic error, retry |

---

## 🔗 Deep Links

### **Password Reset Deep Link**
```
ptccare://password-reset?token=abc123def456...
```

### **Android Manifest**
```xml
<activity android:name=".PasswordChangeActivity">
    <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="ptccare" android:host="password-reset" />
    </intent-filter>
</activity>
```

### **iOS URL Scheme**
```swift
// In SceneDelegate
func scene(_ scene: UIScene, openURLContexts URLContexts: Set<UIOpenURLContext>) {
    guard let url = URLContexts.first?.url else { return }
    
    if url.scheme == "ptccare" && url.host == "password-reset" {
        let components = URLComponents(url: url, resolvingAgainstBaseURL: false)
        if let token = components?.queryItems?.first(where: { $0.name == "token" })?.value {
            navigateToPasswordChange(token: token)
        }
    }
}
```

---

## 📱 Rate Limiting

| Endpoint | Limit | Window | Action on Limit |
|----------|-------|--------|-----------------|
| `/api/login/` | No limit | - | Implement client throttling |
| `/api/request-password-reset/` | 3 requests | 1 hour per email | Same success message |
| `/api/change-password/` | No limit | - | Token-based security |

---

**Base URL**: `https://your-domain.com` or `http://localhost:8000` (development)  
**API Version**: 1.0  
**Documentation**: See `PTC_CARE_AUTHENTICATION_API_DOCUMENTATION.md` for complete details
