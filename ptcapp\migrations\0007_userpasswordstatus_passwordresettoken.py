# Generated by Django 4.2.20 on 2025-06-14 09:27

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('ptcapp', '0006_remove_profile_pregnancy_end_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserPasswordStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('must_change_password', models.<PERSON><PERSON>anField(default=False)),
                ('password_changed_at', models.DateTimeField(blank=True, null=True)),
                ('first_login_completed', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='password_status', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Statut de mot de passe utilisateur',
                'verbose_name_plural': 'Statuts de mot de passe utilisateur',
                'db_table': 'ptcapp_user_password_status',
            },
        ),
        migrations.CreateModel(
            name='PasswordResetToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(max_length=64, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField()),
                ('is_used', models.BooleanField(default=False)),
                ('force_password_change', models.BooleanField(default=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='password_reset_token', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Token de changement de mot de passe',
                'verbose_name_plural': 'Tokens de changement de mot de passe',
                'db_table': 'ptcapp_password_reset_token',
            },
        ),
    ]
