{% extends 'assistance/layout.html' %} {% load static %}
{% load layout %}
{% block up-style %}
<!-- DataTables -->
<link href="{% static 'libs/magnific-popup/magnific-popup.css' %}" rel="stylesheet" type="text/css" /> 
<link href="{% static 'libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css' %}" rel="stylesheet" type="text/css" /> 
<style>
    .zoom-gallery a{
        width: 50%
    }

    .zoom-gallery a *{
        width: 100%
    }
</style>
{% endblock up-style %} 

{% block content %}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white text-left">
                <span class="h2 text-capitalize">Consultation du {{appointment.consul_date|date:"Y-m-d"}} {{appointment.consul_hour|date:"H:i"}}</span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8 col-sm-12">
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <h3>Nom du médécin</h3>
                                <p>{{appointment.doctor.user|auth_fullname}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h3>Date et heure de la rencontre</h3>
                                <p>{{appointment.consul_date|date:"Y-m-d"}} {{appointment.consul_hour|date:"H:i"}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h3>Type de consultation</h3>
                                <p>{{appointment.appointment_type}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h3>Statut</h3>
                                <p>
                                    {% if appointment.state == 'pending' %}
                                    <span class="bg-warning rounded-3 p-2 text-white">En attente</span>
                                    {% endif %}
                                    {% if appointment.state == 'missed' %}
                                    <span class="bg-danger rounded-3 p-2 text-white">Manquée</span>
                                    {% endif %}
                                    {% if appointment.state == 'canceled' %}
                                    <span class="bg-info rounded-3 p-2 text-white">Annulée</span>
                                    {% endif %}
                                    {% if appointment.state == 'done' %}
                                    <span class="bg-success rounded-3 p-2 text-white">Effectuée</span>
                                    {% endif %}
                                </p>
                            </div>
                            <div class="col-md-12 col-sm-12">
                                <h3>Données significatives relevées</h3>
                                <p>{% firstof appointment.consul_data "Pas d'informations renseignées"%}</p>
                            </div>
                            <div class="col-md-12 col-sm-12">
                                <h3>Synthèse de la consultation</h3>
                                <p>{% firstof appointment.consul_resume "Pas d'informations renseignées"%}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h3>Décisions</h3>
                                <p>{% firstof appointment.consul_decisions "Pas d'informations renseignées"%}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-12">
                        <h4>Documents</h4>
                        <div class="zoom-gallery">
                            <a class="float-start" href="{% static 'images/log-img.jpg' %} " data-source="{% static 'images/log-img.jpg' %} " title=""><img src="{% static 'images/log-img.jpg' %} " alt="img-3"></a>
                            <a class="float-start" href="{% static 'images/log-img.jpg' %} " data-source="{% static 'images/log-img.jpg' %} " title=""><img src="{% static 'images/log-img.jpg' %} " alt="img-7"></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %} {% block down-script %}
<script src="//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json"></script>
<!-- Required datatable js -->
<script src="{% static 'libs/datatables.net/js/jquery.dataTables.min.js'%}"></script>
<script src="{% static 'libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js'%}"></script>
<!-- Responsive examples -->
<script src="{% static 'libs/datatables.net-responsive/js/dataTables.responsive.min.js'%}"></script>
<script src="{% static 'libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js'%}"></script>
<!-- Datatable init js -->
{% comment %}
<script src="{% static 'js/pages/datatables.init.js' %}"></script> {% endcomment %}
<script src="{% static 'libs/magnific-popup/jquery.magnific-popup.min.js' %}"></script>
<script src="{% static 'js/pages/lightbox.init.js' %}"></script>
<script>
    var dt = $("#datatable").DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
        },
        "columnDefs": [{
            "targets": [5, 0],
            "orderable": false
        }],
        order: [
            [1, 'asc']
        ],
        drawCallback: function() {
            $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
        },
    });
    $(function() {
        $('[data-toggle="tooltip"]').tooltip()
    })
</script>
{% endblock down-script %}