{% extends 'patient/layout.html' %} 
{% load static %} 
{% load layout %}
{% block up-style %}
<!-- DataTables -->
<link href="{% static 'libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css' %}" rel="stylesheet" type="text/css" /> {% endblock up-style %} {% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white text-left">
                <span class="h2 text-capitalize">Renseignements</span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 col-sm-12">
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <h4>Nom de l'hopital</h4>
                                <p>{{doc_pat.doctor.hospital.name}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Contact</h4>
                                <p>{{doc_pat.doctor.hospital.tel}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Addresse</h4>
                                <p>{{doc_pat.doctor.hospital.address}}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-sm-12">
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <h4>Dernier Docteur rencontré</h4>
                                <p>{{doc_pat.doctor.user|auth_fullname}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Service</h4>
                                <p>{{doc_pat.doctor.service.name}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Contact</h4>
                                <p>{{doc_pat.doctor.tel}}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %} 
{% block down-script %}
<script src="//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json"></script>
<!-- Required datatable js -->
<script src="{% static 'libs/datatables.net/js/jquery.dataTables.min.js'%}"></script>
<script src="{% static 'libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js'%}"></script>
<!-- Responsive examples -->
<script src="{% static 'libs/datatables.net-responsive/js/dataTables.responsive.min.js'%}"></script>
<script src="{% static 'libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js'%}"></script>
<!-- Datatable init js -->
{% comment %}
<script src="{% static 'js/pages/datatables.init.js' %}"></script> {% endcomment %}
<script>
    var dt = $("#datatable").DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
        },
        "columnDefs": [{
            "targets": [5, 0],
            "orderable": false
        }],
        order: [
            [1, 'asc']
        ],
        drawCallback: function() {
            $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
        },
    });
    $(function() {
        $('[data-toggle="tooltip"]').tooltip()
    })
</script>
{% endblock down-script %}