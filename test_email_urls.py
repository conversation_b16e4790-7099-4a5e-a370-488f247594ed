#!/usr/bin/env python
"""
Script pour tester que les URLs d'email utilisent le bon domaine
"""
import os
import django

# Configuration Django
if 'DYNO' in os.environ:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ptccare.settings_production')
else:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ptccare.settings')

django.setup()

from django.conf import settings
from django.contrib.auth.models import User, Group
from ptcapp.models.profile import Profile
from ptcapp.models.password_reset import PasswordResetToken
from ptcapp.services.email_service import EmailNotificationService

def test_email_urls():
    print("🔍 Test des URLs d'email...")
    
    # Vérifier la configuration
    base_url = getattr(settings, 'PTCCARE_BASE_URL', 'http://localhost:8000')
    print(f"📍 PTCCARE_BASE_URL: {base_url}")
    
    # Créer un utilisateur de test temporaire
    try:
        # Créer un utilisateur temporaire
        user = User.objects.create_user(
            username='TEST-EMAIL-URL',
            email='<EMAIL>',
            password='temp123'
        )
        
        # Ajouter au groupe patient
        group, created = Group.objects.get_or_create(name='patient')
        user.groups.add(group)
        
        # Créer un profil
        profile = Profile.objects.create(
            user=user,
            firstname='Test',
            lastname='Email',
            tel='+229 00 00 00 00',
            sexe='F'
        )
        
        # Créer un token de réinitialisation
        reset_token = PasswordResetToken.objects.create(user=user)
        
        # Tester la génération d'URL (comme dans le service email)
        if 'DYNO' in os.environ:
            base_url = 'https://ptccare-web-ae382d4ad8cc.herokuapp.com'
        password_change_url = f"{base_url}/change-password/{reset_token.token}/"
        
        print(f"🔗 URL générée: {password_change_url}")
        
        # Vérifier que l'URL contient le bon domaine
        if 'localhost' in password_change_url:
            print("❌ ERREUR: L'URL contient encore 'localhost'")
            return False
        elif 'herokuapp.com' in password_change_url:
            print("✅ SUCCÈS: L'URL utilise le domaine Heroku")
            return True
        else:
            print(f"⚠️ ATTENTION: Domaine inattendu dans l'URL")
            return False
            
    except Exception as e:
        print(f"❌ Erreur lors du test: {e}")
        return False
    finally:
        # Nettoyer
        try:
            if 'user' in locals():
                user.delete()
        except:
            pass

def main():
    print("🧪 Test de configuration des URLs d'email")
    print("=" * 50)
    
    success = test_email_urls()
    
    if success:
        print("\n🎉 Configuration des URLs d'email correcte !")
    else:
        print("\n❌ Problème avec la configuration des URLs d'email")
    
    return success

if __name__ == "__main__":
    main()
