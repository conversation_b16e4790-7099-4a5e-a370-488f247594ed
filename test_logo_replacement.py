#!/usr/bin/env python
"""
Script de test pour valider le remplacement du logo PTC Care
"""

import os
import re

def check_file_exists(file_path):
    """Vérifie si un fichier existe."""
    return os.path.exists(file_path)

def check_template_logo_reference(template_path, expected_logo):
    """Vérifie que le template référence le bon logo."""
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Chercher les références au logo
        logo_pattern = r'<img[^>]*src="[^"]*static[^"]*images/([^"]*)"[^>]*class="logo"'
        matches = re.findall(logo_pattern, content)
        
        if matches:
            found_logo = matches[0]
            return found_logo == expected_logo, found_logo
        else:
            return False, "Aucune référence logo trouvée"
            
    except Exception as e:
        return False, f"Erreur: {str(e)}"

def check_css_logo_styles():
    """Vérifie que les styles CSS pour le logo sont appropriés."""
    css_path = "ptcapp/static/css/login.css"
    
    try:
        with open(css_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Vérifier la présence des styles logo
        has_logo_class = '.logo' in content
        has_max_width = 'max-width' in content
        has_responsive = '@media' in content and '.logo' in content
        
        return {
            'has_logo_class': has_logo_class,
            'has_max_width': has_max_width,
            'has_responsive': has_responsive,
            'content_preview': content[:200] + "..." if len(content) > 200 else content
        }
        
    except Exception as e:
        return {'error': str(e)}

def validate_logo_replacement():
    """Valide le remplacement complet du logo."""
    print("🔍 VALIDATION DU REMPLACEMENT DU LOGO PTC CARE")
    print("=" * 55)
    
    results = {
        'files_exist': {},
        'template_updates': {},
        'css_styles': {},
        'overall_success': True
    }
    
    # 1. Vérifier l'existence des fichiers de logo
    print("\n📁 1. Vérification des fichiers de logo")
    print("-" * 40)
    
    logo_files = {
        'Nouveau logo SVG': 'ptcapp/static/images/ptccare-logo.svg',
        'Nouveau logo PNG': 'ptcapp/static/images/ptccare-logo-new.png',
        'Ancien logo (référence)': 'ptcapp/static/images/logo-light.png'
    }
    
    for name, path in logo_files.items():
        exists = check_file_exists(path)
        results['files_exist'][name] = exists
        
        if exists:
            print(f"   ✅ {name}: {path}")
        else:
            print(f"   ❌ {name}: {path} - MANQUANT")
            if 'Nouveau logo' in name:
                results['overall_success'] = False
    
    # 2. Vérifier les mises à jour des templates
    print("\n📄 2. Vérification des templates")
    print("-" * 40)
    
    templates = {
        'Page de connexion': 'ptcapp/templates/auth/index.html',
        'Page changement MDP': 'ptcapp/templates/auth/change_password_token.html'
    }
    
    expected_logo = 'ptccare-logo.svg'
    
    for name, path in templates.items():
        if check_file_exists(path):
            is_updated, found_logo = check_template_logo_reference(path, expected_logo)
            results['template_updates'][name] = {
                'updated': is_updated,
                'found_logo': found_logo
            }
            
            if is_updated:
                print(f"   ✅ {name}: Logo mis à jour ({found_logo})")
            else:
                print(f"   ❌ {name}: Logo non mis à jour (trouvé: {found_logo})")
                results['overall_success'] = False
        else:
            print(f"   ❌ {name}: Template non trouvé")
            results['overall_success'] = False
    
    # 3. Vérifier les styles CSS
    print("\n🎨 3. Vérification des styles CSS")
    print("-" * 40)
    
    css_check = check_css_logo_styles()
    results['css_styles'] = css_check
    
    if 'error' in css_check:
        print(f"   ❌ Erreur CSS: {css_check['error']}")
        results['overall_success'] = False
    else:
        if css_check['has_logo_class']:
            print(f"   ✅ Classe .logo présente")
        else:
            print(f"   ❌ Classe .logo manquante")
            
        if css_check['has_max_width']:
            print(f"   ✅ Styles de dimensionnement présents")
        else:
            print(f"   ⚠️  Styles de dimensionnement basiques")
            
        if css_check['has_responsive']:
            print(f"   ✅ Styles responsive présents")
        else:
            print(f"   ⚠️  Styles responsive basiques")
    
    # 4. Résumé et instructions de test
    print("\n" + "=" * 55)
    print("📊 RÉSUMÉ DE LA VALIDATION")
    print("=" * 55)
    
    if results['overall_success']:
        print("🎉 REMPLACEMENT DU LOGO RÉUSSI !")
        print("✅ Tous les éléments critiques sont en place")
    else:
        print("⚠️  REMPLACEMENT PARTIELLEMENT RÉUSSI")
        print("🔧 Certains éléments nécessitent une attention")
    
    # 5. Instructions de test manuel
    print("\n📋 INSTRUCTIONS DE TEST MANUEL")
    print("-" * 40)
    print("1. 🌐 Ouvrez http://localhost:8000/ (page de connexion)")
    print("   • Vérifiez que le nouveau logo PTC Care s'affiche")
    print("   • Vérifiez la taille et l'alignement du logo")
    print("   • Testez sur mobile (logo plus petit)")
    
    print("\n2. 🔑 Créez un token de changement de mot de passe")
    print("   • Ouvrez http://localhost:8000/change-password/{token}/")
    print("   • Vérifiez que le même logo s'affiche")
    print("   • Vérifiez la cohérence avec la page de connexion")
    
    print("\n3. 📱 Test de responsivité")
    print("   • Redimensionnez la fenêtre du navigateur")
    print("   • Vérifiez que le logo s'adapte correctement")
    print("   • Testez sur différentes tailles d'écran")
    
    print("\n4. 🎨 Test de qualité visuelle")
    print("   • Vérifiez que le logo est net et bien défini")
    print("   • Vérifiez que les couleurs sont correctes")
    print("   • Vérifiez l'alignement avec les autres éléments")
    
    # 6. Informations techniques
    print("\n🔧 INFORMATIONS TECHNIQUES")
    print("-" * 40)
    print(f"• Nouveau logo: ptccare-logo.svg")
    print(f"• Format: SVG (vectoriel, redimensionnable)")
    print(f"• Couleurs: Bleu (#4285f4), Rouge (#ea4335)")
    print(f"• Dimensions: 400x300 pixels")
    print(f"• Classe CSS: .logo")
    print(f"• Responsive: Oui (150px sur mobile)")
    
    return results

def main():
    """Fonction principale."""
    results = validate_logo_replacement()
    
    print(f"\n💡 CONSEILS SUPPLÉMENTAIRES")
    print("-" * 40)
    print("• Si le logo ne s'affiche pas, vérifiez les permissions des fichiers")
    print("• Si le logo est trop grand/petit, ajustez max-width dans login.css")
    print("• Pour une meilleure performance, considérez optimiser le SVG")
    print("• Testez sur différents navigateurs pour la compatibilité")
    
    return results['overall_success']

if __name__ == '__main__':
    main()
