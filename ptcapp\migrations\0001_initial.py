# Generated by Django 4.0.2 on 2022-07-06 14:25

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('consul_date', models.DateField()),
                ('consul_hour', models.TimeField()),
                ('appointment_type', models.CharField(max_length=50)),
                ('state', models.CharField(choices=[('EN ATTENTE', 'EN ATTENTE'), ('MANQUEE', 'MANQUEE'), ('ANNULEE', 'ANNULEE'), ('EFFECTUEE', 'EFFECTUEE')], default='EN ATTENTE', max_length=20)),
                ('consul_data', models.TextField(max_length=500, null=True)),
                ('consul_resume', models.TextField(max_length=500, null=True)),
                ('consul_decisions', models.TextField(max_length=500, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Hospital',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('address', models.TextField(max_length=250)),
                ('tel', models.CharField(max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Language',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=20)),
                ('path', models.CharField(max_length=155)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Pregnancy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('state', models.CharField(blank=True, max_length=15, null=True)),
                ('situation', models.CharField(max_length=50)),
                ('description', models.TextField(max_length=255)),
                ('start_date', models.DateField(blank=True, null=True)),
                ('end_date', models.DateField(blank=True, null=True)),
                ('mother_state', models.CharField(blank=True, max_length=100, null=True)),
                ('children_state', models.CharField(blank=True, max_length=100, null=True)),
                ('child_number', models.CharField(blank=True, max_length=100, null=True)),
                ('interruption_cause', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Profile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('firstname', models.CharField(max_length=50)),
                ('lastname', models.CharField(max_length=250)),
                ('tel', models.CharField(max_length=250)),
                ('sexe', models.CharField(choices=[('M', 'M'), ('F', 'F')], default='F', max_length=100)),
                ('birth_date', models.DateField(null=True)),
                ('address', models.CharField(max_length=250)),
                ('occupation', models.CharField(blank=True, max_length=50, null=True)),
                ('assurance', models.CharField(blank=True, max_length=50, null=True)),
                ('special_marks', models.TextField(blank=True, max_length=250, null=True)),
                ('personal_history', models.TextField(blank=True, max_length=250, null=True)),
                ('family_history', models.TextField(blank=True, max_length=250, null=True)),
                ('allergies', models.TextField(blank=True, max_length=200, null=True)),
                ('vaccinations', models.TextField(blank=True, max_length=200, null=True)),
                ('picture', models.CharField(blank=True, max_length=150, null=True)),
                ('child', models.BooleanField(blank=True, null=True)),
                ('archived', models.BooleanField(default=False)),
                ('alive', models.BooleanField(default=True)),
                ('dead_at', models.DateField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('archived_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='archived_by', to=settings.AUTH_USER_MODEL)),
                ('assistant', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assistant', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_by', to=settings.AUTH_USER_MODEL)),
                ('hospital', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='ptcapp.hospital')),
                ('language', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='ptcapp.language')),
            ],
        ),
        migrations.CreateModel(
            name='Speciality',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('description', models.TextField(max_length=250)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('description', models.TextField(max_length=250)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('hospital', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='ptcapp.hospital')),
            ],
        ),
        migrations.CreateModel(
            name='Record',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('archived', models.BooleanField(default=False)),
                ('archived_at', models.DateTimeField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('archived_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='docteur', to='ptcapp.profile')),
                ('profile', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='patient', to='ptcapp.profile')),
            ],
        ),
        migrations.AddField(
            model_name='profile',
            name='service',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='ptcapp.service'),
        ),
        migrations.AddField(
            model_name='profile',
            name='speciality',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='ptcapp.speciality'),
        ),
        migrations.AddField(
            model_name='profile',
            name='user',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user', to=settings.AUTH_USER_MODEL),
        ),
        migrations.CreateModel(
            name='OtherInformation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=20)),
                ('value', models.CharField(max_length=250)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('profile', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='ptcapp.profile')),
            ],
        ),
        migrations.CreateModel(
            name='MotherPregnancy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('mother', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='ptcapp.profile')),
                ('pregnancy', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='ptcapp.pregnancy')),
            ],
        ),
        migrations.CreateModel(
            name='MapMotherChild',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('child', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='enfant', to='ptcapp.profile')),
                ('mother', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='mère', to='ptcapp.profile')),
            ],
        ),
        migrations.CreateModel(
            name='MapDoctorPatient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('doctor', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='doctor', to='ptcapp.profile')),
                ('patient', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='patiente', to='ptcapp.profile')),
            ],
        ),
        migrations.CreateModel(
            name='AppointmentFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=50)),
                ('path', models.CharField(max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('appointment', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='appointment_file', to='ptcapp.appointment')),
            ],
        ),
        migrations.AddField(
            model_name='appointment',
            name='patient',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='ptcapp.profile'),
        ),
        migrations.CreateModel(
            name='Alert',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('ENVOYEE', 'ENVOYEE'), ('EN ATTENTE', 'EN ATTENTE'), ('ANNULEE', 'ANNULEE')], default='EN ATTENTE', max_length=100)),
                ('state', models.CharField(choices=[('APPEL VOCAL', 'APPEL VOCAL'), ('MESSAGE TEXTE', 'MESSAGE TEXTE')], default='EN ATTENTE', max_length=100)),
                ('task_id', models.CharField(max_length=100, null=True)),
                ('date', models.DateField(null=True)),
                ('time', models.TimeField(null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('appointment', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to='ptcapp.appointment')),
            ],
        ),
    ]
