from django.shortcuts import redirect
from django.urls import reverse
from django.http import JsonResponse
from ptcapp.models.password_reset import UserPasswordStatus
import logging

logger = logging.getLogger(__name__)


class ForcePasswordChangeMiddleware:
    """
    Middleware pour forcer le changement de mot de passe des nouveaux utilisateurs.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # URLs qui ne nécessitent pas de changement de mot de passe
        self.exempt_urls = [
            '/logout',
            '/change-password/',
            '/api/change-password/',
            '/static/',
            '/media/',
            '/admin/',  # Admin Django exempt
        ]
        
        # URLs API qui retournent une erreur JSON au lieu de rediriger
        self.api_urls = [
            '/api/',
        ]
    
    def __call__(self, request):
        # Traiter la requête avant la vue
        response = self.process_request(request)
        if response:
            return response
        
        # Continuer avec la vue normale
        response = self.get_response(request)
        return response
    
    def process_request(self, request):
        """
        Vérifie si l'utilisateur doit changer son mot de passe.
        """
        # Ignorer si l'utilisateur n'est pas authentifié
        if not request.user.is_authenticated:
            return None
        
        # Ignorer les superusers
        if request.user.is_superuser:
            return None
        
        # Vérifier si l'URL est exemptée
        if self.is_exempt_url(request.path):
            return None
        
        try:
            # Vérifier le statut de mot de passe
            password_status = UserPasswordStatus.objects.get(user=request.user)
            
            if password_status.must_change_password:
                logger.info(f"Redirection de {request.user.username} vers changement de mot de passe")
                
                # Pour les APIs, retourner une erreur JSON
                if self.is_api_url(request.path):
                    return JsonResponse({
                        'error': 'password_change_required',
                        'message': 'Vous devez changer votre mot de passe avant de continuer',
                        'redirect_url': '/change-password/'
                    }, status=403)
                
                # Pour les vues web, rediriger vers la page de changement
                return redirect('change_password')
                
        except UserPasswordStatus.DoesNotExist:
            # Si pas de statut, créer un par défaut (utilisateur existant)
            UserPasswordStatus.objects.create(
                user=request.user,
                must_change_password=False,
                first_login_completed=True
            )
        
        return None
    
    def is_exempt_url(self, path):
        """
        Vérifie si l'URL est exemptée du changement de mot de passe.
        """
        for exempt_url in self.exempt_urls:
            if path.startswith(exempt_url):
                return True
        return False
    
    def is_api_url(self, path):
        """
        Vérifie si l'URL est une API.
        """
        for api_url in self.api_urls:
            if path.startswith(api_url):
                return True
        return False
