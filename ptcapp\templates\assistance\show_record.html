{% extends 'assistance/layout.html' %} 
{% load static %}
{% load layout %}
{% block up-style %}
<!-- DataTables -->
<link href="{% static 'libs/select2/css/select2.min.css' %}" rel="stylesheet" type="text/css" />
<link href="{% static 'libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css' %}" rel="stylesheet" type="text/css" /> 
<style>
    .action a{
        margin: 0 10px;
    }
</style>
{% endblock up-style %} 
{% block action_button %}
<a href="javascript:;" id="addAppointment" 
data-bs-toggle="modal" 
data-bs-target=".bs-example-modal-center"  class="btn btn-primary mx-3">Ajouter une consultation</a>
{% endblock action_button %}
{% block content %}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between">
                    <span class="h2 text-left text-capitalize">Dossier de {{profile.lastname}} {{profile.firstname}}</span>
                    <div class="col-md-6 col-sm-12">
                        <div class="d-flex justify-content-end">
                            <select class="w-50 form-control select2" name="associe_doctor" id="associe_doctor" >
                                {% for docteur in docteurs %}
                                    {% if 'docteur' == docteur.user|user_group %}                                                  
                                    <option value="{{docteur.pk}}">{{docteur.user|auth_fullname}}</option>                                                  
                                    {% endif %}
                                {% endfor %}
                            </select>
                            <button onclick="attached()" class="btn btn-primary mx-3">Associé</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8 col-sm-12">
                        <div class="row">
                            <h4 class="text-capitalize"> identification </h4>
                            <div class="col-md-6 col-sm-12">
                                <h6>Nom complet</h6>
                                <p>{{profile.lastname}} {{profile.firstname}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h6>Sexe</h6>
                                <p>{{profile.sexe}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h6>Date de naissance</h6>
                                <p>{{profile.birth_date|date:"Y-m-d"}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h6>Signes particuliers</h6>
                                <p>{{profile.special_marks}}</p>
                            </div>
                        </div>
                        <div class="row">
                            <h4 class="text-capitalize"> Informations administratives </h4>
                            <div class="col-md-6 col-sm-12">
                                <h6>Adresse</h6>
                                <p>{{profile.address}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h6>Téléphone</h6>
                                <p>{{profile.tel}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h6>Profession</h6>
                                <p>{{profile.occupation}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h6>Assurance</h6>
                                <p>{{profile.assurance}}</p>
                            </div>
                        </div>
                        <div class="row">
                            <h4 class="text-capitalize"> Données d'alerte </h4>
                            <div class="col-md-12 col-sm-12">
                                <h6>Allergies / intolérances médicamenteuses</h6>
                                <p>{{profile.allergies}}</p>
                            </div>
                        </div>
                        <div class="row">
                            <h4 class="text-capitalize"> Histoire médicale actualisée et facteurs de santé </h4>
                            <div class="col-md-12 col-sm-12">
                                <h6>Antécédents personnels</h6>
                                <p>{{profile.personal_history}}</p>
                            </div>
                            <div class="col-md-12 col-sm-12">
                                <h6>Antécédents familiaux</h6>
                                <p>{{profile.family_history}}</p>
                            </div>
                            <div class="col-md-12 col-sm-12">
                                <h6>Vaccinations et autres actions de prévention et de dépistage</h6>
                                <p>{{profile.vaccinations}}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-12">
                        <h4>Consultations</h4>
                        <table id="datatable" class="table table-bordered table-striped dt-responsive nowrap" style="border-collapse: collapse; border-spacing: 0; width: 100%">
                            <thead>
                                <tr>
                                    <th>Nom du médécin</th>
                                    <th>Date de rencontre</th>
                                    <th>Action</th>
                                </tr>
                            </thead>

                            <tbody>
                                {% for appointment in appointments %}
                                    <tr>
                                        <td>---</td>
                                        <td>{{appointment.consul_date|date:"Y-m-d"}} {{appointment.consul_hour|date:"H : i"}}</td>
                                        <td class="action"><a href="{% url 'doctor.appointment.show' id=appointment.id %}" data-toggle="tooltip" data-placement="right" title="Détails"><i class="ri-eye-line" ></i></a>
                                        <a href="{% url 'doctor.appointment.edit' id=appointment.id %}" data-toggle="tooltip" data-placement="right" title="Modifier"><i class="ri-pencil-line" ></i></a></td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade bs-example-modal-center" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-title">Créer une consultation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="#" method="post">
                    <div class="row">
                        <div class="col-md-12 col-sm-12 mt-4">
                            {% comment %} <fieldset class="h-100"> {% endcomment %}
                                {% comment %} <legend>Informations Générales</legend> {% endcomment %}
                                <input type="hidden" name="_method" id="method" value="post">
                                {% csrf_token %}
                                {% comment %} <div class="my-3">
                                    <label for="num_record" class="form-label required">Numéro du dossier</label>
                                    <input class="form-control" type="text" name="num_record" value="DOS-{{id}}" id="num_record" required="required" disabled>
                                    <input class="form-control" type="hidden" name="current_num_record" value="DOS-{{id}}" id="current_num_record" required="required">
                                </div> {% endcomment %}
                                <div class="my-3">
                                    <label for="type_appointment" class="form-label required">Type de consultation</label>
                                    <input class="form-control" type="text" name="type_appointment" placeholder="Type de consultation" id="type_appointment" required="required">
                                </div>
                            {% comment %} </fieldset> {% endcomment %}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 col-sm-12">
                            <label for="consul_date" class="form-label required">Date de la Consultaion</label>
                            <input class="form-control" type="date" name="consul_date" placeholder="Date d'inscriptation" id="consul_date" required>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <label for="consul_time" class="form-label required">Heure de la Consultaion</label>
                            <input class="form-control" type="time" name="consul_time" placeholder="Date d'inscriptation" id="consul_time" required>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col">
                            <input class="btn btn-success w-auto" id="form-submit" type="submit" value="Sauvegarder">
                        </div>
                    </div>
                </form>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
{% endblock content %} {% block down-script %}
<script src="{% static 'libs/select2/js/select2.min.js'%}"></script>
    <script src="//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json"></script>
    <!-- Required datatable js -->
    <script src="{% static 'libs/datatables.net/js/jquery.dataTables.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js'%}"></script>
    <!-- Responsive examples -->
    <script src="{% static 'libs/datatables.net-responsive/js/dataTables.responsive.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js'%}"></script>
    <!-- Datatable init js -->
    {% comment %} <script src="{% static 'js/pages/datatables.init.js' %}"></script> {% endcomment %}
    <script>
        $('.select2').select2()
        var dt = $("#datatable").DataTable({
            searching: false,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
            },
            order : [[1, 'desc']],
            drawCallback: function() {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })

        $('#new_doc').click(function(){
            $('#num_record').val('DOS-')
            $('#num_record').attr('disabled', false)
        })

        $('#current_doc').click(function(){
            $('#num_record').val($('#current_num_record').val())
            $('#num_record').attr('disabled', true)
        })


        $('#form-submit').click(function(e){
            e.preventDefault()
            $.post(
                "{% url 'appointment.create' %}",
                {
                    csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val(),
                    appointment_patient : {{profile.id}},
                    appointment_type : $('#type_appointment').val(),
                    appointment_date : $('#consul_date').val(),
                    appointment_hour : $('#consul_time').val()
                },
                function(response){
                    if(response.success){
                        $('.bs-example-modal-center').modal('toggle');
                        if($('#method').val() == 'put'){
                            Swal.fire("Mis à jour!", "", "success");
                        }else if($('#method').val() == 'post'){
                            Swal.fire("Création!", "", "success");
                        }
                        window.location.reload()
                    }
                }
            );
        })

        function attached(){
            $.post(
                "{% url 'profile.attached' %}",
                {
                    csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val(),
                    patient : {{profile.id}},
                    doctor : $('#associe_doctor').val()
                },
                function(response){
                    if(response.success){
                        Swal.fire("Docteur associé au patient avec succès", "", "success");
                    }else{
                        Swal.fire("Docteur déjà associé au patient", "", "info");
                    }
                }
            );
        }
    </script>
{% endblock down-script %}