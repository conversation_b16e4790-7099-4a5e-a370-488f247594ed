# Guide d'Intégration Mobile - Partie 3

## 📱 Considérations Mobiles Spécifiques

### Bibliothèques HTTP Recommandées

#### Android
```kotlin
// build.gradle (Module: app)
dependencies {
    // Networking
    implementation "com.squareup.retrofit2:retrofit:2.9.0"
    implementation "com.squareup.retrofit2:converter-gson:2.9.0"
    implementation "com.squareup.okhttp3:okhttp:4.11.0"
    implementation "com.squareup.okhttp3:logging-interceptor:4.11.0"
    
    // Coroutines pour async
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"
    
    // Sécurité
    implementation "androidx.security:security-crypto:1.1.0-alpha06"
    
    // Base de données locale
    implementation "androidx.room:room-runtime:2.5.0"
    implementation "androidx.room:room-ktx:2.5.0"
    kapt "androidx.room:room-compiler:2.5.0"
    
    // Injection de dépendances
    implementation "com.google.dagger:hilt-android:2.47"
    kapt "com.google.dagger:hilt-compiler:2.47"
}
```

#### iOS
```swift
// Package.swift ou CocoaPods
dependencies: [
    .package(url: "https://github.com/Alamofire/Alamofire.git", from: "5.8.0"),
    .package(url: "https://github.com/kishikawakatsumi/KeychainAccess.git", from: "4.2.2"),
    .package(url: "https://github.com/realm/realm-swift.git", from: "10.42.0"),
    .package(url: "https://github.com/apple/swift-crypto.git", from: "3.0.0")
]

// Configuration Alamofire
class APIClient {
    static let shared = APIClient()
    
    private let session: Session
    
    init() {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        configuration.timeoutIntervalForResource = 60
        
        // Certificate Pinning
        let evaluators = [
            "your-api-domain.com": PinnedCertificatesTrustEvaluator()
        ]
        let serverTrustManager = ServerTrustManager(evaluators: evaluators)
        
        session = Session(
            configuration: configuration,
            serverTrustManager: serverTrustManager,
            interceptor: AuthenticationInterceptor()
        )
    }
}
```

### Gestion de la Batterie et Optimisation

#### Android - WorkManager pour Sync en Arrière-plan
```kotlin
@HiltWorker
class SyncWorker @AssistedInject constructor(
    @Assisted context: Context,
    @Assisted workerParams: WorkerParameters,
    private val syncManager: SyncManager
) : CoroutineWorker(context, workerParams) {
    
    override suspend fun doWork(): Result {
        return try {
            when (val result = syncManager.syncData()) {
                is SyncResult.Success -> Result.success()
                is SyncResult.NoConnection -> Result.retry()
                else -> Result.failure()
            }
        } catch (e: Exception) {
            Result.failure()
        }
    }
    
    @AssistedFactory
    interface Factory {
        fun create(context: Context, params: WorkerParameters): SyncWorker
    }
}

// Planification du travail
class SyncScheduler @Inject constructor(
    @ApplicationContext private val context: Context
) {
    fun schedulePeriodicSync() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.CONNECTED)
            .setRequiresBatteryNotLow(true)
            .build()
        
        val syncRequest = PeriodicWorkRequestBuilder<SyncWorker>(
            repeatInterval = 15,
            repeatIntervalTimeUnit = TimeUnit.MINUTES
        )
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.EXPONENTIAL,
                WorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .build()
        
        WorkManager.getInstance(context)
            .enqueueUniquePeriodicWork(
                "sync_work",
                ExistingPeriodicWorkPolicy.KEEP,
                syncRequest
            )
    }
}
```

#### iOS - Background App Refresh
```swift
class BackgroundSyncManager {
    func scheduleBackgroundSync() {
        let request = BGAppRefreshTaskRequest(identifier: "com.ptccare.sync")
        request.earliestBeginDate = Date(timeIntervalSinceNow: 15 * 60) // 15 minutes
        
        do {
            try BGTaskScheduler.shared.submit(request)
        } catch {
            print("Could not schedule app refresh: \(error)")
        }
    }
    
    func handleBackgroundSync(task: BGAppRefreshTask) {
        scheduleBackgroundSync() // Planifier la prochaine sync
        
        let syncOperation = SyncOperation()
        
        task.expirationHandler = {
            syncOperation.cancel()
        }
        
        syncOperation.completionBlock = {
            task.setTaskCompleted(success: !syncOperation.isCancelled)
        }
        
        OperationQueue().addOperation(syncOperation)
    }
}
```

### Cache et Compression des Données

#### Android - Cache Intelligent
```kotlin
class CacheManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private val cacheSize = 10 * 1024 * 1024 // 10 MB
    private val cache = Cache(File(context.cacheDir, "http_cache"), cacheSize.toLong())
    
    fun createCacheInterceptor(): Interceptor {
        return Interceptor { chain ->
            val request = chain.request()
            
            // Cache pour 5 minutes si en ligne, 7 jours si hors ligne
            val cacheControl = if (isOnline()) {
                CacheControl.Builder()
                    .maxAge(5, TimeUnit.MINUTES)
                    .build()
            } else {
                CacheControl.Builder()
                    .maxStale(7, TimeUnit.DAYS)
                    .build()
            }
            
            val newRequest = request.newBuilder()
                .cacheControl(cacheControl)
                .build()
            
            chain.proceed(newRequest)
        }
    }
    
    private fun isOnline(): Boolean {
        val connectivityManager = context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }
}

// Compression des données
class CompressionInterceptor : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        val compressedRequest = originalRequest.newBuilder()
            .header("Accept-Encoding", "gzip, deflate")
            .build()
        
        return chain.proceed(compressedRequest)
    }
}
```

### Notifications Push pour Synchronisation

#### Android - Firebase Cloud Messaging
```kotlin
@AndroidEntryPoint
class PTCFirebaseMessagingService : FirebaseMessagingService() {
    
    @Inject
    lateinit var syncManager: SyncManager
    
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        
        when (remoteMessage.data["type"]) {
            "sync_required" -> {
                // Déclencher une synchronisation
                CoroutineScope(Dispatchers.IO).launch {
                    syncManager.syncData()
                }
            }
            "password_change_required" -> {
                // Notifier l'utilisateur du changement de mot de passe requis
                showPasswordChangeNotification()
            }
            "new_patient_assigned" -> {
                // Nouveau patient assigné
                showNewPatientNotification(remoteMessage.data)
            }
        }
    }
    
    private fun showPasswordChangeNotification() {
        val intent = Intent(this, PasswordChangeActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val notification = NotificationCompat.Builder(this, "ptc_notifications")
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("Changement de mot de passe requis")
            .setContentText("Vous devez changer votre mot de passe pour continuer")
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .build()
        
        NotificationManagerCompat.from(this).notify(1, notification)
    }
}
```

## 🔧 Configuration et Déploiement

### Configuration des Environnements

#### Android - BuildConfig
```kotlin
// build.gradle (Module: app)
android {
    buildTypes {
        debug {
            buildConfigField "String", "API_BASE_URL", "\"http://10.0.2.2:8000\""
            buildConfigField "boolean", "DEBUG_MODE", "true"
            applicationIdSuffix ".debug"
        }
        
        staging {
            buildConfigField "String", "API_BASE_URL", "\"https://staging-api.ptccare.com\""
            buildConfigField "boolean", "DEBUG_MODE", "true"
            applicationIdSuffix ".staging"
        }
        
        release {
            buildConfigField "String", "API_BASE_URL", "\"https://api.ptccare.com\""
            buildConfigField "boolean", "DEBUG_MODE", "false"
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

// Utilisation dans le code
class ApiConfig {
    companion object {
        const val BASE_URL = BuildConfig.API_BASE_URL
        const val DEBUG = BuildConfig.DEBUG_MODE
    }
}
```

#### iOS - Configuration.plist
```xml
<!-- Configuration-Debug.plist -->
<?xml version="1.0" encoding="UTF-8"?>
<plist version="1.0">
<dict>
    <key>API_BASE_URL</key>
    <string>http://localhost:8000</string>
    <key>DEBUG_MODE</key>
    <true/>
</dict>
</plist>

<!-- Configuration-Release.plist -->
<?xml version="1.0" encoding="UTF-8"?>
<plist version="1.0">
<dict>
    <key>API_BASE_URL</key>
    <string>https://api.ptccare.com</string>
    <key>DEBUG_MODE</key>
    <false/>
</dict>
</plist>
```

### Tests d'Intégration

#### Android - Tests d'API
```kotlin
@RunWith(AndroidJUnit4::class)
@HiltAndroidTest
class ApiIntegrationTest {
    
    @get:Rule
    var hiltRule = HiltAndroidRule(this)
    
    @Inject
    lateinit var apiClient: ApiClient
    
    @Before
    fun setup() {
        hiltRule.inject()
    }
    
    @Test
    fun testLoginFlow() = runTest {
        // Test de connexion
        val loginResult = apiClient.login("<EMAIL>", "password123")
        assertThat(loginResult).isInstanceOf(AuthResult.Success::class.java)
        
        // Test d'accès aux données protégées
        val patientsResult = apiClient.getPatients()
        assertThat(patientsResult).isInstanceOf(DataResult.Success::class.java)
    }
    
    @Test
    fun testPasswordChangeRequired() = runTest {
        // Simuler un utilisateur nécessitant un changement de mot de passe
        val result = apiClient.getPatients()
        assertThat(result).isInstanceOf(DataResult.PasswordChangeRequired::class.java)
    }
    
    @Test
    fun testOfflineSync() = runTest {
        // Créer des données hors ligne
        val patient = LocalPatient(
            firstname = "Test",
            lastname = "Patient",
            tel = "+22912345678",
            email = "<EMAIL>",
            sexe = "M",
            birthDate = "1990-01-01",
            address = "Test Address"
        )
        
        localDatabase.patientDao().insertPatient(patient)
        
        // Tester la synchronisation
        val syncResult = syncManager.syncData()
        assertThat(syncResult).isInstanceOf(SyncResult.Success::class.java)
        
        // Vérifier que l'ID serveur a été mis à jour
        val updatedPatient = localDatabase.patientDao().getPatientByMobileId(patient.mobileId)
        assertThat(updatedPatient?.serverId).isNotNull()
    }
}
```

## 📋 Checklist d'Implémentation

### Phase 1: Configuration de Base
- [ ] Configuration des bibliothèques HTTP (Retrofit/Alamofire)
- [ ] Mise en place du stockage sécurisé (EncryptedSharedPreferences/Keychain)
- [ ] Configuration de la base de données locale (Room/Realm)
- [ ] Implémentation du gestionnaire d'authentification

### Phase 2: Intégration API
- [ ] Implémentation des endpoints de base (login, initial-data)
- [ ] Gestion des erreurs et codes de statut
- [ ] Implémentation du changement de mot de passe obligatoire
- [ ] Tests des endpoints de création d'utilisateurs

### Phase 3: Fonctionnalités Hors Ligne
- [ ] Implémentation du cache local
- [ ] Système de queue pour les actions hors ligne
- [ ] Gestionnaire de synchronisation
- [ ] Résolution des conflits de données

### Phase 4: Sécurité et Optimisation
- [ ] Certificate pinning
- [ ] Chiffrement des données sensibles
- [ ] Optimisation de la batterie
- [ ] Compression et cache des données

### Phase 5: Tests et Déploiement
- [ ] Tests unitaires et d'intégration
- [ ] Tests de performance
- [ ] Configuration des environnements
- [ ] Documentation pour l'équipe

## 🚀 Recommandations Finales

### Bonnes Pratiques
1. **Toujours valider les certificats SSL** en production
2. **Implémenter un retry intelligent** avec backoff exponentiel
3. **Utiliser des timeouts appropriés** (30s pour les requêtes, 60s pour les uploads)
4. **Chiffrer toutes les données sensibles** stockées localement
5. **Implémenter des logs détaillés** pour le debugging (sans exposer de données sensibles)

### Monitoring et Analytics
```kotlin
class AnalyticsManager @Inject constructor() {
    fun trackApiCall(endpoint: String, success: Boolean, responseTime: Long) {
        // Envoyer les métriques à votre service d'analytics
        Firebase.analytics.logEvent("api_call") {
            param("endpoint", endpoint)
            param("success", success)
            param("response_time", responseTime)
        }
    }
    
    fun trackSyncEvent(itemCount: Int, success: Boolean) {
        Firebase.analytics.logEvent("sync_event") {
            param("item_count", itemCount.toLong())
            param("success", success)
        }
    }
}
```

Cette architecture garantit une intégration robuste, sécurisée et performante des APIs PTC Care dans votre application mobile, avec une gestion complète des fonctionnalités hors ligne et des notifications email automatiques.
