"""
API de synchronisation mobile pour PTCCare
Endpoints pour la synchronisation bidirectionnelle des données
"""

import json
from datetime import datetime, timedelta
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.models import User
from django.db.models import Q
from django.utils import timezone

from ..models.profile import Profile
from ..models.patient import Patient
from ..models.appointment import Appointment
from ..models.pregnancy import Pregnancy
from ..models.consultation import Consultation
from ..middleware.mobile_auth_middleware import mobile_auth_required, doctor_or_admin_required
from ..decorators.cors_decorators import mobile_api_cors


@csrf_exempt
@mobile_api_cors
@mobile_auth_required
def sync_status(request):
    """
    GET /api/mobile/sync/status
    Obtenir le statut de synchronisation et les timestamps
    """
    if request.method != 'GET':
        return JsonResponse({'error': 'Méthode non autorisée'}, status=405)
    
    try:
        user = request.user
        profile = Profile.objects.get(user=user)
        
        # Dernières modifications par type de données
        last_sync_data = {
            'user_id': user.id,
            'last_sync': timezone.now().isoformat(),
            'server_time': timezone.now().isoformat(),
            'data_timestamps': {
                'patients': get_last_modified_timestamp(Patient, user),
                'appointments': get_last_modified_timestamp(Appointment, user),
                'pregnancies': get_last_modified_timestamp(Pregnancy, user),
                'consultations': get_last_modified_timestamp(Consultation, user),
            }
        }
        
        return JsonResponse({
            'status': 'success',
            'data': last_sync_data
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Erreur lors de la récupération du statut: {str(e)}'
        }, status=500)


@csrf_exempt
@mobile_api_cors
@mobile_auth_required
def sync_patients(request):
    """
    GET /api/mobile/sync/patients?since=timestamp
    POST /api/mobile/sync/patients (upload des modifications)
    Synchronisation des patients
    """
    try:
        user = request.user
        profile = Profile.objects.get(user=user)
        
        if request.method == 'GET':
            # Télécharger les patients modifiés depuis un timestamp
            since = request.GET.get('since')
            
            # Filtrer les patients selon le rôle
            if user.groups.filter(name='admin').exists():
                patients_query = Patient.objects.all()
            elif user.groups.filter(name='docteur').exists():
                # Patients du médecin
                patients_query = Patient.objects.filter(
                    Q(doctor=profile) | Q(appointments__doctor=profile)
                ).distinct()
            else:
                patients_query = Patient.objects.none()
            
            # Filtrer par timestamp si fourni
            if since:
                try:
                    since_date = datetime.fromisoformat(since.replace('Z', '+00:00'))
                    patients_query = patients_query.filter(updated_at__gte=since_date)
                except:
                    pass
            
            patients_data = []
            for patient in patients_query:
                patients_data.append({
                    'id': patient.id,
                    'user_id': patient.user.id,
                    'username': patient.user.username,
                    'email': patient.user.email,
                    'firstname': patient.firstname,
                    'lastname': patient.lastname,
                    'tel': patient.tel,
                    'birth_date': patient.birth_date.isoformat() if patient.birth_date else None,
                    'address': patient.address,
                    'assurance': patient.assurance,
                    'husband_name': patient.husband_name,
                    'husband_tel': patient.husband_tel,
                    'created_at': patient.created_at.isoformat(),
                    'updated_at': patient.updated_at.isoformat(),
                })
            
            return JsonResponse({
                'status': 'success',
                'data': {
                    'patients': patients_data,
                    'count': len(patients_data),
                    'timestamp': timezone.now().isoformat()
                }
            })
            
        elif request.method == 'POST':
            # Upload des modifications depuis le mobile
            data = json.loads(request.body)
            patients_updates = data.get('patients', [])
            
            updated_count = 0
            errors = []
            
            for patient_data in patients_updates:
                try:
                    # Logique de mise à jour/création
                    # À implémenter selon vos besoins
                    updated_count += 1
                except Exception as e:
                    errors.append(f"Patient {patient_data.get('id', 'unknown')}: {str(e)}")
            
            return JsonResponse({
                'status': 'success',
                'data': {
                    'updated_count': updated_count,
                    'errors': errors,
                    'timestamp': timezone.now().isoformat()
                }
            })
            
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Erreur de synchronisation patients: {str(e)}'
        }, status=500)


@csrf_exempt
@mobile_api_cors
@mobile_auth_required
def sync_appointments(request):
    """
    GET /api/mobile/sync/appointments?since=timestamp
    POST /api/mobile/sync/appointments
    Synchronisation des rendez-vous
    """
    try:
        user = request.user
        profile = Profile.objects.get(user=user)
        
        if request.method == 'GET':
            since = request.GET.get('since')
            
            # Filtrer les rendez-vous selon le rôle
            if user.groups.filter(name='admin').exists():
                appointments_query = Appointment.objects.all()
            elif user.groups.filter(name='docteur').exists():
                appointments_query = Appointment.objects.filter(doctor=profile)
            else:
                appointments_query = Appointment.objects.none()
            
            # Filtrer par timestamp
            if since:
                try:
                    since_date = datetime.fromisoformat(since.replace('Z', '+00:00'))
                    appointments_query = appointments_query.filter(updated_at__gte=since_date)
                except:
                    pass
            
            appointments_data = []
            for appointment in appointments_query:
                appointments_data.append({
                    'id': appointment.id,
                    'patient_id': appointment.patient.id,
                    'patient_name': f"{appointment.patient.firstname} {appointment.patient.lastname}",
                    'doctor_id': appointment.doctor.id if appointment.doctor else None,
                    'consul_date': appointment.consul_date.isoformat(),
                    'consul_hour': appointment.consul_hour.strftime('%H:%M'),
                    'appointment_type': appointment.appointment_type,
                    'state': appointment.state,
                    'consul_data': appointment.consul_data,
                    'created_at': appointment.created_at.isoformat(),
                    'updated_at': appointment.updated_at.isoformat(),
                })
            
            return JsonResponse({
                'status': 'success',
                'data': {
                    'appointments': appointments_data,
                    'count': len(appointments_data),
                    'timestamp': timezone.now().isoformat()
                }
            })
            
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Erreur de synchronisation rendez-vous: {str(e)}'
        }, status=500)


@csrf_exempt
@mobile_api_cors
@mobile_auth_required
def sync_pregnancies(request):
    """
    GET /api/mobile/sync/pregnancies?since=timestamp
    POST /api/mobile/sync/pregnancies
    Synchronisation des grossesses
    """
    try:
        user = request.user
        profile = Profile.objects.get(user=user)
        
        if request.method == 'GET':
            since = request.GET.get('since')
            
            # Filtrer les grossesses selon le rôle
            if user.groups.filter(name='admin').exists():
                pregnancies_query = Pregnancy.objects.all()
            elif user.groups.filter(name='docteur').exists():
                pregnancies_query = Pregnancy.objects.filter(
                    patient__appointments__doctor=profile
                ).distinct()
            else:
                pregnancies_query = Pregnancy.objects.none()
            
            # Filtrer par timestamp
            if since:
                try:
                    since_date = datetime.fromisoformat(since.replace('Z', '+00:00'))
                    pregnancies_query = pregnancies_query.filter(updated_at__gte=since_date)
                except:
                    pass
            
            pregnancies_data = []
            for pregnancy in pregnancies_query:
                pregnancies_data.append({
                    'id': pregnancy.id,
                    'patient_id': pregnancy.patient.id,
                    'term': pregnancy.term,
                    'state': pregnancy.state,
                    'start_date': pregnancy.start_date.isoformat() if pregnancy.start_date else None,
                    'end_date': pregnancy.end_date.isoformat() if pregnancy.end_date else None,
                    'created_at': pregnancy.created_at.isoformat(),
                    'updated_at': pregnancy.updated_at.isoformat(),
                })
            
            return JsonResponse({
                'status': 'success',
                'data': {
                    'pregnancies': pregnancies_data,
                    'count': len(pregnancies_data),
                    'timestamp': timezone.now().isoformat()
                }
            })
            
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'Erreur de synchronisation grossesses: {str(e)}'
        }, status=500)


def get_last_modified_timestamp(model_class, user):
    """
    Obtenir le timestamp de la dernière modification pour un modèle
    """
    try:
        latest = model_class.objects.filter(
            # Filtrer selon les permissions utilisateur
        ).order_by('-updated_at').first()
        
        if latest and hasattr(latest, 'updated_at'):
            return latest.updated_at.isoformat()
        return None
    except:
        return None
