import os
from django.shortcuts import redirect, render,get_object_or_404,HttpResponseRedirect
from django.http import JsonResponse
import datetime, wave

# relative import of forms
from ptcapp.models import Language
from django.templatetags.static import static
from django.core.files.storage import FileSystemStorage


def create_view(request):
    if request.method == 'POST':
        name = request.POST['name']
        if request.FILES.get('audio') != None:
            blob = request.FILES['audio']
            pathfic = "static/uploads/"+name.lower()+".wav"
            fic = wave.open("ptcapp/"+pathfic, "wb")
            fic.setnchannels(1)
            fic.setsampwidth(2)
            fic.setframerate(8000)
            fic.setnframes(320)
            fic.writeframes(blob.read())
        language = Language()
        language.name=name
        language.path=pathfic
        language.save()
        # call.apply_async((), eta=datetime.datetime(2022, 6, 10, 10, 38))
        # sms.delay()
        # add.apply_async((6,2,) , eta=datetime.datetime(2022, 5, 26, 14, 20))
        return JsonResponse({'language':int(language.id), 'success' : True})
    return JsonResponse({'language':int(language.id), 'success' : False})

def update_view(request, id):
    language = get_object_or_404(Language, id = id)
    
    if request.method == 'POST':
        name = request.POST['name']
        if request.FILES.get('audio') != None:
            blob = request.FILES['audio']
            pathfic = "static/uploads/"+name.lower()+".wav"
            fic = wave.open("ptcapp/"+pathfic, "wb")
            fic.setnchannels(1)
            fic.setsampwidth(2)
            fic.setframerate(8000)
            fic.setnframes(320)
            fic.writeframes(blob.read())
        language.name=name
        language.path=pathfic
        language.save()
        return JsonResponse({'language':int(language.id), 'success' : True})
    return JsonResponse({'language':int(language.id), 'success' : False})
    

def delete_view(request, id):
    obj = get_object_or_404(Language, id = id)
 
    if request.method =="POST":
        os.remove('ptcapp/'+obj.path)
        obj.delete()
        return JsonResponse({'success' : True})
    return JsonResponse({'language':int(obj.id), 'success' : False})

