{% extends 'doctor/layout.html' %} 
{% load static %}
{% load layout %}

{% block up-style %}
    <!-- DataTables -->
    <link href="{% static 'libs/select2/css/select2.min.css' %}" rel="stylesheet" type="text/css" />
    <link href="{% static 'libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css' %}" rel="stylesheet" type="text/css" />
    <style>
        .swal2-content{
            color:white !important;
        }
        .action a{
            margin: 0 10px;
        }
    </style>
{% endblock up-style %}
{% block action_button %}
<a href="javascript:;" id="addAppointment" 
data-bs-toggle="modal" 
data-bs-target=".bs-example-modal-center"  class="btn btn-primary mx-3">Ajouter une consultation</a>
{% endblock action_button %}
{% block content %}
{% csrf_token %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white text-left">
                    <span class="h2 text-capitalize">Filtre sur les consultations</span>
                </div>
                <div class="card-body">
                    <form action="{% url 'doctor.appointmentfilters' %}" method="get">
                        
                        <div class="row">
                            <div class="col-md-3 col-sm-12">
                                <label for="fullname" class="form-label">Patient(e)</label>
                                <input class="form-control" type="text" name="fullname" placeholder="Nom & Prénoms" id="fullname">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="statut" class="form-label">Statut</label>
                                <select class="form-control select2" name="statut" id="statut">
                                    <option value="all">Tout</option>
                                    <option value="pending" selected>En attente</option>
                                    <option value="canceled">Annulée</option>
                                    <option value="done">Effectué</option>
                                    <option value="missed">Manqué</option>
                                </select>
                            </div>
                            
                            <div class="col-md-3 col-sm-12">
                                <label for="date_appointment" class="form-label">Date de la Consultaion</label>
                                <input class="form-control" type="date" name="date_appointment" placeholder="Date de consultation" id="date_appointment">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="" class="form-label text-white">Appliquer</label>
                                <input class="form-control btn btn-success" type="submit" value="Appliquer">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-center">
                    <span class="h2 text-capitalize text-white"> liste des Consultations</span>
                </div>
                <div class="card-body">
                    <table
                        id="datatable"
                        class="table table-striped dt-responsive nowrap"
                        style="border-collapse: collapse; border-spacing: 0; width: 100%"
                        >
                        <thead>
                            <tr>
                                <th>Patient(e)</th>
                                <th>Statut</th>
                                <th>Type</th>
                                <th>Date de la consultation</th>
                                <th>Heure de la consultation</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        <tbody>
                            {% for appointment in appointments %}
                                <tr>
                                    <td>{{appointment.patient.user|auth_fullname}}</td>
                                    <td>
                                        {% if appointment.state == 'pending' %}
                                        <span class="bg-warning rounded-3 p-2 text-white">En attente</span>
                                        {% endif %}
                                        {% if appointment.state == 'missed' %}
                                        <span class="bg-danger rounded-3 p-2 text-white">Manquée</span>
                                        {% endif %}
                                        {% if appointment.state == 'canceled' %}
                                        <span class="bg-info rounded-3 p-2 text-white">Annulée</span>
                                        {% endif %}
                                        {% if appointment.state == 'done' %}
                                        <span class="bg-success rounded-3 p-2 text-white">Effectuée</span>
                                        {% endif %}
                                    </td>
                                    <td>{{appointment.appointment_type}}</td>
                                    <td>{{appointment.consul_date|date:"Y-m-d"}}</td>
                                    <td>{{appointment.consul_hour|date:"H : i"}}</td>
                                    <td class="action">
                                        <a href="{% url 'doctor.appointment.show' id=appointment.id %}" data-toggle="tooltip" data-placement="right" title="Détails"><i class="ri-eye-line" ></i></a>
                                        <a href="{% url 'doctor.appointment.edit' id=appointment.id %}" data-toggle="tooltip" data-placement="right" title="Modifier"><i class=" ri-pencil-fill" ></i></a>
                                        {% comment %} <a href="#" class="delete-appointment" data-appointment-id="{{appointment.pk}}" data-toggle="tooltip" data-placement="right" title="Supprimer"><i class=" ri-delete-bin-fill" ></i></a> {% endcomment %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade bs-example-modal-center" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-title">Créer une consultation</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="#" method="post">
                        {% csrf_token %}
                        {% comment %} <div class="row">
                            <div class="d-flex  justify-content-end">
                                <div class="form-check mx-2">
                                    <label for="current_doc" class="form-check-label required">Dossier actuel</label>
                                    <input class="form-check-input" type="radio" name="group" value="current_doc" id="current_doc" checked>
                                </div>
                                <div class="form-check mx-2">
                                    <label for="new_doc" class="form-check-label required">Nouveau Dossier</label>
                                    <input class="form-check-input" type="radio" name="group" value="new_doc" id="new_doc">
                                </div>
                            </div>
                        </div> {% endcomment %}
                        <div class="row">
                            <div class="col-md-12 col-sm-12 mt-4">
                                {% comment %} <fieldset class="h-100"> {% endcomment %}
                                    {% comment %} <legend>Informations Générales</legend> {% endcomment %}
                                    <input type="hidden" name="_method" id="method" value="post">
                                    {% comment %} <div class="my-3">
                                        <label for="num_record" class="form-label required">Numéro du dossier</label>
                                        <input class="form-control" type="text" name="num_record" value="DOS-47852" id="num_record" required="required" disabled>
                                        <input class="form-control" type="hidden" name="current_num_record" value="DOS-47852" id="current_num_record" required="required">
                                    </div> {% endcomment %}
                                    <div class="my-3">
                                        <label for="appointment_patient" class="form-label required">Sélectionner patient</label>
                                        <select class="select2 form-control" name="appointment_patient" id="appointment_patient" required="required">*
                                            {% for patient in patients %}
                                            {% if patient %}
                                            <option value="{{patient.id}}">{{patient.user|auth_fullname}}</option>
                                            {% endif %}
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="my-3">
                                        <label for="type_appointment" class="form-label required">Type de consultation</label>
                                        {% comment %} <a href="#" style="font-size:20px"><i class="ri-add-box-fill"></i></a> {% endcomment %}
                                        <input class="form-control" type="text" name="type_appointment" placeholder="Type de consultation" id="type_appointment" required="required">
                                    </div>
                                    {% comment %} <div class="my-3">
                                        <label for="illness" class="form-label required">Maux ressentis</label>
                                        <input class="form-control" type="text" name="illness" placeholder="Maux ressentis" id="illness" required="required">
                                    </div> {% endcomment %}
                                {% comment %} </fieldset> {% endcomment %}
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <label for="consul_date" class="form-label required">Date de la Consultaion</label>
                                <input class="form-control" type="date" name="consul_date" min="{{date|date:"Y-m-d"}}" placeholder="Date d'inscriptation" id="consul_date" required>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <label for="consul_time" class="form-label required">Heure de la Consultaion</label>
                                <input class="form-control" type="time" name="consul_time" placeholder="Date d'inscriptation" id="consul_time" required>
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col">
                                <input class="btn btn-success w-auto" id="form-submit" type="submit" value="Sauvegarder">
                            </div>
                        </div>
                    </form>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->

{% endblock content %}

{% block down-script %}
    <script src="//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json"></script>
    <script src="{% static 'libs/select2/js/select2.min.js'%}"></script>
    <!-- Required datatable js -->
    <script src="{% static 'libs/datatables.net/js/jquery.dataTables.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js'%}"></script>
    <!-- Responsive examples -->
    <script src="{% static 'libs/datatables.net-responsive/js/dataTables.responsive.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js'%}"></script>
    <!-- Datatable init js -->
    {% comment %} <script src="{% static 'js/pages/datatables.init.js' %}"></script> {% endcomment %}
    <script>

        {% for msg in messages %}
                Swal.fire({
                    position: "top-end",
                    text: "{{msg}}",
                    showConfirmButton: !1,
                    timer: 1000,
                    background:"rgba(63,255,106,0.69)"
                });
            {% endfor %} 
        var dt = $("#datatable").DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
            },
            "columnDefs": [ {
                "targets": [4],
                "orderable": false
            }],
            order : [[3, 'desc'], [4, 'desc']],
            drawCallback: function() {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        $('.select2').select2()
        $(".delete-appointment").click(function() {
            Swal.fire({
                title: "Voulez-vous vraiment supprimer cet utilisateur ?",
                text: "Cet utilisateur sera définitivement supprimer !",
                icon: "warning",
                showCancelButton: !0,
                confirmButtonColor: "#1cbb8c",
                cancelButtonColor: "#ff3d60",
                confirmButtonText: "Oui, supprimer!",
                cancelButtonText: "Annuler",
            }).then(function(t) {
                t.value &&
                    Swal.fire("Supprimer!", "", "success");
            });
        })

        $('#new_doc').click(function(){
            $('#num_record').val('DOS-')
            $('#num_record').attr('disabled', false)
        })

        $('#current_doc').click(function(){
            $('#num_record').val($('#current_num_record').val())
            $('#num_record').attr('disabled', true)
        })


        ('#form-submit').click(function(e){
            e.preventDefault()
            $.post(
                "{% url 'appointment.create' %}",
                {
                    csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val(),
                    appointment_patient : $('#appointment_patient').val(),
                    appointment_type : $('#type_appointment').val(),
                    appointment_date : $('#consul_date').val(),
                    appointment_hour : $('#consul_time').val()
                },
                function(response){
                    if(response.success){
                        $('.bs-example-modal-center').modal('toggle');
                        if($('#method').val() == 'put'){
                            Swal.fire("Mis à jour!", "", "success");
                        }else if($('#method').val() == 'post'){
                            Swal.fire("Création!", "", "success");
                        }
                        window.location.reload()
                    }
                }
            );
        })
    </script>
{% endblock down-script %}