"""
Paramètres Django pour la production Heroku
"""

import os
from .settings import *

# Import conditionnel de dj_database_url
try:
    import dj_database_url
    HAS_DJ_DATABASE_URL = True
except ImportError:
    HAS_DJ_DATABASE_URL = False
    # En développement, on peut ignorer cette dépendance

# SÉCURITÉ
DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
# Configuration dynamique des ALLOWED_HOSTS
ALLOWED_HOSTS = os.environ.get('DJANGO_ALLOWED_HOSTS', '').split(',') if os.environ.get('DJANGO_ALLOWED_HOSTS') else [
    '.herokuapp.com',
    'ptccare-web.herokuapp.com',
    'ptccare-web-ae382d4ad8cc.herokuapp.com',  # Votre domaine Heroku actuel
    'localhost',
    '127.0.0.1'
]

# Nettoyer les espaces vides
ALLOWED_HOSTS = [host.strip() for host in ALLOWED_HOSTS if host.strip()]

# Ajouter le domaine Heroku automatiquement si disponible
heroku_app_name = os.environ.get('HEROKU_APP_NAME')
if heroku_app_name:
    ALLOWED_HOSTS.append(f'{heroku_app_name}.herokuapp.com')

# En développement, permettre tous les hosts
if DEBUG:
    ALLOWED_HOSTS.append('*')

# Clé secrète depuis les variables d'environnement
SECRET_KEY = os.environ.get('SECRET_KEY', 'votre-cle-secrete-par-defaut')

# Base de données PostgreSQL pour Heroku
if HAS_DJ_DATABASE_URL and os.environ.get('DATABASE_URL'):
    DATABASES = {
        'default': dj_database_url.config(
            default=os.environ.get('DATABASE_URL'),
            conn_max_age=600,
            conn_health_checks=True,
        )
    }
else:
    # Configuration de fallback pour le développement local
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'NAME': 'ptccare',
            'USER': 'root',
            'PASSWORD': '',
            'HOST': '127.0.0.1',
            'PORT': '3306',
            'OPTIONS': {
                'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            },
        }
    }

# Configuration Redis pour Celery (Heroku Redis)
REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379')
CELERY_BROKER_URL = REDIS_URL
CELERY_RESULT_BACKEND = REDIS_URL

# Fichiers statiques avec WhiteNoise (seulement si disponible)
try:
    import whitenoise
    MIDDLEWARE.insert(1, 'whitenoise.middleware.WhiteNoiseMiddleware')
    # Utiliser le stockage simple au lieu du compressé pour éviter les erreurs de manifeste
    STATICFILES_STORAGE = 'whitenoise.storage.CompressedStaticFilesStorage'
except ImportError:
    # En développement, on peut ignorer WhiteNoise
    STATICFILES_STORAGE = 'django.contrib.staticfiles.storage.StaticFilesStorage'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATIC_URL = '/static/'

# Répertoires de fichiers statiques
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'ptcapp', 'static'),
]

# Collecte des fichiers statiques plus permissive
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# Configuration email pour production
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', '587'))
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', '<EMAIL>')

# Configuration CORS pour production
CORS_ALLOWED_ORIGINS = [
    "https://ptccare.herokuapp.com",  # Votre domaine Heroku
    "https://www.ptccare.com",        # Votre domaine personnalisé si vous en avez un
]
CORS_ALLOW_ALL_ORIGINS = False  # Sécurisé pour la production

# Logging pour Heroku
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose'
        },
    },
    'root': {
        'handlers': ['console'],
        'level': 'INFO',
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
        'ptcapp': {
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}

# Sécurité HTTPS
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_SSL_REDIRECT = True
SESSION_COOKIE_SECURE = True
CSRF_COOKIE_SECURE = True

# Configuration des médias pour Heroku (utiliser un service cloud)
# Pour la production, utilisez AWS S3, Google Cloud Storage, etc.
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Configuration Firebase (variables d'environnement)
FIREBASE_CONFIG = {
    'type': os.environ.get('FIREBASE_TYPE'),
    'project_id': os.environ.get('FIREBASE_PROJECT_ID'),
    'private_key_id': os.environ.get('FIREBASE_PRIVATE_KEY_ID'),
    'private_key': os.environ.get('FIREBASE_PRIVATE_KEY', '').replace('\\n', '\n'),
    'client_email': os.environ.get('FIREBASE_CLIENT_EMAIL'),
    'client_id': os.environ.get('FIREBASE_CLIENT_ID'),
    'auth_uri': os.environ.get('FIREBASE_AUTH_URI'),
    'token_uri': os.environ.get('FIREBASE_TOKEN_URI'),
    'auth_provider_x509_cert_url': os.environ.get('FIREBASE_AUTH_PROVIDER_X509_CERT_URL'),
    'client_x509_cert_url': os.environ.get('FIREBASE_CLIENT_X509_CERT_URL'),
}

# Désactiver les fonctionnalités SMS en production si pas configurées
SMS_ENABLED = os.environ.get('SMS_ENABLED', 'False').lower() == 'true'
GSM_MODEM_PORT = os.environ.get('GSM_MODEM_PORT', None)

# Désactiver reCAPTCHA en production pour éviter les erreurs de domaine
RECAPTCHA_ENABLED = False
RECAPTCHA_PUBLIC_KEY = ''
RECAPTCHA_PRIVATE_KEY = ''

# Configuration du domaine pour les emails et URLs
SITE_DOMAIN = os.environ.get('SITE_DOMAIN', 'ptccare-web-ae382d4ad8cc.herokuapp.com')
SITE_URL = f'https://{SITE_DOMAIN}'

# Configuration pour les emails avec URLs absolues
USE_TZ = True
SITE_ID = 1

# URL de base pour les emails (remplace localhost)
PTCCARE_BASE_URL = os.environ.get('PTCCARE_BASE_URL', f'https://{SITE_DOMAIN}')
