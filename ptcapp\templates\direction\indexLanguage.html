{% extends 'direction/layout.html' %} 
{% load static %}

{% block up-style %}
    <!-- DataTables -->
    <link href="{% static 'libs/select2/css/select2.min.css' %}" rel="stylesheet" type="text/css" />
    <link href="{% static 'libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css' %}" rel="stylesheet" type="text/css" />
    <style>
        label.required::after{
            content: "*";
            color: red;
            padding-left: 3px
        }

        audio{
            width:100%;
        }

        #datatable tbody tr{
            vertical-align: middle !important;
        }
        .action a{
            margin: 0 10px;
        }
    </style>
{% endblock up-style %} 
{% block action_button %}
<a href="javascript:;" id="addLanguage" 
data-bs-toggle="modal" 
data-bs-target=".bs-example-modal-center" class="btn btn-primary mx-3">Nouvelle Langue</a>
{% endblock action_button %}
{% block content %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-center">
                    <span class="h2 text-capitalize text-white"> Liste des Langues</span>
                </div>
                <div class="card-body">
                    <table
                        id="datatable"
                        class="table table-striped dt-responsive nowrap"
                        style="border-collapse: collapse; border-spacing: 0; width: 100%"
                        >
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Nombre d'utilisateurs</th>
                                <th>Message Audio</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        <tbody>
                            {% for language in languages %}
                            <tr>
                                <td>{{language.name}}</td>
                                
                                <td>{{language.profile_set.all|length}}</td>
                                <td>
                                    <audio src="/{{language.path}}" controls></audio>
                                </td>
                                <td class="action">
                                    <a href="javascript:;" 
                                    class="edit-language"
                                    data-language-id="{{language.pk}}" 
                                    data-language-name="{{language.name}}" 
                                    data-language-path="/{{language.path}}" 
                                    data-bs-toggle="modal" 
                                    data-bs-target=".bs-example-modal-center"
                                    data-toggle="tooltip" data-placement="right" title="Modifier"><i class="ri-pencil-fill" ></i></a>
                                    <a href="javascript:;" class="delete-language" data-language-id="{{language.pk}}" data-toggle="tooltip" data-placement="right" title="Supprimer"><i class=" ri-delete-bin-fill" ></i></a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade bs-example-modal-center" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-title">Créer langue</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="#" method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-12 col-sm-12 mt-4">
                                {% comment %} <fieldset class="h-100"> {% endcomment %}
                                    {% comment %} <legend>Informations Générales</legend> {% endcomment %}
                                    <input type="hidden" name="_method" id="method" value="post">
                                    <input type="hidden" name="language_id" id="language_id" value="">
                                    <div class="my-3">
                                        <label for="name" class="form-label required">Nom</label>
                                        <input class="form-control" type="text" name="name" placeholder="Nom" id="name" required="required">
                                    </div>
                                    <div id="controls">
                                        <button id="recordButton"class="btn btn-sm btn-primary" ><i class="ri-play-mini-fill"></i></button>
                                        {% comment %} <button id="pauseButton" disabled>Pause</button> {% endcomment %}
                                        <button id="stopButton" class="btn btn-sm btn-primary" disabled><i class="ri-stop-mini-fill"></i></button>
                                    </div>
                                    <p><strong>Enregistrement:</strong></p>
  	                                <ul id="recordingsList" style="list-style-type: none; padding: 0 !important; margin:0 !important"></ul>
                                    
                                {% comment %} </fieldset> {% endcomment %}
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col">
                                <input class="btn btn-success w-auto" id="form-submit" type="submit" value="Sauvegarder">
                            </div>
                        </div>
                    </form>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
{% endblock content %}

{% block down-script %}
    <script src="//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json"></script>
    <script src="{% static 'libs/select2/js/select2.min.js'%}"></script>
    <!-- Required datatable js -->
    <script src="{% static 'libs/datatables.net/js/jquery.dataTables.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js'%}"></script>
    <!-- Responsive examples -->
    <script src="{% static 'libs/datatables.net-responsive/js/dataTables.responsive.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js'%}"></script>
    <!-- Datatable init js -->
    <script src="{% static 'libs/recorder/recorder.min.js'%}"></script>
    <script src="{% static 'libs/recorder/recorder-app.js'%}"></script>
    {% comment %} <script src="{% static 'js/pages/datatables.init.js' %}"></script> {% endcomment %}
    <script>
        var dt = $("#datatable").DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
            },
            "columnDefs": [ {
                "targets": [2],
                "orderable": false
            }],
            order : [[0, 'asc']],
            drawCallback: function() {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        $('.select2').select2()
        
        $('#addlanguage').click(function(){
            $('#modal-title').text('Créer Langue')
            $('#method').val('post')
            $('#name').val('')
            
        })

        $('.edit-language').click(function(){
            $("#newaudio").remove()
            $('#modal-title').text('Editer Langue')
            $('#method').val('put')
            $('#language_id').val($(this).data('languageId'))
            $('#name').val($(this).data('languageName'))
            $('#recordingsList').append('<audio id="newaudio" src="'+$(this).data('languagePath')+'" controls></audio>')
        })

        const sendBlob = async function(blobURL, url) {
            let blob = await fetch(blobURL).then(r => r.blob());
            
            var form = new FormData();
            form.append('csrfmiddlewaretoken', $('input[name="csrfmiddlewaretoken"]').val())
            form.append('name', $('#name').val())
            form.append('audio', blob)
            console.log(blob)
            $.ajax({
                url: url,
                type: 'POST',
                data: form,
                processData: false,
                contentType: false,
                success: function (response) {
                    if(response.success){
                        $('.bs-example-modal-center').modal('toggle');
                        if($('#method').val() == 'put'){
                            Swal.fire("Mis à jour!", "", "success");
                        }else if($('#method').val() == 'post'){
                            Swal.fire("Création!", "", "success");
                        }
                        window.location.reload()
                    }
                },
                error: function () {
                   // handle error case here
                }
            });
          }

        $('#form-submit').click(function(e){
            e.preventDefault()
            if($('#method').val() == 'put'){
                var url = "/language/update/"+$('#language_id').val()
            }else if($('#method').val() == 'post'){
                var url = "{% url 'language.create' %}"
            }
            sendBlob($("#newaudio").prop('src'), url)
        })
        
        $(".delete-language").click(function() {
            id = $(this).data('languageId')
            Swal.fire({
                title: "Voulez-vous vraiment supprimer cette langue ?",
                text: "Cette langue sera définitivement supprimer !",
                icon: "warning",
                showCancelButton: !0,
                confirmButtonColor: "#1cbb8c",
                cancelButtonColor: "#ff3d60",
                confirmButtonText: "Oui, supprimer!",
                cancelButtonText: "Annuler",
            }).then(function(t) {
                if(t.value){
                    $.post(
                        "/language/delete/"+id,
                        {
                            csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val(),
                        },
                        function(response){
                            if(response.success){
                                Swal.fire("Supprimer!", "", "success");
                                window.location.reload()
                            }
                        }
                    );
                }
            });
        })
    </script>
{% endblock down-script %}