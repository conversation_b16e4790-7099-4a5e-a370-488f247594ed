#!/usr/bin/env python
"""
Script pour remplir la base de données PTC Care avec des données de test
"""
import os
import sys
from datetime import datetime, date, timedelta

# Ajouter le répertoire du projet au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configuration Django - utiliser les paramètres de production sur Heroku
if 'DYNO' in os.environ:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ptccare.settings_production')
else:
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ptccare.settings')

import django
django.setup()

# Imports des modèles après la configuration Django
from django.contrib.auth.models import User, Group
from django.utils import timezone
from ptcapp.models import (
    Profile, Hospital, Service, Speciality, Language,
    Pregnancy, Appointment, Record, MotherPregnancy,
    MapDoctorPatient, MapMotherChild, MapPregnancyChild,
    OtherInformation, PasswordResetToken, UserPasswordStatus
)

def create_groups():
    """Créer les groupes d'utilisateurs"""
    print("🔧 Création des groupes d'utilisateurs...")
    groups = ['admin', 'docteur', 'assistant', 'patient']
    for group_name in groups:
        group, created = Group.objects.get_or_create(name=group_name)
        if created:
            print(f"   ✅ Groupe '{group_name}' créé")
        else:
            print(f"   ℹ️  Groupe '{group_name}' existe déjà")

def create_hospitals():
    """Créer des hôpitaux de test"""
    print("🏥 Création des hôpitaux...")
    hospitals_data = [
        {
            'name': 'Centre Hospitalier Départemental (CHD)',
            'address': 'Avenue Jean-Paul II, Cotonou, Bénin',
            'tel': '+229 21 30 01 00'
        },
        {
            'name': 'Hôpital de Zone de Calavi',
            'address': 'Calavi, Littoral, Bénin',
            'tel': '+229 21 35 02 15'
        },
        {
            'name': 'Centre de Santé Communal de Godomey',
            'address': 'Godomey, Abomey-Calavi, Bénin',
            'tel': '+229 21 35 03 20'
        }
    ]
    
    hospitals = []
    for hospital_data in hospitals_data:
        hospital, created = Hospital.objects.get_or_create(
            name=hospital_data['name'],
            defaults=hospital_data
        )
        hospitals.append(hospital)
        if created:
            print(f"   ✅ Hôpital '{hospital.name}' créé")
        else:
            print(f"   ℹ️  Hôpital '{hospital.name}' existe déjà")
    
    return hospitals

def create_services(hospitals):
    """Créer des services médicaux"""
    print("🏢 Création des services...")
    services_data = [
        'Gynécologie-Obstétrique',
        'Pédiatrie',
        'Médecine Générale',
        'Sage-femme',
        'Infirmerie',
        'Laboratoire',
        'Radiologie'
    ]

    services = []
    for i, service_name in enumerate(services_data):
        # Répartir les services entre les hôpitaux
        hospital = hospitals[i % len(hospitals)]

        service, created = Service.objects.get_or_create(
            name=service_name,
            hospital=hospital,
            defaults={'description': f'Service de {service_name}'}
        )
        services.append(service)
        if created:
            print(f"   ✅ Service '{service.name}' créé à {hospital.name}")
        else:
            print(f"   ℹ️  Service '{service.name}' existe déjà")

    return services

def create_specialities():
    """Créer des spécialités médicales"""
    print("👨‍⚕️ Création des spécialités...")
    specialities_data = [
        'Gynécologue-Obstétricien',
        'Pédiatre',
        'Médecin Généraliste',
        'Sage-femme',
        'Infirmier(ère)',
        'Technicien de Laboratoire',
        'Radiologue'
    ]
    
    specialities = []
    for spec_name in specialities_data:
        speciality, created = Speciality.objects.get_or_create(
            name=spec_name,
            defaults={'description': f'Spécialité en {spec_name}'}
        )
        specialities.append(speciality)
        if created:
            print(f"   ✅ Spécialité '{speciality.name}' créée")
        else:
            print(f"   ℹ️  Spécialité '{speciality.name}' existe déjà")
    
    return specialities

def create_languages():
    """Créer des langues"""
    print("🌍 Création des langues...")
    languages_data = [
        {'name': 'Français', 'path': 'fr'},
        {'name': 'Fon', 'path': 'fon'},
        {'name': 'Yoruba', 'path': 'yo'},
        {'name': 'Anglais', 'path': 'en'}
    ]
    
    languages = []
    for lang_data in languages_data:
        language, created = Language.objects.get_or_create(
            name=lang_data['name'],
            defaults=lang_data
        )
        languages.append(language)
        if created:
            print(f"   ✅ Langue '{language.name}' créée")
        else:
            print(f"   ℹ️  Langue '{language.name}' existe déjà")
    
    return languages

def create_admin_user(hospitals, services, specialities, languages):
    """Créer un utilisateur administrateur"""
    print("👑 Création de l'utilisateur administrateur...")
    
    # Créer l'utilisateur admin
    admin_user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'email': '<EMAIL>',
            'first_name': 'Administrateur',
            'last_name': 'PTC Care',
            'is_staff': True,
            'is_superuser': True
        }
    )
    
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
        print(f"   ✅ Utilisateur admin créé (username: admin, password: admin123)")
    else:
        print(f"   ℹ️  Utilisateur admin existe déjà")
    
    # Ajouter au groupe admin
    admin_group = Group.objects.get(name='admin')
    admin_user.groups.add(admin_group)
    
    # Créer le profil admin
    admin_profile, created = Profile.objects.get_or_create(
        user=admin_user,
        defaults={
            'firstname': 'Administrateur',
            'lastname': 'PTC Care',
            'tel': '+229 97 00 00 00',
            'sexe': 'M',
            'birth_date': date(1980, 1, 1),
            'address': 'Cotonou, Bénin',
            'language': languages[0],  # Français
            'hospital': hospitals[0],  # CHD
            'service': services[0],    # Gynécologie
            'speciality': specialities[0],  # Gynécologue
            'occupation': 'Administrateur Système'
        }
    )
    
    if created:
        print(f"   ✅ Profil administrateur créé")
    else:
        print(f"   ℹ️  Profil administrateur existe déjà")
    
    return admin_user, admin_profile

def create_doctors(hospitals, services, specialities, languages):
    """Créer des médecins de test"""
    print("👨‍⚕️ Création des médecins...")
    
    doctors_data = [
        {
            'username': 'DOC-12345678',
            'email': '<EMAIL>',
            'first_name': 'Dr. Jean',
            'last_name': 'KOUASSI',
            'profile': {
                'firstname': 'Jean',
                'lastname': 'KOUASSI',
                'tel': '+229 97 11 11 11',
                'sexe': 'M',
                'birth_date': date(1975, 5, 15),
                'address': 'Quartier Fidjrossè, Cotonou',
                'language': languages[0],
                'hospital': hospitals[0],
                'service': services[0],  # Gynécologie
                'speciality': specialities[0],  # Gynécologue
                'occupation': 'Gynécologue-Obstétricien'
            }
        },
        {
            'username': 'DOC-87654321',
            'email': '<EMAIL>',
            'first_name': 'Dr. Marie',
            'last_name': 'ADJOVI',
            'profile': {
                'firstname': 'Marie',
                'lastname': 'ADJOVI',
                'tel': '+229 97 22 22 22',
                'sexe': 'F',
                'birth_date': date(1982, 8, 20),
                'address': 'Quartier Akpakpa, Cotonou',
                'language': languages[0],
                'hospital': hospitals[1],
                'service': services[1],  # Pédiatrie
                'speciality': specialities[1],  # Pédiatre
                'occupation': 'Pédiatre'
            }
        }
    ]
    
    doctors = []
    for doctor_data in doctors_data:
        # Créer l'utilisateur
        user, created = User.objects.get_or_create(
            username=doctor_data['username'],
            defaults={
                'email': doctor_data['email'],
                'first_name': doctor_data['first_name'],
                'last_name': doctor_data['last_name']
            }
        )
        
        if created:
            user.set_password('doctor123')
            user.save()
            print(f"   ✅ Médecin {user.username} créé")
        else:
            print(f"   ℹ️  Médecin {user.username} existe déjà")
        
        # Ajouter au groupe docteur
        doctor_group = Group.objects.get(name='docteur')
        user.groups.add(doctor_group)
        
        # Créer le profil
        profile, created = Profile.objects.get_or_create(
            user=user,
            defaults=doctor_data['profile']
        )
        
        doctors.append((user, profile))
    
    return doctors

def create_assistants(hospitals, services, specialities, languages):
    """Créer des assistants de test"""
    print("👩‍⚕️ Création des assistants...")
    
    assistants_data = [
        {
            'username': 'ASS-11111111',
            'email': '<EMAIL>',
            'first_name': 'Mme. Françoise',
            'last_name': 'KOKO',
            'profile': {
                'firstname': 'Françoise',
                'lastname': 'KOKO',
                'tel': '+229 97 33 33 33',
                'sexe': 'F',
                'birth_date': date(1985, 3, 10),
                'address': 'Quartier Dantokpa, Cotonou',
                'language': languages[0],
                'hospital': hospitals[0],
                'service': services[3],  # Sage-femme
                'speciality': specialities[3],  # Sage-femme
                'occupation': 'Sage-femme'
            }
        }
    ]
    
    assistants = []
    for assistant_data in assistants_data:
        # Créer l'utilisateur
        user, created = User.objects.get_or_create(
            username=assistant_data['username'],
            defaults={
                'email': assistant_data['email'],
                'first_name': assistant_data['first_name'],
                'last_name': assistant_data['last_name']
            }
        )
        
        if created:
            user.set_password('assistant123')
            user.save()
            print(f"   ✅ Assistant {user.username} créé")
        else:
            print(f"   ℹ️  Assistant {user.username} existe déjà")
        
        # Ajouter au groupe assistant
        assistant_group = Group.objects.get(name='assistant')
        user.groups.add(assistant_group)
        
        # Créer le profil
        profile, created = Profile.objects.get_or_create(
            user=user,
            defaults=assistant_data['profile']
        )
        
        assistants.append((user, profile))
    
    return assistants

def create_patients(hospitals, languages, doctors, assistants):
    """Créer des patientes de test"""
    print("🤰 Création des patientes...")

    patients_data = [
        {
            'username': 'PAT-20240001',
            'email': '<EMAIL>',
            'first_name': 'Akoua',
            'last_name': 'MENSAH',
            'profile': {
                'firstname': 'Akoua',
                'lastname': 'MENSAH',
                'tel': '+229 97 44 44 44',
                'sexe': 'F',
                'birth_date': date(1995, 6, 15),
                'address': 'Quartier Gbégamey, Cotonou',
                'language': languages[0],
                'hospital': hospitals[0],
                'occupation': 'Commerçante',
                'assurance': 'CNSS',
                'husband_name': 'Koffi MENSAH',
                'husband_tel': '+229 97 55 55 55',
                'study_level': 'Secondaire',
                'child': False,
                'assistant': assistants[0][0] if assistants else None,
                'created_by': doctors[0][0] if doctors else None
            }
        },
        {
            'username': 'PAT-20240002',
            'email': '<EMAIL>',
            'first_name': 'Fatou',
            'last_name': 'IBRAHIM',
            'profile': {
                'firstname': 'Fatou',
                'lastname': 'IBRAHIM',
                'tel': '+229 97 66 66 66',
                'sexe': 'F',
                'birth_date': date(1992, 9, 22),
                'address': 'Quartier Zongo, Cotonou',
                'language': languages[1],  # Fon
                'hospital': hospitals[1],
                'occupation': 'Couturière',
                'assurance': 'RAMU',
                'husband_name': 'Moussa IBRAHIM',
                'husband_tel': '+229 97 77 77 77',
                'study_level': 'Primaire',
                'child': False,
                'assistant': assistants[0][0] if assistants else None,
                'created_by': doctors[1][0] if len(doctors) > 1 else doctors[0][0]
            }
        },
        {
            'username': 'PAT-20240003',
            'email': '<EMAIL>',
            'first_name': 'Grace',
            'last_name': 'TOGNON',
            'profile': {
                'firstname': 'Grace',
                'lastname': 'TOGNON',
                'tel': '+229 97 88 88 88',
                'sexe': 'F',
                'birth_date': date(1998, 12, 5),
                'address': 'Quartier Vedoko, Cotonou',
                'language': languages[0],
                'hospital': hospitals[0],
                'occupation': 'Étudiante',
                'study_level': 'Universitaire',
                'child': False,
                'assistant': assistants[0][0] if assistants else None,
                'created_by': doctors[0][0] if doctors else None
            }
        }
    ]

    patients = []
    for patient_data in patients_data:
        # Créer l'utilisateur
        user, created = User.objects.get_or_create(
            username=patient_data['username'],
            defaults={
                'email': patient_data['email'],
                'first_name': patient_data['first_name'],
                'last_name': patient_data['last_name']
            }
        )

        if created:
            user.set_password('patient123')
            user.save()
            print(f"   ✅ Patiente {user.username} créée")
        else:
            print(f"   ℹ️  Patiente {user.username} existe déjà")

        # Ajouter au groupe patient
        patient_group = Group.objects.get(name='patient')
        user.groups.add(patient_group)

        # Créer le profil
        profile, created = Profile.objects.get_or_create(
            user=user,
            defaults=patient_data['profile']
        )

        patients.append((user, profile))

    return patients

def create_pregnancies(patients):
    """Créer des grossesses de test"""
    print("🤱 Création des grossesses...")

    pregnancies_data = [
        {
            'state': 'En cours',
            'situation': 'Grossesse normale',
            'description': 'Grossesse de 28 semaines, évolution normale',
            'term': '28 SA',
            'start_date': date.today() - timedelta(weeks=28),
            'mother_state': 'Bonne santé',
            'children_state': 'Développement normal',
            'child_number': '1'
        },
        {
            'state': 'En cours',
            'situation': 'Grossesse gémellaire',
            'description': 'Grossesse gémellaire de 24 semaines',
            'term': '24 SA',
            'start_date': date.today() - timedelta(weeks=24),
            'mother_state': 'Surveillance renforcée',
            'children_state': 'Développement normal',
            'child_number': '2'
        },
        {
            'state': 'Terminée',
            'situation': 'Accouchement normal',
            'description': 'Accouchement à terme, bébé en bonne santé',
            'term': '40 SA',
            'start_date': date.today() - timedelta(weeks=42),
            'end_date': timezone.now() - timedelta(weeks=2),
            'mother_state': 'Post-partum normal',
            'children_state': 'Nouveau-né en bonne santé',
            'child_number': '1'
        }
    ]

    pregnancies = []
    for i, pregnancy_data in enumerate(pregnancies_data):
        if i < len(patients):
            pregnancy, created = Pregnancy.objects.get_or_create(
                defaults=pregnancy_data
            )
            pregnancies.append(pregnancy)

            if created:
                print(f"   ✅ Grossesse créée pour {patients[i][1].firstname}")

                # Créer la relation mère-grossesse
                mother_pregnancy, created = MotherPregnancy.objects.get_or_create(
                    mother=patients[i][1],
                    pregnancy=pregnancy
                )
            else:
                print(f"   ℹ️  Grossesse existe déjà")

    return pregnancies

def create_appointments(patients, doctors):
    """Créer des rendez-vous de test"""
    print("📅 Création des rendez-vous...")

    appointments_data = [
        {
            'consul_date': date.today() + timedelta(days=7),
            'consul_hour': '09:00',
            'appointment_type': 'Consultation prénatale',
            'state': 'EN ATTENTE',
            'consul_data': 'Contrôle de routine, échographie',
            'patient': patients[0][1] if patients else None,
            'doctor': doctors[0][1] if doctors else None
        },
        {
            'consul_date': date.today() + timedelta(days=14),
            'consul_hour': '14:30',
            'appointment_type': 'Suivi grossesse gémellaire',
            'state': 'EN ATTENTE',
            'consul_data': 'Surveillance spécialisée grossesse multiple',
            'patient': patients[1][1] if len(patients) > 1 else None,
            'doctor': doctors[0][1] if doctors else None
        },
        {
            'consul_date': date.today() - timedelta(days=3),
            'consul_hour': '10:15',
            'appointment_type': 'Consultation post-natale',
            'state': 'EFFECTUEE',
            'consul_data': 'Contrôle post-accouchement',
            'consul_resume': 'Mère et enfant en bonne santé',
            'consul_decisions': 'Prochain RDV dans 1 mois',
            'patient': patients[2][1] if len(patients) > 2 else None,
            'doctor': doctors[1][1] if len(doctors) > 1 else doctors[0][1]
        }
    ]

    appointments = []
    for appointment_data in appointments_data:
        if appointment_data['patient'] and appointment_data['doctor']:
            appointment, created = Appointment.objects.get_or_create(
                consul_date=appointment_data['consul_date'],
                consul_hour=appointment_data['consul_hour'],
                patient=appointment_data['patient'],
                doctor=appointment_data['doctor'],
                defaults=appointment_data
            )
            appointments.append(appointment)

            if created:
                print(f"   ✅ RDV créé pour {appointment.patient.firstname} avec Dr. {appointment.doctor.lastname}")
            else:
                print(f"   ℹ️  RDV existe déjà")

    return appointments

def create_doctor_patient_mappings(doctors, patients):
    """Créer les relations médecin-patient"""
    print("🔗 Création des relations médecin-patient...")

    mappings = []
    for i, (patient_user, patient_profile) in enumerate(patients):
        doctor_index = i % len(doctors)  # Répartir les patients entre les médecins
        doctor_user, doctor_profile = doctors[doctor_index]

        mapping, created = MapDoctorPatient.objects.get_or_create(
            doctor=doctor_profile,
            patient=patient_profile
        )
        mappings.append(mapping)

        if created:
            print(f"   ✅ Relation créée: Dr. {doctor_profile.lastname} -> {patient_profile.firstname}")
        else:
            print(f"   ℹ️  Relation existe déjà")

    return mappings

def main():
    """Fonction principale pour créer toutes les données de test"""
    print("🚀 Début du remplissage de la base de données PTC Care avec des données de test")
    print("=" * 80)

    try:
        # 1. Créer les groupes d'utilisateurs
        create_groups()
        print()

        # 2. Créer les données de référence
        hospitals = create_hospitals()
        print()

        services = create_services(hospitals)
        print()

        specialities = create_specialities()
        print()

        languages = create_languages()
        print()

        # 3. Créer les utilisateurs et profils
        admin_user, admin_profile = create_admin_user(hospitals, services, specialities, languages)
        print()

        doctors = create_doctors(hospitals, services, specialities, languages)
        print()

        assistants = create_assistants(hospitals, services, specialities, languages)
        print()

        patients = create_patients(hospitals, languages, doctors, assistants)
        print()

        # 4. Créer les données métier
        pregnancies = create_pregnancies(patients)
        print()

        appointments = create_appointments(patients, doctors)
        print()

        mappings = create_doctor_patient_mappings(doctors, patients)
        print()

        print("=" * 80)
        print("✅ SUCCÈS ! Base de données remplie avec des données de test")
        print()
        print("📊 RÉSUMÉ DES DONNÉES CRÉÉES :")
        print(f"   👑 Administrateurs : 1")
        print(f"   👨‍⚕️ Médecins : {len(doctors)}")
        print(f"   👩‍⚕️ Assistants : {len(assistants)}")
        print(f"   🤰 Patientes : {len(patients)}")
        print(f"   🏥 Hôpitaux : {len(hospitals)}")
        print(f"   🏢 Services : {len(services)}")
        print(f"   👨‍⚕️ Spécialités : {len(specialities)}")
        print(f"   🌍 Langues : {len(languages)}")
        print(f"   🤱 Grossesses : {len(pregnancies)}")
        print(f"   📅 Rendez-vous : {len(appointments)}")
        print()
        print("🔑 COMPTES DE TEST CRÉÉS :")
        print("   👑 Admin : username='admin', password='admin123'")
        print("   👨‍⚕️ Médecins : password='doctor123'")
        print("   👩‍⚕️ Assistants : password='assistant123'")
        print("   🤰 Patientes : password='patient123'")
        print()
        print("🌐 Accès à l'application :")
        print("   • Interface principale : http://localhost:8000")
        print("   • Administration Django : http://localhost:8000/super-admin/")
        print("   • API : http://localhost:8000/api/")
        print()
        print("🎉 Vous pouvez maintenant tester l'application PTC Care !")

    except Exception as e:
        print(f"❌ ERREUR lors de la création des données de test : {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main()
