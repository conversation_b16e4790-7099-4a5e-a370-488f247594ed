# Guide d'Intégration Mobile - APIs PTC Care

## 🏗️ Architecture d'Intégration Mobile

### Vue d'Ensemble de l'Architecture

```
┌─────────────────┐    HTTPS/TLS    ┌─────────────────┐
│   Mobile App    │ ◄──────────────► │   PTC Care API  │
│                 │                  │   (Django)      │
├─────────────────┤                  ├─────────────────┤
│ • Auth Manager  │                  │ • Authentication│
│ • API Client    │                  │ • User Mgmt     │
│ • Local Storage │                  │ • Email Service │
│ • Sync Manager  │                  │ • Data Endpoints│
│ • Offline Queue │                  │ • Security      │
└─────────────────┘                  └─────────────────┘
        │                                     │
        ▼                                     ▼
┌─────────────────┐                  ┌─────────────────┐
│ Local Database  │                  │ PostgreSQL DB   │
│ (SQLite/Realm)  │                  │ + Email SMTP    │
└─────────────────┘                  └─────────────────┘
```

### Composants Principaux

#### 1. **Authentication Manager**
- Gestion des tokens Bearer
- Détection du changement de mot de passe obligatoire
- Renouvellement automatique des sessions

#### 2. **API Client**
- Interface unifiée pour tous les endpoints
- Gestion des erreurs et retry automatique
- Support hors ligne avec queue

#### 3. **Local Storage Manager**
- Cache des données critiques
- Stockage sécurisé des tokens
- Gestion des données hors ligne

#### 4. **Sync Manager**
- Synchronisation bidirectionnelle
- Résolution des conflits
- Mapping mobile_id ↔ server_id

## 🔐 Flux d'Authentification

### Diagramme de Flux

```
┌─────────────┐
│ App Launch  │
└─────┬───────┘
      │
      ▼
┌─────────────┐    Non    ┌─────────────┐
│ Token Saved?│ ────────► │ Login Screen│
└─────┬───────┘           └─────┬───────┘
      │ Oui                     │
      ▼                         ▼
┌─────────────┐           ┌─────────────┐
│ Validate    │           │ POST /login │
│ Token       │           └─────┬───────┘
└─────┬───────┘                 │
      │                         ▼
      ▼                   ┌─────────────┐
┌─────────────┐    403    │ Password    │
│ API Call    │ ────────► │ Change      │
└─────┬───────┘           │ Required    │
      │ 200               └─────────────┘
      ▼
┌─────────────┐
│ Main App    │
└─────────────┘
```

### Implémentation Authentication Manager

#### Android (Kotlin)
```kotlin
class AuthenticationManager(private val context: Context) {
    private val prefs = context.getSharedPreferences("ptc_auth", Context.MODE_PRIVATE)
    private val apiClient = ApiClient()
    
    companion object {
        private const val TOKEN_KEY = "auth_token"
        private const val USER_ID_KEY = "user_id"
        private const val ROLE_KEY = "user_role"
    }
    
    suspend fun login(email: String, password: String): AuthResult {
        try {
            val response = apiClient.post<LoginResponse>("/api/login/") {
                body = LoginRequest(email, password)
            }
            
            when (response.status) {
                "success" -> {
                    saveAuthData(response.userId, response.role)
                    return AuthResult.Success(response)
                }
                else -> return AuthResult.Error("Identifiants invalides")
            }
        } catch (e: Exception) {
            return AuthResult.Error(e.message ?: "Erreur de connexion")
        }
    }
    
    private fun saveAuthData(userId: String, role: String) {
        prefs.edit()
            .putString(TOKEN_KEY, "Bearer $userId")
            .putString(USER_ID_KEY, userId)
            .putString(ROLE_KEY, role)
            .apply()
    }
    
    fun getAuthToken(): String? = prefs.getString(TOKEN_KEY, null)
    
    fun isLoggedIn(): Boolean = getAuthToken() != null
    
    fun logout() {
        prefs.edit().clear().apply()
    }
    
    suspend fun handlePasswordChangeRequired(): Boolean {
        // Rediriger vers l'écran de changement de mot de passe
        val intent = Intent(context, PasswordChangeActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        context.startActivity(intent)
        return true
    }
}

sealed class AuthResult {
    data class Success(val response: LoginResponse) : AuthResult()
    data class Error(val message: String) : AuthResult()
}

data class LoginRequest(val email: String, val password: String)
data class LoginResponse(
    val status: String,
    val userId: String,
    val username: String,
    val role: String,
    val name: String
)
```

#### iOS (Swift)
```swift
class AuthenticationManager {
    private let keychain = Keychain(service: "com.ptccare.app")
    private let apiClient = APIClient()
    
    private enum Keys {
        static let authToken = "auth_token"
        static let userId = "user_id"
        static let userRole = "user_role"
    }
    
    func login(email: String, password: String) async -> AuthResult {
        do {
            let request = LoginRequest(email: email, password: password)
            let response: LoginResponse = try await apiClient.post("/api/login/", body: request)
            
            if response.status == "success" {
                saveAuthData(userId: response.userId, role: response.role)
                return .success(response)
            } else {
                return .error("Identifiants invalides")
            }
        } catch {
            return .error(error.localizedDescription)
        }
    }
    
    private func saveAuthData(userId: String, role: String) {
        keychain[Keys.authToken] = "Bearer \(userId)"
        keychain[Keys.userId] = userId
        keychain[Keys.userRole] = role
    }
    
    var authToken: String? {
        return keychain[Keys.authToken]
    }
    
    var isLoggedIn: Bool {
        return authToken != nil
    }
    
    func logout() {
        try? keychain.removeAll()
    }
    
    func handlePasswordChangeRequired() {
        DispatchQueue.main.async {
            // Naviguer vers l'écran de changement de mot de passe
            if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
               let window = windowScene.windows.first {
                let passwordChangeVC = PasswordChangeViewController()
                let navController = UINavigationController(rootViewController: passwordChangeVC)
                window.rootViewController = navController
            }
        }
    }
}

enum AuthResult {
    case success(LoginResponse)
    case error(String)
}

struct LoginRequest: Codable {
    let email: String
    let password: String
}

struct LoginResponse: Codable {
    let status: String
    let userId: String
    let username: String
    let role: String
    let name: String
    
    enum CodingKeys: String, CodingKey {
        case status
        case userId = "user_id"
        case username
        case role
        case name
    }
}
```

## 🔄 Gestion du Changement de Mot de Passe Obligatoire

### Détection et Redirection

#### Android Implementation
```kotlin
class ApiClient {
    private val authManager = AuthenticationManager(context)
    
    suspend inline fun <reified T> makeRequest(
        endpoint: String,
        method: HttpMethod = HttpMethod.GET,
        body: Any? = null
    ): ApiResponse<T> {
        try {
            val response = httpClient.request(endpoint) {
                this.method = method
                headers {
                    append("Authorization", authManager.getAuthToken() ?: "")
                    append("Content-Type", "application/json")
                    append("User-Agent", "PTC-Care-Mobile/1.0")
                }
                if (body != null) {
                    setBody(Json.encodeToString(body))
                }
            }
            
            return when (response.status.value) {
                200 -> ApiResponse.Success(response.body())
                401 -> {
                    authManager.logout()
                    ApiResponse.Unauthorized
                }
                403 -> {
                    val errorBody = response.body<ErrorResponse>()
                    if (errorBody.error == "password_change_required") {
                        authManager.handlePasswordChangeRequired()
                        ApiResponse.PasswordChangeRequired
                    } else {
                        ApiResponse.Forbidden
                    }
                }
                400 -> ApiResponse.ValidationError(response.body<ErrorResponse>().message)
                else -> ApiResponse.Error("Erreur serveur: ${response.status.value}")
            }
        } catch (e: Exception) {
            return ApiResponse.NetworkError(e.message ?: "Erreur réseau")
        }
    }
}

sealed class ApiResponse<T> {
    data class Success<T>(val data: T) : ApiResponse<T>()
    object Unauthorized : ApiResponse<Nothing>()
    object PasswordChangeRequired : ApiResponse<Nothing>()
    object Forbidden : ApiResponse<Nothing>()
    data class ValidationError<T>(val message: String) : ApiResponse<T>()
    data class Error<T>(val message: String) : ApiResponse<T>()
    data class NetworkError<T>(val message: String) : ApiResponse<T>()
}
```

### Écran de Changement de Mot de Passe

#### Android (Compose)
```kotlin
@Composable
fun PasswordChangeScreen(
    viewModel: PasswordChangeViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "Changement de mot de passe obligatoire",
            style = MaterialTheme.typography.h5,
            color = MaterialTheme.colors.error
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        OutlinedTextField(
            value = uiState.currentPassword,
            onValueChange = viewModel::updateCurrentPassword,
            label = { Text("Mot de passe actuel") },
            visualTransformation = PasswordVisualTransformation(),
            modifier = Modifier.fillMaxWidth()
        )
        
        OutlinedTextField(
            value = uiState.newPassword,
            onValueChange = viewModel::updateNewPassword,
            label = { Text("Nouveau mot de passe") },
            visualTransformation = PasswordVisualTransformation(),
            modifier = Modifier.fillMaxWidth()
        )
        
        OutlinedTextField(
            value = uiState.confirmPassword,
            onValueChange = viewModel::updateConfirmPassword,
            label = { Text("Confirmer le mot de passe") },
            visualTransformation = PasswordVisualTransformation(),
            modifier = Modifier.fillMaxWidth()
        )
        
        // Indicateurs de sécurité du mot de passe
        PasswordStrengthIndicator(password = uiState.newPassword)
        
        Button(
            onClick = viewModel::changePassword,
            enabled = uiState.isValid && !uiState.isLoading,
            modifier = Modifier.fillMaxWidth()
        ) {
            if (uiState.isLoading) {
                CircularProgressIndicator(size = 16.dp)
            } else {
                Text("Changer le mot de passe")
            }
        }
        
        uiState.error?.let { error ->
            Text(
                text = error,
                color = MaterialTheme.colors.error,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}

class PasswordChangeViewModel @Inject constructor(
    private val apiClient: ApiClient,
    private val authManager: AuthenticationManager
) : ViewModel() {
    
    private val _uiState = MutableStateFlow(PasswordChangeUiState())
    val uiState: StateFlow<PasswordChangeUiState> = _uiState.asStateFlow()
    
    fun changePassword() {
        viewModelScope.launch {
            _uiState.value = _uiState.value.copy(isLoading = true, error = null)
            
            val request = ChangePasswordRequest(
                email = authManager.getUserEmail(),
                currentPassword = _uiState.value.currentPassword,
                newPassword = _uiState.value.newPassword
            )
            
            when (val result = apiClient.post<ChangePasswordResponse>("/api/change-password/", request)) {
                is ApiResponse.Success -> {
                    // Mot de passe changé avec succès
                    // Rediriger vers l'écran principal
                }
                is ApiResponse.ValidationError -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = result.message
                    )
                }
                else -> {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "Erreur lors du changement de mot de passe"
                    )
                }
            }
        }
    }
}
```

## 👥 Création d'Utilisateurs avec Notifications Email

### Service de Gestion des Utilisateurs

#### Android Implementation
```kotlin
class UserManagementService @Inject constructor(
    private val apiClient: ApiClient
) {
    suspend fun createHealthAgent(agent: CreateAgentRequest): CreateUserResult {
        return when (val response = apiClient.post<CreateAgentResponse>("/api/create-health-agent/", agent)) {
            is ApiResponse.Success -> {
                if (response.data.emailSent) {
                    CreateUserResult.Success(
                        username = response.data.username,
                        emailSent = true,
                        message = "Agent créé avec succès. Email envoyé à ${agent.email}"
                    )
                } else {
                    CreateUserResult.Success(
                        username = response.data.username,
                        emailSent = false,
                        message = "Agent créé mais email non envoyé: ${response.data.emailError}"
                    )
                }
            }
            is ApiResponse.ValidationError -> CreateUserResult.ValidationError(response.message)
            is ApiResponse.Unauthorized -> CreateUserResult.Unauthorized
            else -> CreateUserResult.Error("Erreur lors de la création")
        }
    }
    
    suspend fun createPatient(patient: CreatePatientRequest): CreateUserResult {
        return when (val response = apiClient.post<CreatePatientResponse>("/api/create-patient/", patient)) {
            is ApiResponse.Success -> {
                CreateUserResult.Success(
                    username = response.data.username,
                    emailSent = response.data.emailSent,
                    message = if (response.data.emailSent) {
                        "Patient créé avec succès. Email envoyé à ${patient.email}"
                    } else {
                        "Patient créé mais email non envoyé"
                    }
                )
            }
            is ApiResponse.ValidationError -> CreateUserResult.ValidationError(response.message)
            else -> CreateUserResult.Error("Erreur lors de la création")
        }
    }
}

data class CreateAgentRequest(
    val firstname: String,
    val lastname: String,
    val tel: String,
    val email: String,
    val role: String, // "docteur" ou "assistant"
    val sexe: String,
    val address: String,
    val hospitalId: Int,
    val serviceId: Int,
    val specialityId: Int
)

data class CreatePatientRequest(
    val firstname: String,
    val lastname: String,
    val tel: String,
    val email: String,
    val sexe: String,
    val birthDate: String,
    val address: String,
    val languageId: Int,
    val occupation: String? = null,
    val isChild: Boolean = false,
    val isPregnant: Boolean = false,
    val pregnancySituation: String? = null
)

sealed class CreateUserResult {
    data class Success(
        val username: String,
        val emailSent: Boolean,
        val message: String
    ) : CreateUserResult()
    
    data class ValidationError(val message: String) : CreateUserResult()
    object Unauthorized : CreateUserResult()
    data class Error(val message: String) : CreateUserResult()
}
```

### Interface de Création d'Utilisateur

#### Android (Compose)
```kotlin
@Composable
fun CreatePatientScreen(
    viewModel: CreatePatientViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        item {
            Text(
                text = "Nouveau Patient",
                style = MaterialTheme.typography.h5
            )
        }
        
        item {
            OutlinedTextField(
                value = uiState.firstname,
                onValueChange = viewModel::updateFirstname,
                label = { Text("Prénom *") },
                modifier = Modifier.fillMaxWidth(),
                isError = uiState.errors.containsKey("firstname")
            )
            uiState.errors["firstname"]?.let { error ->
                Text(text = error, color = MaterialTheme.colors.error, fontSize = 12.sp)
            }
        }
        
        item {
            OutlinedTextField(
                value = uiState.lastname,
                onValueChange = viewModel::updateLastname,
                label = { Text("Nom *") },
                modifier = Modifier.fillMaxWidth(),
                isError = uiState.errors.containsKey("lastname")
            )
        }
        
        item {
            OutlinedTextField(
                value = uiState.email,
                onValueChange = viewModel::updateEmail,
                label = { Text("Email *") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
                modifier = Modifier.fillMaxWidth(),
                isError = uiState.errors.containsKey("email")
            )
        }
        
        item {
            OutlinedTextField(
                value = uiState.tel,
                onValueChange = viewModel::updateTel,
                label = { Text("Téléphone *") },
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Phone),
                modifier = Modifier.fillMaxWidth()
            )
        }
        
        // Autres champs...
        
        item {
            Button(
                onClick = viewModel::createPatient,
                enabled = uiState.isValid && !uiState.isLoading,
                modifier = Modifier.fillMaxWidth()
            ) {
                if (uiState.isLoading) {
                    CircularProgressIndicator(size = 16.dp)
                } else {
                    Text("Créer le Patient")
                }
            }
        }
        
        uiState.result?.let { result ->
            item {
                when (result) {
                    is CreateUserResult.Success -> {
                        Card(
                            backgroundColor = Color.Green.copy(alpha = 0.1f),
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Column(modifier = Modifier.padding(16.dp)) {
                                Text(
                                    text = "✅ Patient créé avec succès",
                                    fontWeight = FontWeight.Bold,
                                    color = Color.Green
                                )
                                Text(text = "Username: ${result.username}")
                                Text(
                                    text = if (result.emailSent) {
                                        "📧 Email de bienvenue envoyé"
                                    } else {
                                        "⚠️ Email non envoyé"
                                    }
                                )
                            }
                        }
                    }
                    is CreateUserResult.ValidationError -> {
                        Text(
                            text = result.message,
                            color = MaterialTheme.colors.error
                        )
                    }
                    else -> {
                        Text(
                            text = "Erreur lors de la création",
                            color = MaterialTheme.colors.error
                        )
                    }
                }
            }
        }
    }
}
```
