import importlib
from django.http import JsonResponse
import datetime as dt
from django.shortcuts import redirect, render,get_object_or_404,HttpResponseRedirect
from celery.worker.control import revoke
from celery.result import AsyncResult
from django.core.files.storage import FileSystemStorage
from datetime import datetime

# relative import of forms
from django.contrib import messages
from ptcapp.helpers.authentification_helper import is_doctor
from ptcapp.models import Appointment, Alert, appointment
from ptcapp.models.appointment_file import AppointmentFile
from ptcapp.models.profile import Profile

from ptcapp.tasks import call, send_sms;


def create_view(request):
    if request.method == 'POST':
        patient = get_object_or_404(Profile, id=request.POST['appointment_patient'])
        if is_doctor(request.user):
            doctor = get_object_or_404(Profile, user=request.user)
        else:
            doctor = get_object_or_404(Profile, id=request.POST['appointment_doctor'])

        type_appointment = request.POST['appointment_type']
        consul_date = request.POST['appointment_date']
        consul_time = request.POST['appointment_hour']

        appointment = Appointment()
        appointment.patient = patient
        appointment.doctor = doctor
        appointment.consul_date = consul_date
        appointment.consul_hour = consul_time
        appointment.appointment_type = type_appointment
        appointment.state = "pending"
        appointment.save()

        fullname = get_object_or_404(Profile, user=request.user).firstname+" "+get_object_or_404(Profile, user=request.user).lastname

    
        sms_alert = Alert()
        voice_alert = Alert()

        sms_time = dt.datetime.strptime('1300','%H%M').time()
        call_time = dt.datetime.strptime('1812','%H%M').time()

        sms_date = dt.datetime.strptime(consul_date,"%Y-%m-%d").date() - dt.timedelta(days = 4)
        call_date_one = dt.datetime.strptime(consul_date,"%Y-%m-%d").date() - dt.timedelta(days = 1)
        call_date_three = dt.datetime.strptime(consul_date,"%Y-%m-%d").date() - dt.timedelta(days = 3)
        call_date_seven = dt.datetime.strptime(consul_date,"%Y-%m-%d").date() - dt.timedelta(days = 7)

        if(call_date_seven >= datetime.today().date()):
            print("plus de 7")
            call_task = call.apply_async((patient.tel, "ptcapp/static/uploads/"+patient.language.path+"/rel-7.wav"), eta=dt.datetime.combine(call_date_seven, call_time), queue='call-one')
        if(call_date_three > datetime.today().date()):
            print("plus de 3")
            call_task = call.apply_async((patient.tel, "ptcapp/static/uploads/"+patient.language.path+"/rel-3.wav"), eta=dt.datetime.combine(call_date_three, call_time), queue='call-one')
        if(call_date_one >= datetime.today().date()):
            print("plus de 1")
            call_task = call.apply_async((patient.tel, "ptcapp/static/uploads/"+patient.language.path+"/rel-1.wav"), eta=dt.datetime.combine(call_date_one, call_time), queue='call-one')

        #sms_task = send_sms.delay(patient.firstname+" "+patient.lastname, consul_date, consul_time, patient.tel, fullname) 
        #call_task = call.apply_async((patient.tel, "ptcapp/"+patient.language.path, ), countdown=30) 

        # sms_task = send_sms.apply_async((patient.firstname, consul_date, consul_time, patient.tel), eta=dt.datetime.combine(sms_date, sms_time))
        # call_task = call.apply_async((patient.tel, patient.language.path), eta=dt.datetime.combine(call_date, call_time))
        
        # sms_task = send_sms.delay(patient.firstname, consul_date, consul_time, patient.tel, fullname) 
        # call_task = call.apply_async((patient.tel, "ptcapp/static/uploads/"+patient.language.path+"rel-1.wav",), queue='call-one' )
        # call_task = call.apply_async(("63753456", "ptcapp/static/uploads/rel-1.wav",), countdown = 50, queue='call-one')
        # call_task = call.apply_async(("67924963", "ptcapp/static/uploads/rel-1.wav",), queue='call-one')

        # sms_task = show.apply_async((), countdown=30)
        # call_task = show.apply_async((), countdown=30)
        # sms_alert.appointment = appointment
        # sms_alert.type = "sms"
        # sms_alert.date = sms_date
        # sms_alert.time = sms_time
        # sms_alert.state = "pending"
        # sms_alert.task_id = sms_task.task_id
        # sms_alert.save()

        # voice_alert.appointment = appointment
        # voice_alert.type = "voice_call"
        # voice_alert.date = call_date
        # voice_alert.time = call_time
        # voice_alert.state = "pending"
        # voice_alert.task_id = call_task.task_id
        # voice_alert.save()

        return JsonResponse({'appointment':int(appointment.id), 'success' : True})
    return JsonResponse({'success' : False})


def list_view(request):
    context ={}
  
    context["dataset"] = Appointment.objects.all()
    return render(request, "appointment/list.html", context)


def detail_view(request, id):
    
    context ={}
 
    context["data"] = Appointment.objects.get(id = id)
         
    return render(request, "appointment/detail.html", context)


def update_view(request, id):
    if request.method == 'POST':
        fullname = get_object_or_404(Profile, user=request.user).firstname+" "+get_object_or_404(Profile, user=request.user).lastname
        patient = get_object_or_404(Profile, id=request.POST['appointment_patient'])
        if is_doctor(request.user):
            doctor = get_object_or_404(Profile, user=request.user)
        else:
            doctor = get_object_or_404(Profile, id=request.POST['appointment_doctor'])
        type_appointment = request.POST['type_appointment']
        consul_date = request.POST['consul_date']
        consul_time = request.POST['consul_time']
        state = request.POST['state']
        consul_data = request.POST['consul_data']
        consul_resume = request.POST['consul_resume']
        consul_decisions = request.POST['consul_decisions']

        appointment = get_object_or_404(Appointment, id=id)
        appointment.patient = patient
        appointment.doctor = doctor
        appointment.consul_date = consul_date
        appointment.consul_hour = consul_time
        appointment.appointment_type = type_appointment
        appointment.state = state
        appointment.consul_data = consul_data
        appointment.consul_resume = consul_resume
        appointment.consul_decisions = consul_decisions
        appointment.save()

        old_sms = get_object_or_404(Alert, appointment = appointment, type = "sms")
        old_call = get_object_or_404(Alert, appointment = appointment, type = "voice_call")


        result_sms = AsyncResult(old_sms.task_id)
        result_call = AsyncResult(old_sms.task_id)
        #result.revoke()
        result_sms.revoke(terminate= True)
        result_call.revoke(terminate= True)

        old_sms.delete()
        old_call.delete()

        sms_alert = Alert()
        voice_alert = Alert()

        sms_time = dt.datetime.strptime('1300','%H%M').time()
        call_time = dt.datetime.strptime('1000','%H%M').time()

        sms_date = dt.datetime.strptime(consul_date,"%Y-%m-%d").date() - dt.timedelta(days = 4)
        call_date = dt.datetime.strptime(consul_date,"%Y-%m-%d").date() - dt.timedelta(days = 2)

        sms_task = send_sms.delay(patient.firstname, consul_date, consul_time, patient.tel, fullname) 
        call_task = call.delay(patient.tel, "ptcapp/"+patient.language.path)
        
        sms_alert.appointment = appointment
        sms_alert.type = "sms"
        sms_alert.date = sms_date
        sms_alert.time = sms_time
        sms_alert.state = "pending"
        sms_alert.task_id = sms_task.task_id
        sms_alert.save()

        voice_alert.appointment = appointment
        voice_alert.type = "voice_call"
        voice_alert.date = call_date
        voice_alert.time = call_time
        voice_alert.state = "pending"
        voice_alert.task_id = call_task.task_id
        voice_alert.save()


        messages.success(request, "Consulation mis à jour avec succès")
        return redirect('/assistant/appointment/')
    return JsonResponse({'success' : False})
    

def delete_view(request, id):
    context ={}
 
    obj = get_object_or_404(Appointment, id = id)
 
    if request.method =="POST":
        obj.delete()
        return HttpResponseRedirect("/")
 
    return render(request, "appointment/delete.html", context)


def save_files(request, id):
    now = datetime.now()
    now = now.strftime("%Y%m%d%H%M%S")
    if(request.FILES):
        for myfile in request.FILES:
            fs = FileSystemStorage()
            lst=request.FILES[myfile].name.split('.')
            ext=lst[len(lst)-1]
            filename = fs.save("appointment-"+id+"-"+now+"."+ext, request.FILES[myfile])
            uploaded_file_url = fs.url(filename)


            appointment_file = AppointmentFile()
            appointment_file.title = "appointment-"+id+"-"+now+"."+ext
            appointment_file.appointment=get_object_or_404(Appointment, id=id)
            appointment_file.path = uploaded_file_url
            appointment_file.save()
        return JsonResponse({'success' : True})
    raise
    
def delete_file(request, id):
    context ={}
 
    obj = get_object_or_404(AppointmentFile, id = id)
 
    obj.delete()
    return JsonResponse({'success' : True})