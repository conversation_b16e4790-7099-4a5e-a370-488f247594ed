from django.db import models
from .profile import Profile


class Record(models.Model):
    profile = models.ForeignKey(Profile, related_name='patient', null = True, on_delete=models.SET_NULL)
    archived = models.BooleanField(default= False)
    archived_by = models.ForeignKey(Profile, related_name='docteur', null = True, on_delete=models.SET_NULL)
    archived_at = models.DateTimeField(null= True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
