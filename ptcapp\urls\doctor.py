from .. import views
from django.urls import path

urlpatterns = [
    path('', views.views.doctorIndex, name='doctor.index'),
    path('archives/', views.views.doctorArchives, name='doctor.archives'),
    path('grossesses/', views.views.doctorPregnancies, name='doctor.pregnancy'),
    # path('patients/', views.views.doctorPatients, name='doctor.patients'),
    path('patients', views.views.doctorFilters, name='doctor.filters'),
    path('archives', views.views.doctorArchiveFilters, name='doctor.archivefilters'),
    path('appointment', views.views.doctorAppointmentFilters, name='doctor.appointmentfilters'),
    path('pregnancy', views.views.doctorPregnancyFilters, name='doctor.pregnancyfilters'),
    path('dossier/<id>', views.views.doctorShowRecord, name='doctor.record.show'),
    path('dossier/archive/<id>', views.profile.archive, name='doctor.record.archive'),
    path('dossier/unarchive/<id>', views.profile.unarchive, name='doctor.record.unarchive'),
    path('patient/create', views.views.doctorCreatePatient, name='doctor.patient.create'),
    path('patient/edit/<id>', views.views.doctorEditPatient, name='doctor.patient.edit'),
    path('appointment/', views.views.doctorAppointmentIndex, name='doctor.appointment.index'),
    path('appointment/edit/<id>', views.views.doctorAppointmentEdit, name='doctor.appointment.edit'),
    path('pregnancy/edit/<id>', views.views.doctorPregnancyEdit, name='doctor.pregnancy.edit'),
    path('profil', views.views.doctorProfile, name='doctor.profile'),
    path('appointment/show/<id>', views.views.doctorAppointmentShow, name='doctor.appointment.show'),
    path('profil', views.views.doctorProfile, name='doctor.profile'),
    path('attached-doctor', views.views.attached_doctor, name='profile.attached')
]