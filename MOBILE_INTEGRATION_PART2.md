# Guide d'Intégration Mobile - Partie 2

## 📊 Récupération et Affichage des Données

### Service de Données

#### Android Implementation
```kotlin
class DataService @Inject constructor(
    private val apiClient: ApiClient,
    private val localDatabase: LocalDatabase
) {
    suspend fun getInitialData(): DataResult<InitialData> {
        return try {
            when (val response = apiClient.get<InitialDataResponse>("/api/initial-data/")) {
                is ApiResponse.Success -> {
                    // Sauvegarder en local pour utilisation hors ligne
                    localDatabase.saveInitialData(response.data)
                    DataResult.Success(response.data.toInitialData())
                }
                is ApiResponse.NetworkError -> {
                    // Utiliser les données en cache si disponibles
                    val cachedData = localDatabase.getInitialData()
                    if (cachedData != null) {
                        DataResult.Cached(cachedData)
                    } else {
                        DataResult.Error("Aucune donnée disponible hors ligne")
                    }
                }
                else -> DataResult.Error("Erreur lors de la récupération des données")
            }
        } catch (e: Exception) {
            DataResult.Error(e.message ?: "Erreur inconnue")
        }
    }
    
    suspend fun getPatients(): DataResult<List<Patient>> {
        return when (val response = apiClient.get<PatientsResponse>("/api/patients/")) {
            is ApiResponse.Success -> {
                localDatabase.savePatients(response.data.patients)
                DataResult.Success(response.data.patients)
            }
            is ApiResponse.PasswordChangeRequired -> DataResult.PasswordChangeRequired
            is ApiResponse.Unauthorized -> DataResult.Unauthorized
            is ApiResponse.NetworkError -> {
                val cachedPatients = localDatabase.getPatients()
                DataResult.Cached(cachedPatients)
            }
            else -> DataResult.Error("Erreur lors de la récupération des patients")
        }
    }
}

sealed class DataResult<T> {
    data class Success<T>(val data: T) : DataResult<T>()
    data class Cached<T>(val data: T) : DataResult<T>()
    object PasswordChangeRequired : DataResult<Nothing>()
    object Unauthorized : DataResult<Nothing>()
    data class Error<T>(val message: String) : DataResult<T>()
}
```

## 🔄 Synchronisation Hors Ligne

### Architecture de Synchronisation

```
┌─────────────────┐
│ User Actions    │
└─────┬───────────┘
      │
      ▼
┌─────────────────┐    Offline    ┌─────────────────┐
│ Local Database  │ ◄──────────── │ Offline Queue   │
│ (SQLite/Realm)  │               │ (Pending Sync)  │
└─────┬───────────┘               └─────────────────┘
      │                                     │
      │ Online                              │ Background Sync
      ▼                                     ▼
┌─────────────────┐               ┌─────────────────┐
│ Sync Manager    │ ────────────► │ Server API      │
│ (Conflict Res.) │               │ /sync-mobile/   │
└─────────────────┘               └─────────────────┘
```

### Gestionnaire de Synchronisation

#### Android Implementation
```kotlin
class SyncManager @Inject constructor(
    private val apiClient: ApiClient,
    private val localDatabase: LocalDatabase,
    private val connectivityManager: ConnectivityManager
) {
    private val _syncStatus = MutableStateFlow(SyncStatus.Idle)
    val syncStatus: StateFlow<SyncStatus> = _syncStatus.asStateFlow()
    
    suspend fun syncData(): SyncResult {
        if (!isOnline()) {
            return SyncResult.NoConnection
        }
        
        _syncStatus.value = SyncStatus.Syncing
        
        try {
            // Récupérer les données en attente de synchronisation
            val pendingPatients = localDatabase.getPendingPatients()
            val pendingPregnancies = localDatabase.getPendingPregnancies()
            val pendingAppointments = localDatabase.getPendingAppointments()
            
            val syncRequest = SyncRequest(
                patients = pendingPatients.map { it.toSyncPatient() },
                pregnancies = pendingPregnancies.map { it.toSyncPregnancy() },
                appointments = pendingAppointments.map { it.toSyncAppointment() }
            )
            
            when (val response = apiClient.post<SyncResponse>("/api/sync-mobile-data/", syncRequest)) {
                is ApiResponse.Success -> {
                    // Traiter les réponses et mettre à jour les IDs locaux
                    processSyncResponse(response.data)
                    _syncStatus.value = SyncStatus.Success
                    return SyncResult.Success(response.data.processed)
                }
                
                is ApiResponse.PasswordChangeRequired -> {
                    _syncStatus.value = SyncStatus.PasswordChangeRequired
                    return SyncResult.PasswordChangeRequired
                }
                
                else -> {
                    _syncStatus.value = SyncStatus.Error("Erreur de synchronisation")
                    return SyncResult.Error("Erreur de synchronisation")
                }
            }
        } catch (e: Exception) {
            _syncStatus.value = SyncStatus.Error(e.message ?: "Erreur inconnue")
            return SyncResult.Error(e.message ?: "Erreur inconnue")
        }
    }
    
    private suspend fun processSyncResponse(response: SyncResponse) {
        // Mettre à jour les IDs des patients
        response.processed.patients.forEach { processedPatient ->
            if (processedPatient.created) {
                localDatabase.updatePatientServerId(
                    mobileId = processedPatient.mobileId,
                    serverId = processedPatient.serverId
                )
            }
        }
        
        // Marquer les éléments comme synchronisés
        localDatabase.markAsSynced(
            patientIds = response.processed.patients.filter { it.created }.map { it.mobileId },
            pregnancyIds = response.processed.pregnancies.filter { it.created }.map { it.mobileId },
            appointmentIds = response.processed.appointments.filter { it.created }.map { it.mobileId }
        )
    }
    
    private fun isOnline(): Boolean {
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }
    
    // Synchronisation automatique en arrière-plan
    suspend fun scheduleBackgroundSync() {
        while (true) {
            delay(30_000) // Attendre 30 secondes
            
            if (isOnline() && localDatabase.hasPendingData()) {
                syncData()
            }
        }
    }
}

sealed class SyncStatus {
    object Idle : SyncStatus()
    object Syncing : SyncStatus()
    object Success : SyncStatus()
    object PasswordChangeRequired : SyncStatus()
    data class Error(val message: String) : SyncStatus()
}

data class SyncRequest(
    val patients: List<SyncPatient>,
    val pregnancies: List<SyncPregnancy>,
    val appointments: List<SyncAppointment>
)
```

### Base de Données Locale

#### Android (Room Database)
```kotlin
@Entity(tableName = "patients")
data class LocalPatient(
    @PrimaryKey val mobileId: String = UUID.randomUUID().toString(),
    val serverId: Int? = null,
    val firstname: String,
    val lastname: String,
    val tel: String,
    val email: String,
    val sexe: String,
    val birthDate: String,
    val address: String,
    val isChild: Boolean = false,
    val isPregnant: Boolean = false,
    val needsSync: Boolean = true,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

@Dao
interface PatientDao {
    @Query("SELECT * FROM patients WHERE needsSync = 1")
    suspend fun getPendingPatients(): List<LocalPatient>
    
    @Query("SELECT * FROM patients")
    suspend fun getAllPatients(): List<LocalPatient>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPatient(patient: LocalPatient)
    
    @Query("UPDATE patients SET serverId = :serverId, needsSync = 0 WHERE mobileId = :mobileId")
    suspend fun updateServerId(mobileId: String, serverId: Int)
    
    @Query("UPDATE patients SET needsSync = 0 WHERE mobileId IN (:mobileIds)")
    suspend fun markAsSynced(mobileIds: List<String>)
    
    @Query("SELECT COUNT(*) > 0 FROM patients WHERE needsSync = 1")
    suspend fun hasPendingData(): Boolean
}

@Database(
    entities = [LocalPatient::class, LocalPregnancy::class, LocalAppointment::class],
    version = 1,
    exportSchema = false
)
abstract class LocalDatabase : RoomDatabase() {
    abstract fun patientDao(): PatientDao
    abstract fun pregnancyDao(): PregnancyDao
    abstract fun appointmentDao(): AppointmentDao
}
```

## 🔒 Sécurité et Bonnes Pratiques

### Stockage Sécurisé des Tokens

#### Android (EncryptedSharedPreferences)
```kotlin
class SecureStorage @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private val masterKey = MasterKey.Builder(context)
        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
        .build()
    
    private val encryptedPrefs = EncryptedSharedPreferences.create(
        context,
        "ptc_secure_prefs",
        masterKey,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )
    
    fun saveAuthToken(token: String) {
        encryptedPrefs.edit()
            .putString("auth_token", token)
            .apply()
    }
    
    fun getAuthToken(): String? {
        return encryptedPrefs.getString("auth_token", null)
    }
    
    fun clearAuthData() {
        encryptedPrefs.edit().clear().apply()
    }
    
    fun saveUserData(userId: String, role: String, email: String) {
        encryptedPrefs.edit()
            .putString("user_id", userId)
            .putString("user_role", role)
            .putString("user_email", email)
            .apply()
    }
}
```

#### iOS (Keychain)
```swift
class SecureStorage {
    private let keychain = Keychain(service: "com.ptccare.app")
        .synchronizable(false)
        .accessibility(.whenUnlockedThisDeviceOnly)
    
    func saveAuthToken(_ token: String) {
        keychain["auth_token"] = token
    }
    
    func getAuthToken() -> String? {
        return keychain["auth_token"]
    }
    
    func clearAuthData() {
        try? keychain.removeAll()
    }
    
    func saveUserData(userId: String, role: String, email: String) {
        keychain["user_id"] = userId
        keychain["user_role"] = role
        keychain["user_email"] = email
    }
}
```

### Configuration HTTPS et Certificate Pinning

#### Android (OkHttp)
```kotlin
class NetworkModule {
    @Provides
    @Singleton
    fun provideOkHttpClient(): OkHttpClient {
        val certificatePinner = CertificatePinner.Builder()
            .add("your-api-domain.com", "sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=")
            .build()
        
        return OkHttpClient.Builder()
            .certificatePinner(certificatePinner)
            .addInterceptor(AuthInterceptor())
            .addInterceptor(LoggingInterceptor())
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build()
    }
}

class AuthInterceptor @Inject constructor(
    private val secureStorage: SecureStorage
) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        val token = secureStorage.getAuthToken()
        
        val newRequest = if (token != null) {
            originalRequest.newBuilder()
                .addHeader("Authorization", token)
                .addHeader("User-Agent", "PTC-Care-Mobile/1.0")
                .build()
        } else {
            originalRequest.newBuilder()
                .addHeader("User-Agent", "PTC-Care-Mobile/1.0")
                .build()
        }
        
        return chain.proceed(newRequest)
    }
}
```

### Gestion des Erreurs et Retry

#### Android Implementation
```kotlin
class ApiClient @Inject constructor(
    private val httpClient: OkHttpClient,
    private val secureStorage: SecureStorage
) {
    private val maxRetries = 3
    private val retryDelayMs = 1000L
    
    suspend fun <T> makeRequestWithRetry(
        request: suspend () -> ApiResponse<T>
    ): ApiResponse<T> {
        repeat(maxRetries) { attempt ->
            when (val result = request()) {
                is ApiResponse.NetworkError -> {
                    if (attempt < maxRetries - 1) {
                        delay(retryDelayMs * (attempt + 1))
                        return@repeat
                    }
                    return result
                }
                is ApiResponse.Unauthorized -> {
                    // Token expiré, nettoyer et rediriger vers login
                    secureStorage.clearAuthData()
                    return result
                }
                else -> return result
            }
        }
        
        return ApiResponse.Error("Échec après $maxRetries tentatives")
    }
    
    suspend inline fun <reified T> get(endpoint: String): ApiResponse<T> {
        return makeRequestWithRetry {
            try {
                val request = Request.Builder()
                    .url("${BuildConfig.API_BASE_URL}$endpoint")
                    .get()
                    .build()
                
                val response = httpClient.newCall(request).execute()
                handleResponse<T>(response)
            } catch (e: Exception) {
                ApiResponse.NetworkError(e.message ?: "Erreur réseau")
            }
        }
    }
    
    private inline fun <reified T> handleResponse(response: Response): ApiResponse<T> {
        return when (response.code) {
            200 -> {
                val json = response.body?.string() ?: ""
                val data = Json.decodeFromString<T>(json)
                ApiResponse.Success(data)
            }
            401 -> ApiResponse.Unauthorized
            403 -> {
                val errorBody = response.body?.string()?.let { 
                    Json.decodeFromString<ErrorResponse>(it) 
                }
                if (errorBody?.error == "password_change_required") {
                    ApiResponse.PasswordChangeRequired
                } else {
                    ApiResponse.Forbidden
                }
            }
            400 -> {
                val errorBody = response.body?.string()?.let { 
                    Json.decodeFromString<ErrorResponse>(it) 
                }
                ApiResponse.ValidationError(errorBody?.message ?: "Erreur de validation")
            }
            else -> ApiResponse.Error("Erreur serveur: ${response.code}")
        }
    }
}
```
