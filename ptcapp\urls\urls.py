"""ptccare URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from ..views import views, api, password_change

urlpatterns = [
    path('', views.index, name='index'),
    path('logout', views.disconnect, name='logout'),
    # Password change URLs
    path('change-password/', password_change.change_password_view, name='change_password'),
    path('change-password/<str:token>/', password_change.change_password_with_token, name='change_password_token'),
    # API endpoints
    path('api/login/', api.login_api, name='api.login'),
    path('api/change-password/', password_change.change_password_api, name='api.change_password'),
    path('api/request-password-reset/', password_change.request_password_reset_api, name='api.request_password_reset'),
    path('api/initial-data/', api.get_initial_data, name='api.initial_data'),
    path('api/create-patient/', api.create_patient, name='api.create_patient'),
    path('api/create-health-agent/', api.create_health_agent, name='api.create_health_agent'),
    path('api/health-centers/', api.get_health_centers, name='api.health_centers'),
    path('api/create-health-center/', api.create_health_center, name='api.create_health_center'),
    path('api/agents/', api.get_agents, name='api.agents'),
    path('api/patients/', api.get_patients, name='api.patients'),
    path('api/sync-mobile-data/', api.sync_mobile_data, name='api.sync_mobile_data'),
    # Mobile API endpoints
    path('api/mobile/', include('ptcapp.urls.mobile_api_urls')),
    # Application URLs
    path('appointment/', include('ptcapp.urls.appointment')),
    path('alert/', include('ptcapp.urls.alert')),
    path('records/', include('ptcapp.urls.record')),
    path('hospital/', include('ptcapp.urls.hospital')),
    path('pregnancy/', include('ptcapp.urls.pregnancy')),
    path('profile/', include('ptcapp.urls.profile')),
    path('service/', include('ptcapp.urls.service')),
    path('language/', include('ptcapp.urls.language')),
    path('speciality/', include('ptcapp.urls.speciality')),
    path('patient/', include('ptcapp.urls.patient')),
    path('admin/', include('ptcapp.urls.admin')),
    path('assistant/', include('ptcapp.urls.assistant')),
    path('docteur/', include('ptcapp.urls.doctor'))
]
