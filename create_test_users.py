#!/usr/bin/env python
"""
Script pour créer des utilisateurs de test pour chaque groupe d'utilisateurs 
dans l'application PTC Care afin de valider le nouveau système d'authentification par email.
"""

import os
import sys
import django
from django.conf import settings
from datetime import datetime, date

# Configuration de Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ptccare.settings')
django.setup()

from django.contrib.auth.models import User, Group
from django.contrib.auth import authenticate
from ptcapp.models import (
    Profile, Hospital, Service, Speciality, Language,
    MapDoctorPatient
)
from django.test import Client
import json


def ensure_reference_data():
    """S'assure que les données de référence nécessaires existent."""
    print("🔧 Vérification des données de référence...")
    
    # Créer les groupes s'ils n'existent pas
    groups_created = []
    for group_name in ['admin', 'docteur', 'assistant', 'patient']:
        group, created = Group.objects.get_or_create(name=group_name)
        if created:
            groups_created.append(group_name)
    
    if groups_created:
        print(f"✅ Groupes créés: {', '.join(groups_created)}")
    
    # Créer un hôpital de test s'il n'existe pas
    hospital, created = Hospital.objects.get_or_create(
        name="Hôpital de Test PTC",
        defaults={
            'address': "123 Rue de Test, Porto-Novo",
            'tel': "22912345678"
        }
    )
    if created:
        print("✅ Hôpital de test créé")
    
    # Créer un service de test s'il n'existe pas
    service, created = Service.objects.get_or_create(
        name="Service de Test",
        hospital=hospital,
        defaults={
            'description': "Service de test pour les utilisateurs de démonstration"
        }
    )
    if created:
        print("✅ Service de test créé")
    
    # Créer une spécialité de test s'il n'existe pas
    speciality, created = Speciality.objects.get_or_create(
        name="Médecine Générale",
        defaults={
            'description': "Spécialité de médecine générale pour les tests"
        }
    )
    if created:
        print("✅ Spécialité de test créée")
    
    # Créer une langue de test s'il n'existe pas
    language, created = Language.objects.get_or_create(
        name="Français",
        defaults={
            'path': "fr"
        }
    )
    if created:
        print("✅ Langue de test créée")
    
    return {
        'hospital': hospital,
        'service': service,
        'speciality': speciality,
        'language': language
    }


def cleanup_existing_test_users():
    """Supprime les utilisateurs de test existants."""
    print("🧹 Suppression des utilisateurs de test existants...")
    
    test_emails = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>',
        '<EMAIL>'
    ]
    
    deleted_count = User.objects.filter(email__in=test_emails).count()
    User.objects.filter(email__in=test_emails).delete()
    
    if deleted_count > 0:
        print(f"✅ {deleted_count} utilisateurs de test supprimés")
    else:
        print("ℹ️  Aucun utilisateur de test existant trouvé")


def create_test_users(reference_data):
    """Crée les utilisateurs de test pour chaque groupe."""
    print("\n👥 Création des utilisateurs de test...")
    
    # Mot de passe sécurisé pour tous les utilisateurs de test
    test_password = "TestPTC2024!"
    
    users_data = [
        {
            'group': 'admin',
            'email': '<EMAIL>',
            'username': 'ADM-TEST001',
            'firstname': 'Administrateur',
            'lastname': 'Test',
            'tel': '+22912345001',
            'sexe': 'M',
            'address': '123 Avenue Admin, Porto-Novo',
            'hospital': reference_data['hospital'],
            'service': reference_data['service'],
            'speciality': None,
            'language': reference_data['language']
        },
        {
            'group': 'docteur',
            'email': '<EMAIL>',
            'username': 'DOC-TEST001',
            'firstname': 'Docteur',
            'lastname': 'Médecin',
            'tel': '+22912345002',
            'sexe': 'M',
            'address': '456 Rue Médecin, Porto-Novo',
            'hospital': reference_data['hospital'],
            'service': reference_data['service'],
            'speciality': reference_data['speciality'],
            'language': reference_data['language']
        },
        {
            'group': 'assistant',
            'email': '<EMAIL>',
            'username': 'ASS-TEST001',
            'firstname': 'Assistant',
            'lastname': 'Médical',
            'tel': '+22912345003',
            'sexe': 'F',
            'address': '789 Boulevard Assistant, Porto-Novo',
            'hospital': reference_data['hospital'],
            'service': reference_data['service'],
            'speciality': None,
            'language': reference_data['language']
        },
        {
            'group': 'patient',
            'email': '<EMAIL>',
            'username': 'PAT-TEST001',
            'firstname': 'Patient',
            'lastname': 'Exemple',
            'tel': '+22912345004',
            'sexe': 'F',
            'address': '321 Chemin Patient, Porto-Novo',
            'hospital': None,
            'service': None,
            'speciality': None,
            'language': reference_data['language'],
            'birth_date': date(1990, 5, 15),
            'occupation': 'Enseignante',
            'assurance': 'CNSS Bénin'
        }
    ]
    
    created_users = []
    
    for user_data in users_data:
        print(f"\n📝 Création de l'utilisateur {user_data['group']}...")
        
        # Créer l'utilisateur Django
        user = User.objects.create_user(
            username=user_data['username'],
            email=user_data['email'],
            password=test_password,
            first_name=user_data['firstname'],
            last_name=user_data['lastname']
        )
        
        # Ajouter au groupe approprié
        group = Group.objects.get(name=user_data['group'])
        user.groups.add(group)
        
        # Créer le profil
        profile_data = {
            'user': user,
            'firstname': user_data['firstname'],
            'lastname': user_data['lastname'],
            'tel': user_data['tel'],
            'sexe': user_data['sexe'],
            'address': user_data['address'],
            'hospital': user_data['hospital'],
            'service': user_data['service'],
            'speciality': user_data['speciality'],
            'language': user_data['language']
        }
        
        # Ajouter des champs spécifiques aux patients
        if user_data['group'] == 'patient':
            profile_data.update({
                'birth_date': user_data.get('birth_date'),
                'occupation': user_data.get('occupation'),
                'assurance': user_data.get('assurance'),
                'child': False
            })
        
        profile = Profile.objects.create(**profile_data)
        
        # Stocker le profil pour les relations ultérieures
        if user_data['group'] == 'docteur':
            # On créera les relations après avoir créé tous les utilisateurs
            pass
        
        created_users.append({
            'group': user_data['group'],
            'email': user_data['email'],
            'password': test_password,
            'username': user_data['username'],
            'profile_id': profile.id,
            'user_id': user.id
        })
        
        print(f"✅ Utilisateur {user_data['group']} créé avec succès")

    return created_users


def create_doctor_patient_relationships():
    """Crée les relations médecin-patient après la création des utilisateurs."""
    print("\n🏥 Création des relations médecin-patient...")

    try:
        # Récupérer le docteur de test
        doctor_user = User.objects.get(email='<EMAIL>')
        doctor_profile = Profile.objects.get(user=doctor_user)

        # Récupérer le patient de test
        patient_user = User.objects.get(email='<EMAIL>')
        patient_profile = Profile.objects.get(user=patient_user)

        # Créer la relation
        relationship, created = MapDoctorPatient.objects.get_or_create(
            doctor=doctor_profile,
            patient=patient_profile
        )

        if created:
            print("✅ Relation médecin-patient créée")
        else:
            print("ℹ️  Relation médecin-patient déjà existante")

    except (User.DoesNotExist, Profile.DoesNotExist) as e:
        print(f"❌ Erreur lors de la création de la relation: {str(e)}")


def test_authentication(created_users):
    """Teste l'authentification par email pour chaque utilisateur."""
    print("\n🧪 Test de l'authentification par email...")

    success_count = 0
    total_tests = len(created_users)

    for user_info in created_users:
        print(f"\n🔍 Test de {user_info['group']} ({user_info['email']})...")

        # Test 1: Authentification Django
        user = authenticate(email=user_info['email'], password=user_info['password'])
        if user:
            print(f"  ✅ Authentification Django réussie")
            success_count += 1
        else:
            print(f"  ❌ Authentification Django échouée")
            continue

        # Test 2: Test de redirection (simulation)
        client = Client()
        login_data = {
            'email': user_info['email'],
            'password': user_info['password'],
            'g-recaptcha-response': 'test-response'
        }

        response = client.post('/', login_data)
        if response.status_code in [200, 302]:
            print(f"  ✅ Connexion web traitée (status: {response.status_code})")
        else:
            print(f"  ⚠️  Connexion web - status: {response.status_code}")

        # Test 3: API Login
        api_data = {
            'email': user_info['email'],
            'password': user_info['password']
        }

        response = client.post('/api/login/',
                              data=json.dumps(api_data),
                              content_type='application/json')

        if response.status_code == 200:
            response_data = json.loads(response.content)
            if response_data.get('status') == 'success':
                print(f"  ✅ API login réussie")
                print(f"    - Rôle détecté: {response_data.get('role')}")
                print(f"    - Nom: {response_data.get('name')}")
            else:
                print(f"  ❌ API login - réponse incorrecte")
        else:
            print(f"  ❌ API login - status: {response.status_code}")

    print(f"\n📊 Résultat: {success_count}/{total_tests} authentifications Django réussies")
    return success_count == total_tests


def display_user_credentials(created_users):
    """Affiche les identifiants de connexion pour les tests manuels."""
    print("\n" + "="*80)
    print("📋 IDENTIFIANTS DE CONNEXION POUR TESTS MANUELS")
    print("="*80)

    for user_info in created_users:
        print(f"\n🔹 {user_info['group'].upper()}")
        print(f"   Email:     {user_info['email']}")
        print(f"   Mot de passe: {user_info['password']}")
        print(f"   Username:  {user_info['username']}")
        print(f"   User ID:   {user_info['user_id']}")
        print(f"   Profile ID: {user_info['profile_id']}")

    print("\n" + "="*80)
    print("📝 INSTRUCTIONS POUR LES TESTS MANUELS")
    print("="*80)
    print("1. 🌐 Test Interface Web:")
    print("   - Aller sur http://localhost:8000/")
    print("   - Utiliser l'email et le mot de passe ci-dessus")
    print("   - Vérifier la redirection vers la page appropriée")
    print()
    print("2. 🔌 Test API:")
    print("   - POST /api/login/ avec {'email': '...', 'password': '...'}")
    print("   - Vérifier la réponse JSON avec le rôle correct")
    print()
    print("3. 🔄 Test Rétrocompatibilité:")
    print("   - Essayer de se connecter avec le username au lieu de l'email")
    print("   - Les deux méthodes doivent fonctionner")
    print()
    print("4. 🎯 Redirections attendues:")
    print("   - Admin: /admin/")
    print("   - Docteur: /docteur/")
    print("   - Assistant: /assistant/")
    print("   - Patient: /patient/")


def test_role_redirections():
    """Teste que chaque rôle est redirigé vers la bonne page."""
    print("\n🎯 Test des redirections par rôle...")

    from ptcapp.helpers.authentification_helper import go_home

    test_emails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ]

    expected_redirects = {
        'admin': '/admin/',
        'docteur': '/docteur/',
        'assistant': '/assistant/',
        'patient': '/patient/'
    }

    for email in test_emails:
        try:
            user = User.objects.get(email=email)
            group_name = user.groups.first().name if user.groups.exists() else 'unknown'

            # Simuler la redirection
            redirect_response = go_home(user)
            redirect_url = redirect_response.url if hasattr(redirect_response, 'url') else 'unknown'

            expected_url = expected_redirects.get(group_name, 'unknown')

            if redirect_url == expected_url:
                print(f"  ✅ {group_name}: {redirect_url}")
            else:
                print(f"  ❌ {group_name}: attendu {expected_url}, obtenu {redirect_url}")

        except User.DoesNotExist:
            print(f"  ⚠️  Utilisateur {email} non trouvé")


def main():
    """Fonction principale du script."""
    print("🚀 Création des utilisateurs de test pour PTC Care")
    print("="*60)

    try:
        # Étape 1: Préparer les données de référence
        reference_data = ensure_reference_data()

        # Étape 2: Nettoyer les utilisateurs existants
        cleanup_existing_test_users()

        # Étape 3: Créer les nouveaux utilisateurs de test
        created_users = create_test_users(reference_data)

        # Étape 4: Créer les relations médecin-patient
        create_doctor_patient_relationships()

        # Étape 5: Tester l'authentification
        auth_success = test_authentication(created_users)

        # Étape 5: Tester les redirections
        test_role_redirections()

        # Étape 6: Afficher les identifiants pour les tests manuels
        display_user_credentials(created_users)

        # Résumé final
        print("\n" + "="*60)
        print("🎉 CRÉATION DES UTILISATEURS DE TEST TERMINÉE")
        print("="*60)

        if auth_success:
            print("✅ Tous les tests d'authentification ont réussi")
        else:
            print("⚠️  Certains tests d'authentification ont échoué")

        print(f"✅ {len(created_users)} utilisateurs de test créés")
        print("✅ Données de référence vérifiées")
        print("✅ Tests d'authentification effectués")
        print("✅ Identifiants affichés pour tests manuels")

        print("\n🔗 Pour tester l'application:")
        print("   1. Démarrer le serveur: python manage.py runserver")
        print("   2. Aller sur http://localhost:8000/")
        print("   3. Utiliser les identifiants ci-dessus")

    except Exception as e:
        print(f"❌ Erreur lors de la création des utilisateurs: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
