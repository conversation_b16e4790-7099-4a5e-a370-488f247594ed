{% load static %}

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Identification</title>
    <!-- App favicon - Temporarily disabled for Heroku deployment -->
    <!-- <link rel="shortcut icon" href="{% static 'favicon.ico' %}"> -->
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/app.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/login.css' %}">
    <style>
        body{
            background-color: #f6fbff
        }
    </style>

    <!-- reCAPTCHA disabled for Heroku deployment -->
    <!-- <script src="https://www.google.com/recaptcha/api.js" async defer></script> -->
</head>

<body>
    <div class="log-container">
        <div class="log-section mobile-hide">
            <img src="{% static 'images/log-img.jpg' %} " alt="">
        </div>
        <div class="log-section">
            <img src="{% static 'images/logo-light.png' %}" class="logo" alt="">
            <div class="form-section">
                {% comment %} <h1>Identification</h1> {% endcomment %}
                {% if error %}
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{error}}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endif %}
                <form action="{% url 'index' %}" method="post">
                    {% csrf_token %}
                    <div class="form-group">
                        <label class="form-label" for="email">Adresse email</label>
                        <input class="form-control" id="email" name="email" type="email" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="password">Mot de passe</label>
                        <input class="form-control" id="password" name="password" type="password" required>
                    </div>
                    <!-- reCAPTCHA disabled for Heroku deployment -->
                    <!--
                    <div class="d-flex my-2 align-items-center" style="flex-direction:column">
                        <div class="g-recaptcha" data-sitekey="6Lfkq_cfAAAAABJ2hgR1OhIa3NMPuKJqmxkSAyx-"></div>
                        {% if captcha %}
                            <span class="text-danger">{{captcha}}</span>
                        {% endif %}
                    </div>
                    -->
                    <div class="form-group text-center">
                        <input class="btn btn-primary my-3" type="submit" value="S'identifier">
                    </div>
                </form>
            </div>
        </div>
    </div>
</body>

</html>