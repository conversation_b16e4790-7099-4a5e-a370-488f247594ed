{% extends 'patient/layout.html' %} 
{% load static %}
{% load layout %}
{% block up-style %}
    <!-- DataTables -->
    <link href="{% static 'libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css' %}" rel="stylesheet" type="text/css" />
{% endblock up-style %}

{% block content %}
    {% comment %} <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white text-left">
                    <span class="h2 text-capitalize">Filtre des dossiers</span>
                </div>
                <div class="card-body">
                    <form action="#" method="POST">
                        <div class="row">
                            <div class="col-md-3 col-sm-12">
                                <label for="fullname" class="form-label">Nom & Prénoms</label>
                                <input class="form-control" type="text" name="fullname" placeholder="Nom & Prénoms" id="fullname">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="last_appointment" class="form-label">Dernière Consultation</label>
                                <input class="form-control" type="date" name="last_appointment" placeholder="Dernière Consultation" id="last_appointment">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="next_appointment" class="form-label">Prochaine Consultation</label>
                                <input class="form-control" type="date" name="next_appointment" placeholder="Prochaine Consultation" id="next_appointment">
                            </div>
                            <div class="col-md-3 col-sm-12">
                                <label for="next_appointment" class="form-label text-white">Button d'application</label>
                                <input class=" form-control btn btn-success " type="submit" value="Appliquer">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div> {% endcomment %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-center">
                    <span class="h2 text-capitalize text-white"> mes dossiers</span>
                </div>
                <div class="card-body">
                    <table
                        id="datatable"
                        class="table table-striped dt-responsive nowrap"
                        style="border-collapse: collapse; border-spacing: 0; width: 100%"
                        >
                        <thead>
                            <tr>
                                <th></th>
                                <th>Nom & Prénoms</th>
                                <th>Date de création</th>
                                <th>Dernière Consultation</th>
                                <th>Prochaine Consultation</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        <tbody>
                            <tr>
                                <td>Mère</td>
                                <td>{{mother.user|auth_fullname}}</td>
                                <td>{{mother.created_at|date:"Y-m-d"}}</td>
                                <td>{{mother|last_appointment}}</td>
                                <td>{{mother|next_appointment}}</td>
                                <td><a href="{% url 'patient.dossier.show' id=mother.id %}" data-toggle="tooltip" data-placement="right" title="Détails"><i class="ri-eye-line" ></i></a></td>
                            </tr>

                            {% for children in mother_children %}
                                <tr>
                                    <td>Enfant</td>
                                    <td>{{children.child.firstname}} {{children.child.lastname}}</td>
                                    <td>{{children.child.created_at|date:"Y-m-d"}}</td>
                                    <td>{{children.child|last_appointment}}</td>
                                    <td>{{children.child|next_appointment}}</td>
                                    <td><a href="{% url 'patient.dossier.show' id=children.child.id %}" data-toggle="tooltip" data-placement="right" title="Détails"><i class="ri-eye-line" ></i></a></td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}

{% block down-script %}
    <script src="//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json"></script>
    <!-- Required datatable js -->
    <script src="{% static 'libs/datatables.net/js/jquery.dataTables.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js'%}"></script>
    <!-- Responsive examples -->
    <script src="{% static 'libs/datatables.net-responsive/js/dataTables.responsive.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js'%}"></script>
    <!-- Datatable init js -->
    {% comment %} <script src="{% static 'js/pages/datatables.init.js' %}"></script> {% endcomment %}
    <script>
        var dt = $("#datatable").DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
            },
            "columnDefs": [ {
                "targets": [5, 0],
                "orderable": false
            }],
            order : [[1, 'asc']],
            drawCallback: function() {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
    </script>
{% endblock down-script %}