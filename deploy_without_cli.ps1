# Script de déploiement alternatif sans Heroku CLI
# Utilise l'interface web et GitHub

param(
    [Parameter(Mandatory=$true)]
    [string]$AppName
)

Write-Host "🚀 Déploiement PTCCare sans Heroku CLI" -ForegroundColor Green
Write-Host "App Name: $AppName" -ForegroundColor Yellow

# Vérifier Git
try {
    git --version | Out-Null
    Write-Host "✅ Git détecté" -ForegroundColor Green
} catch {
    Write-Host "❌ Git non trouvé. Veuillez l'installer d'abord." -ForegroundColor Red
    exit 1
}

# Initialiser Git si nécessaire
if (-not (Test-Path ".git")) {
    Write-Host "📁 Initialisation de Git..." -ForegroundColor Yellow
    git init
    git add .
    git commit -m "Initial commit - PTC Care application"
}

Write-Host "📋 Instructions de déploiement manuel :" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. 🌐 Aller sur https://dashboard.heroku.com/" -ForegroundColor White
Write-Host "2. 🆕 Cliquer sur 'New' > 'Create new app'" -ForegroundColor White
Write-Host "3. 📝 Nom de l'app: $AppName" -ForegroundColor White
Write-Host "4. 🌍 Région: United States ou Europe" -ForegroundColor White
Write-Host "5. ✅ Cliquer 'Create app'" -ForegroundColor White
Write-Host ""
Write-Host "6. 🔧 Onglet 'Resources' > Add-ons :" -ForegroundColor Yellow
Write-Host "   - Heroku Postgres (Essential-0)" -ForegroundColor White
Write-Host "   - Heroku Redis (Mini)" -ForegroundColor White
Write-Host ""
Write-Host "7. ⚙️ Onglet 'Settings' > Config Vars :" -ForegroundColor Yellow
Write-Host "   SECRET_KEY = $(Get-Random -Minimum 100000000 -Maximum 999999999)" -ForegroundColor White
Write-Host "   DEBUG = False" -ForegroundColor White
Write-Host "   ADMIN_EMAIL = <EMAIL>" -ForegroundColor White
Write-Host "   ADMIN_PASSWORD = VotreMotDePasse123!" -ForegroundColor White
Write-Host "   DEFAULT_FROM_EMAIL = <EMAIL>" -ForegroundColor White
Write-Host "   SMS_ENABLED = False" -ForegroundColor White
Write-Host ""
Write-Host "8. 🚀 Onglet 'Deploy' :" -ForegroundColor Yellow
Write-Host "   - Deployment method: GitHub" -ForegroundColor White
Write-Host "   - Connecter votre repository GitHub" -ForegroundColor White
Write-Host "   - Enable Automatic Deploys (optionnel)" -ForegroundColor White
Write-Host "   - Manual deploy: Deploy Branch (main)" -ForegroundColor White
Write-Host ""

# Créer un repository GitHub si nécessaire
$createGitHub = Read-Host "Voulez-vous créer un repository GitHub maintenant? (y/N)"
if ($createGitHub -eq "y" -or $createGitHub -eq "Y") {
    Write-Host "🐙 Instructions pour GitHub :" -ForegroundColor Cyan
    Write-Host "1. Aller sur https://github.com/new" -ForegroundColor White
    Write-Host "2. Repository name: ptccare-web" -ForegroundColor White
    Write-Host "3. Description: Application de gestion des soins prénataux" -ForegroundColor White
    Write-Host "4. Public ou Private (votre choix)" -ForegroundColor White
    Write-Host "5. Ne pas initialiser avec README (déjà fait)" -ForegroundColor White
    Write-Host "6. Create repository" -ForegroundColor White
    Write-Host ""
    Write-Host "Puis exécuter ces commandes :" -ForegroundColor Yellow
    Write-Host "git remote add origin https://github.com/VOTRE-USERNAME/ptccare-web.git" -ForegroundColor White
    Write-Host "git branch -M main" -ForegroundColor White
    Write-Host "git push -u origin main" -ForegroundColor White
}

Write-Host ""
Write-Host "🎉 Une fois déployé, votre app sera disponible à :" -ForegroundColor Green
Write-Host "https://$AppName.herokuapp.com" -ForegroundColor Cyan
Write-Host ""
Write-Host "📱 API Mobile :" -ForegroundColor Green
Write-Host "https://$AppName.herokuapp.com/api/mobile/" -ForegroundColor Cyan
