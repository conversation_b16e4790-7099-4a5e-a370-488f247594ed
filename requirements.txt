# PTC Care - Requirements pour Production Heroku
# Django et extensions
Django==4.2.16
djangorestframework==3.14.0
django-cors-headers==4.7.0

# Base de données
psycopg2-binary==2.9.9
PyMySQL==1.1.1
dj-database-url==2.1.0

# Authentification et sécurité
PyJWT==2.10.1
cryptography==44.0.2

# Services Google/Firebase
firebase-admin==6.2.0
google-api-core==2.24.2
google-api-python-client==2.164.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-cloud-core==2.4.3
google-cloud-firestore==2.20.1
google-cloud-storage==3.1.0
google-crc32c==1.7.0
google-resumable-media==2.7.2
googleapis-common-protos==1.69.2

# Communication
grpcio==1.71.0
grpcio-status==1.71.0
httplib2==0.22.0

# Traitement de données
numpy==2.2.5
pandas==2.2.3
openpyxl==3.1.5

# Utilitaires
requests==2.32.3
python-dateutil==2.9.0.post0
python-dotenv==1.0.0
pytz==2025.1
certifi==2025.1.31
charset-normalizer==3.4.1
idna==3.10
urllib3==2.3.0

# Parsing et validation
protobuf==5.29.3
proto-plus==1.26.1
pyasn1==0.6.1
pyasn1-modules==0.4.1
pycparser==2.22
pyparsing==3.2.1
rsa==4.9
six==1.17.0
sqlparse==0.5.3

# Cache et performance
CacheControl==0.14.2
cachetools==5.5.2
msgpack==1.1.0

# Autres
asgiref==3.8.1
et-xmlfile==2.0.0
setuptools==76.0.0
tzdata==2025.2
uriTemplate==4.1.1

# Celery pour les tâches asynchrones
celery==5.3.4
redis==5.0.1

# Serveur WSGI pour Heroku
gunicorn==21.2.0
whitenoise==6.6.0

# Configuration DB
dj-database-url==2.1.0

# GSM Modem pour SMS (version compatible)
pyserial==3.5
python-gsmmodem==0.9
