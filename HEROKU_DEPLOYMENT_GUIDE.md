# 🚀 Guide de Déploiement PTCCare sur Heroku

## 📋 Prérequis

1. **Compte Heroku** : [Créer un compte](https://signup.heroku.com/)
2. **Heroku CLI** : [Installer Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli)
3. **Git** : Installé et configuré
4. **Compte GitHub** (optionnel) : Pour le déploiement automatique

## 🔧 Étape 1 : Préparation locale

### 1.1 Vérification des fichiers
Assurez-vous que ces fichiers sont présents :
- ✅ `requirements.txt`
- ✅ `Procfile`
- ✅ `runtime.txt`
- ✅ `app.json`
- ✅ `.gitignore`
- ✅ `ptccare/settings_production.py`

### 1.2 Test local avec les paramètres de production
```bash
# Activer l'environnement virtuel
.\venv\Scripts\Activate.ps1

# Installer les dépendances de production
pip install -r requirements.txt

# Tester avec les paramètres de production
set DYNO=web
python manage.py check --settings=ptccare.settings_production
```

## 🚀 Étape 2 : Déploiement sur Heroku

### 2.1 Connexion à Heroku
```bash
heroku login
```

### 2.2 Création de l'application
```bash
# Créer une nouvelle app Heroku
heroku create ptccare-app-2025

# Ou utiliser un nom spécifique
heroku create votre-nom-app
```

### 2.3 Configuration des add-ons
```bash
# Ajouter PostgreSQL
heroku addons:create heroku-postgresql:essential-0

# Ajouter Redis pour Celery
heroku addons:create heroku-redis:mini

# Vérifier les add-ons
heroku addons
```

### 2.4 Configuration des variables d'environnement
```bash
# Variables essentielles
heroku config:set SECRET_KEY="votre-cle-secrete-tres-longue-et-complexe"
heroku config:set DEBUG=False
heroku config:set ADMIN_EMAIL="<EMAIL>"
heroku config:set ADMIN_PASSWORD="MotDePasseSecurise123!"

# Configuration email (optionnel)
heroku config:set EMAIL_HOST_USER="<EMAIL>"
heroku config:set EMAIL_HOST_PASSWORD="votre-mot-de-passe-app"
heroku config:set DEFAULT_FROM_EMAIL="<EMAIL>"

# Configuration Firebase (optionnel)
heroku config:set FIREBASE_PROJECT_ID="votre-projet-firebase"
heroku config:set FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
heroku config:set FIREBASE_CLIENT_EMAIL="<EMAIL>"

# Désactiver SMS pour la production
heroku config:set SMS_ENABLED=False
```

### 2.5 Initialisation Git et déploiement
```bash
# Initialiser Git (si pas déjà fait)
git init
git add .
git commit -m "Initial commit - PTC Care application"

# Ajouter Heroku comme remote
heroku git:remote -a votre-nom-app

# Déployer
git push heroku main
```

## 🔧 Étape 3 : Configuration post-déploiement

### 3.1 Exécution des migrations
```bash
# Appliquer les migrations
heroku run python manage.py migrate

# Créer le superutilisateur
heroku run python manage.py createsuperuser

# Collecter les fichiers statiques
heroku run python manage.py collectstatic --noinput
```

### 3.2 Chargement des données de test (optionnel)
```bash
# Charger les données de test
heroku run python populate_test_data.py
```

### 3.3 Configuration des workers Celery
```bash
# Démarrer le worker Celery
heroku ps:scale worker=1

# Démarrer le beat scheduler (optionnel)
heroku ps:scale beat=1
```

## 🌐 Étape 4 : Vérification du déploiement

### 4.1 Vérifier l'application
```bash
# Ouvrir l'application
heroku open

# Vérifier les logs
heroku logs --tail

# Vérifier le statut
heroku ps
```

### 4.2 Tester les endpoints API
```bash
# Tester l'API de connexion
curl -X POST https://votre-app.herokuapp.com/api/mobile/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"MotDePasseSecurise123!"}'

# Tester les données initiales
curl https://votre-app.herokuapp.com/api/mobile/initial-data
```

## 🔧 Étape 5 : Configuration du domaine personnalisé (optionnel)

### 5.1 Ajouter un domaine
```bash
# Ajouter votre domaine
heroku domains:add www.ptccare.com
heroku domains:add ptccare.com

# Configurer SSL
heroku certs:auto:enable
```

### 5.2 Configuration DNS
Configurez vos DNS pour pointer vers Heroku :
```
CNAME www.ptccare.com -> votre-app.herokuapp.com
ALIAS ptccare.com -> votre-app.herokuapp.com
```

## 📊 Étape 6 : Monitoring et maintenance

### 6.1 Surveillance des logs
```bash
# Logs en temps réel
heroku logs --tail

# Logs spécifiques
heroku logs --source app
heroku logs --dyno worker
```

### 6.2 Mise à l'échelle
```bash
# Augmenter les dynos web
heroku ps:scale web=2

# Augmenter les workers
heroku ps:scale worker=2
```

### 6.3 Sauvegarde de la base de données
```bash
# Créer une sauvegarde
heroku pg:backups:capture

# Lister les sauvegardes
heroku pg:backups

# Télécharger une sauvegarde
heroku pg:backups:download
```

## 🔄 Étape 7 : Déploiement automatique avec GitHub

### 7.1 Connecter GitHub
1. Aller sur le dashboard Heroku
2. Sélectionner votre app
3. Onglet "Deploy"
4. Connecter à GitHub
5. Activer "Automatic deploys"

### 7.2 Configuration des branches
- **Production** : `main` → Auto-deploy
- **Staging** : `develop` → Review apps

## 🚨 Dépannage

### Problèmes courants

#### Erreur de migration
```bash
heroku run python manage.py migrate --fake-initial
```

#### Problème de fichiers statiques
```bash
heroku run python manage.py collectstatic --clear --noinput
```

#### Erreur de mémoire
```bash
# Passer à un dyno plus puissant
heroku ps:resize web=standard-1x
```

#### Problème de base de données
```bash
# Réinitialiser la base de données
heroku pg:reset DATABASE_URL
heroku run python manage.py migrate
```

## 📱 URLs de l'application déployée

- **Interface web** : `https://votre-app.herokuapp.com/`
- **Admin Django** : `https://votre-app.herokuapp.com/super-admin/`
- **API mobile** : `https://votre-app.herokuapp.com/api/mobile/`
- **Documentation API** : `https://votre-app.herokuapp.com/api/mobile/auth/verify`

## 🎉 Félicitations !

Votre application PTCCare est maintenant déployée sur Heroku avec :
- ✅ Base de données PostgreSQL
- ✅ Cache Redis pour Celery
- ✅ API mobile sécurisée avec JWT
- ✅ Interface d'administration
- ✅ Gestion des fichiers statiques
- ✅ Monitoring et logs

L'application est prête pour la production et l'intégration avec votre application mobile Flutter ! 🚀
