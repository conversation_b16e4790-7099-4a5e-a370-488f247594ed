# Guide de Démarrage Rapide - Intégration Mobile PTC Care

## 🚀 Configuration Initiale (30 minutes)

### 1. Dépendances Requises

#### Android (build.gradle)
```kotlin
dependencies {
    // Networking
    implementation "com.squareup.retrofit2:retrofit:2.9.0"
    implementation "com.squareup.retrofit2:converter-gson:2.9.0"
    implementation "com.squareup.okhttp3:okhttp:4.11.0"
    
    // Sécurité
    implementation "androidx.security:security-crypto:1.1.0-alpha06"
    
    // Base de données
    implementation "androidx.room:room-runtime:2.5.0"
    implementation "androidx.room:room-ktx:2.5.0"
    
    // Coroutines
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3"
    
    // Injection de dépendances
    implementation "com.google.dagger:hilt-android:2.47"
}
```

#### iOS (Package.swift)
```swift
dependencies: [
    .package(url: "https://github.com/Alamofire/Alamofire.git", from: "5.8.0"),
    .package(url: "https://github.com/kishikawakatsumi/KeychainAccess.git", from: "4.2.2"),
    .package(url: "https://github.com/realm/realm-swift.git", from: "10.42.0")
]
```

### 2. Configuration de Base

#### Android - Application Class
```kotlin
@HiltAndroidApp
class PTCCareApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        
        // Configuration des logs en mode debug
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        }
    }
}
```

#### Configuration API
```kotlin
object ApiConfig {
    const val BASE_URL = "http://10.0.2.2:8000" // Pour émulateur Android
    // const val BASE_URL = "https://api.ptccare.com" // Pour production
    
    const val TIMEOUT_SECONDS = 30L
    const val CACHE_SIZE = 10 * 1024 * 1024L // 10 MB
}
```

## 🔐 Implémentation Authentification (45 minutes)

### 1. Gestionnaire d'Authentification

#### Android
```kotlin
@Singleton
class AuthManager @Inject constructor(
    private val apiService: ApiService,
    private val secureStorage: SecureStorage
) {
    suspend fun login(email: String, password: String): AuthResult {
        try {
            val response = apiService.login(LoginRequest(email, password))
            
            if (response.status == "success") {
                secureStorage.saveAuthData(
                    token = "Bearer ${response.userId}",
                    userId = response.userId,
                    role = response.role
                )
                return AuthResult.Success(response)
            }
            
            return AuthResult.Error("Identifiants invalides")
        } catch (e: Exception) {
            return AuthResult.Error(e.message ?: "Erreur de connexion")
        }
    }
    
    fun isLoggedIn(): Boolean = secureStorage.getAuthToken() != null
    
    fun logout() = secureStorage.clearAuthData()
}

sealed class AuthResult {
    data class Success(val user: LoginResponse) : AuthResult()
    data class Error(val message: String) : AuthResult()
}
```

### 2. Interface API

#### Android - Retrofit
```kotlin
interface ApiService {
    @POST("api/login/")
    suspend fun login(@Body request: LoginRequest): LoginResponse
    
    @POST("api/change-password/")
    suspend fun changePassword(@Body request: ChangePasswordRequest): ChangePasswordResponse
    
    @POST("api/create-patient/")
    suspend fun createPatient(@Body request: CreatePatientRequest): CreatePatientResponse
    
    @GET("api/patients/")
    suspend fun getPatients(): PatientsResponse
    
    @GET("api/initial-data/")
    suspend fun getInitialData(): InitialDataResponse
    
    @POST("api/sync-mobile-data/")
    suspend fun syncMobileData(@Body request: SyncRequest): SyncResponse
}

// Configuration Retrofit
@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {
    @Provides
    @Singleton
    fun provideRetrofit(): Retrofit {
        val okHttpClient = OkHttpClient.Builder()
            .addInterceptor(AuthInterceptor())
            .addInterceptor(HttpLoggingInterceptor().apply {
                level = if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY 
                       else HttpLoggingInterceptor.Level.NONE
            })
            .connectTimeout(ApiConfig.TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .readTimeout(ApiConfig.TIMEOUT_SECONDS, TimeUnit.SECONDS)
            .build()
        
        return Retrofit.Builder()
            .baseUrl(ApiConfig.BASE_URL)
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create())
            .build()
    }
    
    @Provides
    @Singleton
    fun provideApiService(retrofit: Retrofit): ApiService = retrofit.create(ApiService::class.java)
}
```

## 📱 Écrans Principaux (60 minutes)

### 1. Écran de Connexion

#### Android (Compose)
```kotlin
@Composable
fun LoginScreen(
    onLoginSuccess: () -> Unit,
    onPasswordChangeRequired: () -> Unit
) {
    var email by remember { mutableStateOf("") }
    var password by remember { mutableStateOf("") }
    var isLoading by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    
    val authManager = hiltViewModel<AuthManager>()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "PTC Care",
            style = MaterialTheme.typography.h4,
            modifier = Modifier.align(Alignment.CenterHorizontally)
        )
        
        Spacer(modifier = Modifier.height(32.dp))
        
        OutlinedTextField(
            value = email,
            onValueChange = { email = it },
            label = { Text("Email ou Username") },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Email),
            modifier = Modifier.fillMaxWidth()
        )
        
        OutlinedTextField(
            value = password,
            onValueChange = { password = it },
            label = { Text("Mot de passe") },
            visualTransformation = PasswordVisualTransformation(),
            modifier = Modifier.fillMaxWidth()
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Button(
            onClick = {
                isLoading = true
                errorMessage = null
                
                // Lancer la connexion
                CoroutineScope(Dispatchers.IO).launch {
                    when (val result = authManager.login(email, password)) {
                        is AuthResult.Success -> {
                            withContext(Dispatchers.Main) {
                                isLoading = false
                                onLoginSuccess()
                            }
                        }
                        is AuthResult.Error -> {
                            withContext(Dispatchers.Main) {
                                isLoading = false
                                errorMessage = result.message
                            }
                        }
                    }
                }
            },
            enabled = email.isNotBlank() && password.isNotBlank() && !isLoading,
            modifier = Modifier.fillMaxWidth()
        ) {
            if (isLoading) {
                CircularProgressIndicator(size = 16.dp, color = Color.White)
            } else {
                Text("Se connecter")
            }
        }
        
        errorMessage?.let { error ->
            Text(
                text = error,
                color = MaterialTheme.colors.error,
                modifier = Modifier.padding(top = 8.dp)
            )
        }
    }
}
```

### 2. Gestion du Changement de Mot de Passe Obligatoire

#### Android - Intercepteur
```kotlin
class AuthInterceptor @Inject constructor(
    private val secureStorage: SecureStorage,
    private val context: Context
) : Interceptor {
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()
        
        // Ajouter le token d'authentification
        val token = secureStorage.getAuthToken()
        val newRequest = if (token != null) {
            originalRequest.newBuilder()
                .addHeader("Authorization", token)
                .addHeader("User-Agent", "PTC-Care-Mobile/1.0")
                .build()
        } else {
            originalRequest
        }
        
        val response = chain.proceed(newRequest)
        
        // Gérer le changement de mot de passe obligatoire
        if (response.code == 403) {
            try {
                val errorBody = response.body?.string()
                val errorResponse = Gson().fromJson(errorBody, ErrorResponse::class.java)
                
                if (errorResponse.error == "password_change_required") {
                    // Rediriger vers l'écran de changement de mot de passe
                    val intent = Intent(context, PasswordChangeActivity::class.java)
                    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                    context.startActivity(intent)
                }
            } catch (e: Exception) {
                // Ignorer les erreurs de parsing
            }
        }
        
        return response
    }
}
```

## 🔄 Synchronisation Hors Ligne (90 minutes)

### 1. Base de Données Locale

#### Android - Room
```kotlin
@Entity(tableName = "patients")
data class LocalPatient(
    @PrimaryKey val mobileId: String = UUID.randomUUID().toString(),
    val serverId: Int? = null,
    val firstname: String,
    val lastname: String,
    val tel: String,
    val email: String,
    val needsSync: Boolean = true,
    val createdAt: Long = System.currentTimeMillis()
)

@Dao
interface PatientDao {
    @Query("SELECT * FROM patients")
    suspend fun getAllPatients(): List<LocalPatient>
    
    @Query("SELECT * FROM patients WHERE needsSync = 1")
    suspend fun getPendingPatients(): List<LocalPatient>
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertPatient(patient: LocalPatient)
    
    @Query("UPDATE patients SET serverId = :serverId, needsSync = 0 WHERE mobileId = :mobileId")
    suspend fun updateServerId(mobileId: String, serverId: Int)
}

@Database(entities = [LocalPatient::class], version = 1)
abstract class AppDatabase : RoomDatabase() {
    abstract fun patientDao(): PatientDao
}
```

### 2. Gestionnaire de Synchronisation

#### Android
```kotlin
@Singleton
class SyncManager @Inject constructor(
    private val apiService: ApiService,
    private val database: AppDatabase,
    private val connectivityManager: ConnectivityManager
) {
    suspend fun syncData(): SyncResult {
        if (!isOnline()) return SyncResult.NoConnection
        
        try {
            val pendingPatients = database.patientDao().getPendingPatients()
            
            if (pendingPatients.isEmpty()) return SyncResult.Success(emptyList())
            
            val syncRequest = SyncRequest(
                patients = pendingPatients.map { it.toSyncPatient() },
                pregnancies = emptyList(),
                appointments = emptyList()
            )
            
            val response = apiService.syncMobileData(syncRequest)
            
            // Mettre à jour les IDs serveur
            response.processed.patients.forEach { processed ->
                if (processed.created) {
                    database.patientDao().updateServerId(
                        processed.mobileId,
                        processed.serverId
                    )
                }
            }
            
            return SyncResult.Success(response.processed.patients)
        } catch (e: Exception) {
            return SyncResult.Error(e.message ?: "Erreur de synchronisation")
        }
    }
    
    private fun isOnline(): Boolean {
        val network = connectivityManager.activeNetwork ?: return false
        val capabilities = connectivityManager.getNetworkCapabilities(network) ?: return false
        return capabilities.hasCapability(NetworkCapabilities.NET_CAPABILITY_INTERNET)
    }
}

sealed class SyncResult {
    data class Success(val synced: List<ProcessedPatient>) : SyncResult()
    object NoConnection : SyncResult()
    data class Error(val message: String) : SyncResult()
}
```

## 📧 Validation des Notifications Email

### Test de Création d'Utilisateur

#### Android
```kotlin
class CreatePatientViewModel @Inject constructor(
    private val apiService: ApiService,
    private val database: AppDatabase
) : ViewModel() {
    
    fun createPatient(patientData: CreatePatientRequest) {
        viewModelScope.launch {
            try {
                val response = apiService.createPatient(patientData)
                
                if (response.status == "success") {
                    // Sauvegarder localement
                    val localPatient = LocalPatient(
                        serverId = response.patientId,
                        firstname = patientData.firstname,
                        lastname = patientData.lastname,
                        tel = patientData.tel,
                        email = patientData.email,
                        needsSync = false // Déjà synchronisé
                    )
                    
                    database.patientDao().insertPatient(localPatient)
                    
                    // Notifier le succès avec statut email
                    _uiState.value = _uiState.value.copy(
                        result = CreateUserResult.Success(
                            username = response.username,
                            emailSent = response.emailSent,
                            message = if (response.emailSent) {
                                "Patient créé avec succès. Email envoyé à ${patientData.email}"
                            } else {
                                "Patient créé mais email non envoyé"
                            }
                        )
                    )
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value.copy(
                    result = CreateUserResult.Error(e.message ?: "Erreur de création")
                )
            }
        }
    }
}
```

## ✅ Checklist de Validation

### Tests Essentiels
- [ ] **Connexion** : Email et username fonctionnent
- [ ] **Changement mot de passe** : Redirection automatique sur 403
- [ ] **Création utilisateur** : Email automatique envoyé
- [ ] **Données hors ligne** : Cache et synchronisation
- [ ] **Sécurité** : Tokens chiffrés, HTTPS activé

### Validation Email
- [ ] **Email reçu** dans la boîte configurée
- [ ] **Lien fonctionnel** de changement de mot de passe
- [ ] **Confirmation** après changement réussi

### Performance
- [ ] **Temps de réponse** < 3 secondes
- [ ] **Cache** : Données disponibles hors ligne
- [ ] **Batterie** : Sync en arrière-plan optimisée

---

**Temps d'implémentation estimé** : 4-6 heures  
**Prérequis** : API PTC Care fonctionnelle + Configuration email  
**Résultat** : Application mobile complètement intégrée avec notifications automatiques
