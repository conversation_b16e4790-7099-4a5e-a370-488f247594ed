from time import time
from django.db import models
from .appointment import Appointment

class Alert(models.Model):
    ENVOYEE = "ENVOYEE"
    ATTENTE = "EN ATTENTE"
    ANNULEE = "ANNULEE"

    APPEL = "APPEL VOCAL"
    MESSAGE = "MESSAGE TEXTE"

    STATES = (
        (ENVOYEE, <PERSON><PERSON>VOYEE),
        (ATTENTE, ATTENTE),
        (ANNULEE, ANNULEE)
    )

    TYPES = (
        (APPEL, APPEL),
        (MESSAGE, MESSAGE),
    )
    
    type = models.CharField(max_length=100, choices=STATES, default=ATTENTE)
    state = models.CharField(max_length=100, choices=TYPES, default=ATTENTE)
    appointment = models.ForeignKey(Appointment,null=True, on_delete= models.SET_NULL)
    task_id = models.CharField(max_length=100, null=True)
    date = models.DateField(null=True)
    time = models.TimeField(null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
