{% extends 'direction/layout.html' %} 
{% load static %}

{% block up-style %}
    <!-- DataTables -->
    <link href="{% static 'libs/select2/css/select2.min.css' %}" rel="stylesheet" type="text/css" />
    <link href="{% static 'libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css' %}" rel="stylesheet" type="text/css" />
    <style>
        label.required::after{
            content: "*";
            color: red;
            padding-left: 3px
        }
        .action a{
            margin: 0 10px;
        }
    </style>
{% endblock up-style %} 
{% block action_button %}
<a href="javascript:;" id="addHospital" 
data-bs-toggle="modal" 
data-bs-target=".bs-example-modal-center" class="btn btn-primary mx-3">Nouvel Hôpital</a>
{% endblock action_button %}
{% block content %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-center">
                    <span class="h2 text-capitalize text-white">Liste des Hôpitaux</span>
                </div>
                <div class="card-body">
                    <table
                        id="datatable"
                        class="table table-striped dt-responsive nowrap"
                        style="border-collapse: collapse; border-spacing: 0; width: 100%"
                        >
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Contact</th>
                                <th>Adresse</th>
                                <th>Nombre d'Agents</th>
                                <th>Action</th>
                            </tr>
                        </thead>

                        <tbody>
                            {% for hospital in hospitals %}
                            <tr>
                                <td>{{hospital.name}}</td>
                                <td>{{hospital.tel}}</td>
                                <td>{{hospital.address}}</td>
                                <td>{{hospital.profile_set.all|length}}</td>
                                <td class="action">
                                    <a href="javascript:;" 
                                    class="edit-hospital"
                                    data-hospital-id="{{hospital.pk}}" 
                                    data-hospital-name="{{hospital.name}}" 
                                    data-hospital-contact="{{hospital.tel}}"   
                                    data-hospital-address="{{hospital.address}}"   
                                    data-bs-toggle="modal" 
                                    data-bs-target=".bs-example-modal-center"
                                    data-toggle="tooltip" data-placement="right" title="Modifier"><i class="ri-pencil-fill" ></i></a>
                                    <a href="javascript:;" class="delete-hospital" data-hospital-id="{{hospital.pk}}" data-toggle="tooltip" data-placement="right" title="Supprimer"><i class=" ri-delete-bin-fill" ></i></a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade bs-example-modal-center" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modal-title">---</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="#" method="post">
                        {% csrf_token %}
                        <div class="row">
                            <div class="col-md-12 col-sm-12 mt-4">
                                {% comment %} <fieldset class="h-100"> {% endcomment %}
                                    {% comment %} <legend>Informations Générales</legend> {% endcomment %}
                                    <input type="hidden" name="_method" id="method" value="post">
                                    <input type="hidden" name="hospital_id" id="hospital_id" value="">
                                    <div class="my-3">
                                        <label for="name" class="form-label required">Nom</label>
                                        <input class="form-control" type="text" name="name" placeholder="Nom" id="name" required="required">
                                    </div>
                                    <div class="my-3">
                                        <label for="contact" class="form-label required">Contact</label>
                                        <input class="form-control" type="text" name="contact" placeholder="Contact" id="contact" required="required">
                                    </div>
                                    <div class="my-3">
                                        <label for="address" class="form-label required">Adresse</label>
                                        <input class="form-control" type="text" name="address" placeholder="Adresse" id="address" required="required">
                                    </div>
                                {% comment %} </fieldset> {% endcomment %}
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col">
                                <input class="btn btn-success w-auto" id="form-submit" type="submit" value="Sauvegarder">
                            </div>
                        </div>
                    </form>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div><!-- /.modal -->
{% endblock content %}

{% block down-script %}
    <script src="//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json"></script>
    <script src="{% static 'libs/select2/js/select2.min.js'%}"></script>
    <!-- Required datatable js -->
    <script src="{% static 'libs/datatables.net/js/jquery.dataTables.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js'%}"></script>
    <!-- Responsive examples -->
    <script src="{% static 'libs/datatables.net-responsive/js/dataTables.responsive.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js'%}"></script>
    <!-- Datatable init js -->
    {% comment %} <script src="{% static 'js/pages/datatables.init.js' %}"></script> {% endcomment %}
    <script>
        var dt = $("#datatable").DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
            },
            "columnDefs": [ {
                "targets": [4],
                "orderable": false
            }],
            order : [[0, 'asc']],
            drawCallback: function() {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        $('.select2').select2()
        
        $('#addHospital').click(function(){
            $('#modal-title').text('Créer Hôpital')
            $('#method').val('post')
            $('#name').val('')
            $('#contact').val('')
            $('#address').val('')
        })

        $('.edit-hospital').click(function(){
            $('#modal-title').text('Editer Hôpital')
            $('#method').val('put')
            $('#hospital_id').val($(this).data('hospitalId'))
            $('#name').val($(this).data('hospitalName'))
            $('#contact').val($(this).data('hospitalContact'))
            $('#address').val($(this).data('hospitalAddress'))
        })

        $('#form-submit').click(function(e){
            e.preventDefault()
            if($('#method').val() == 'put'){
                var url = "/hospital/update/"+$('#hospital_id').val()
            }else if($('#method').val() == 'post'){
                var url = "{% url 'hospital.create' %}"
            }
            $.post(
                url,
                {
                    csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val(),
                    name : $('#name').val(),
                    contact : $('#contact').val(),
                    address : $('#address').val()
                },
                function(response){
                    if(response.success){
                        $('.bs-example-modal-center').modal('toggle');
                        if($('#method').val() == 'put'){
                            Swal.fire("Mis à jour!", "", "success");
                        }else if($('#method').val() == 'post'){
                            Swal.fire("Création!", "", "success");
                        }
                        window.location.reload()
                    }
                }
            );
        })
        
        $(".delete-hospital").click(function() {
            id = $(this).data('hospitalId')
            Swal.fire({
                title: "Voulez-vous vraiment supprimer cet hôpital ?",
                text: "Cet hôpital sera définitivement supprimer !",
                icon: "warning",
                showCancelButton: !0,
                confirmButtonColor: "#1cbb8c",
                cancelButtonColor: "#ff3d60",
                confirmButtonText: "Oui, supprimer!",
                cancelButtonText: "Annuler",
            }).then(function(t) {
                if(t.value){
                    $.post(
                        "/hospital/delete/"+id,
                        {
                            csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val(),
                        },
                        function(response){
                            if(response.success){
                                Swal.fire("Supprimer!", "", "success");
                                window.location.reload()
                            }
                        }
                    );
                }
            });
        })
    </script>
{% endblock down-script %}