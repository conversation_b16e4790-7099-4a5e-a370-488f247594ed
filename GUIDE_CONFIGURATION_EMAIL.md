# Guide de Configuration - Système de Notification Email

## 📧 Configuration SMTP

### 1. Variables d'Environnement

Créez un fichier `.env` à la racine du projet avec les paramètres SMTP :

```bash
# Configuration Email
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=votre-mot-de-passe-app
PTCCARE_BASE_URL=https://votre-domaine.com

# Pour Gmail, utilisez un mot de passe d'application
# https://support.google.com/accounts/answer/185833
```

### 2. Configuration pour Gmail

```python
# Dans ptccare/settings.py (d<PERSON><PERSON><PERSON> configuré)
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '<EMAIL>')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = 'PTC Care <<EMAIL>>'
```

### 3. Configuration pour Autres Fournisseurs

#### Outlook/Hotmail
```python
EMAIL_HOST = 'smtp-mail.outlook.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
```

#### Yahoo
```python
EMAIL_HOST = 'smtp.mail.yahoo.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
```

#### Serveur SMTP Personnalisé
```python
EMAIL_HOST = 'mail.votre-domaine.com'
EMAIL_PORT = 587  # ou 465 pour SSL
EMAIL_USE_TLS = True  # ou EMAIL_USE_SSL = True
```

## 🔧 Configuration de Production

### 1. Variables d'Environnement de Production

```bash
# .env.production
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=mot-de-passe-securise
PTCCARE_BASE_URL=https://ptccare.com
PASSWORD_RESET_TOKEN_EXPIRY_HOURS=24
```

### 2. Configuration de Sécurité

```python
# Dans settings.py pour la production
if not DEBUG:
    EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
    # Configuration SMTP sécurisée
else:
    # Pour le développement, utiliser la console
    EMAIL_BACKEND = 'django.core.mail.backends.console.EmailBackend'
```

## 🧪 Test de Configuration

### 1. Test Manuel

```python
# Dans le shell Django
python manage.py shell

from django.core.mail import send_mail
from django.conf import settings

send_mail(
    'Test PTC Care',
    'Ceci est un test d\'envoi d\'email.',
    settings.DEFAULT_FROM_EMAIL,
    ['<EMAIL>'],
    fail_silently=False,
)
```

### 2. Test avec le Système

```python
# Créer un utilisateur de test
python manage.py shell

from django.contrib.auth.models import User, Group
from ptcapp.services.email_service import EmailNotificationService

# Créer un utilisateur
user = User.objects.create_user(
    username='TEST-12345678',
    email='<EMAIL>',
    password='test123'
)
group = Group.objects.get(name='patient')
user.groups.add(group)

# Tester l'envoi
success, message = EmailNotificationService.send_new_user_credentials(
    user, 'test123', 'Patient'
)
print(f"Succès: {success}, Message: {message}")

# Nettoyer
user.delete()
```

## 🎨 Personnalisation des Templates

### 1. Modifier les Templates Email

Les templates se trouvent dans `ptcapp/templates/emails/` :

- `new_user_credentials.html` - Template HTML
- `new_user_credentials.txt` - Template texte
- `password_changed_confirmation.html` - Confirmation HTML
- `password_changed_confirmation.txt` - Confirmation texte

### 2. Variables Disponibles

Dans les templates, vous avez accès à :

```django
{{ user.username }}          # Nom d'utilisateur
{{ user.email }}             # Email
{{ full_name }}              # Nom complet
{{ password }}               # Mot de passe temporaire
{{ role_name }}              # Rôle (Médecin, Patient, etc.)
{{ password_change_url }}    # URL de changement
{{ token_expiry_hours }}     # Heures d'expiration
{{ support_email }}          # Email de support
```

### 3. Personnaliser les Styles

Modifiez les styles CSS dans les templates HTML pour correspondre à votre charte graphique.

## 🔒 Sécurité

### 1. Mots de Passe d'Application

Pour Gmail, utilisez des mots de passe d'application :
1. Activez la vérification en 2 étapes
2. Générez un mot de passe d'application
3. Utilisez ce mot de passe dans `EMAIL_HOST_PASSWORD`

### 2. Chiffrement

- Utilisez toujours `EMAIL_USE_TLS = True` ou `EMAIL_USE_SSL = True`
- Ne stockez jamais les mots de passe en dur dans le code
- Utilisez des variables d'environnement

### 3. Limitation de Débit

Configurez des limites d'envoi pour éviter le spam :

```python
# Dans settings.py
EMAIL_RATE_LIMIT = 100  # emails par heure
```

## 📱 Configuration Mobile/API

### 1. Réponses API

Les APIs retournent maintenant des informations sur l'envoi d'email :

```json
{
    "status": "success",
    "message": "Patient créé avec succès",
    "patient_id": 123,
    "username": "PAT-12345678",
    "password": "temporaire123",
    "email_sent": true,
    "email_error": null
}
```

### 2. Gestion des Erreurs

Si l'envoi d'email échoue :

```json
{
    "status": "success",
    "message": "Patient créé avec succès",
    "email_sent": false,
    "email_error": "Erreur SMTP: ..."
}
```

## 🚀 Déploiement

### 1. Variables d'Environnement

Configurez ces variables sur votre serveur :

```bash
export EMAIL_HOST_USER=<EMAIL>
export EMAIL_HOST_PASSWORD=mot-de-passe-securise
export PTCCARE_BASE_URL=https://ptccare.com
```

### 2. Test de Production

Après déploiement, testez :

1. Création d'un utilisateur via l'interface web
2. Création d'un utilisateur via l'API
3. Réception de l'email
4. Changement de mot de passe via le lien
5. Connexion avec le nouveau mot de passe

## 📞 Support

### 1. Logs d'Erreur

Les erreurs d'email sont loggées. Vérifiez les logs Django :

```python
import logging
logger = logging.getLogger(__name__)
# Les erreurs apparaissent dans les logs
```

### 2. Dépannage Courant

| Problème | Solution |
|----------|----------|
| Authentification échouée | Vérifiez EMAIL_HOST_USER et EMAIL_HOST_PASSWORD |
| Connexion refusée | Vérifiez EMAIL_HOST et EMAIL_PORT |
| Emails non reçus | Vérifiez les dossiers spam/indésirables |
| Token expiré | Augmentez PASSWORD_RESET_TOKEN_EXPIRY_HOURS |

### 3. Contact

Pour toute assistance technique :
- Email : <EMAIL>
- Documentation : [Lien vers la documentation]

---

**Note :** Ce système est maintenant entièrement opérationnel et prêt pour la production après configuration SMTP appropriée.
