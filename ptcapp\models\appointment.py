from django.db import models

from ptcapp.models.profile import Profile
from .record import Record


class Appointment(models.Model):
    ATTENTE = "EN ATTENTE"
    MANQUEE = "MANQUEE"
    ANNULEE = "ANNULEE"
    EFFECTUEE = "EFFECTUEE"

    STATES = (
        (ATTENTE, ATTENTE),
        (MANQUEE, MANQUEE),
        (ANNULEE, ANNULEE),
        (EFFECTUEE, EFFECTUEE),
    )
    
    consul_date = models.DateField()
    consul_hour = models.TimeField()
    # record = models.ForeignKey(Record,null=True, on_delete= models.SET_NULL)
    appointment_type = models.Char<PERSON>ield(max_length=50)
    state = models.CharField(max_length=20, choices=STATES, default=ATTENTE)
    patient = models.ForeignKey(Profile, null=True, on_delete= models.SET_NULL)
    doctor = models.ForeignKey(Profile, null=True, related_name='doctor_of', on_delete= models.SET_NULL)
    # illness = models.Char<PERSON>ield(max_length = 150)
    consul_data = models.TextField(max_length= 500, null=True)
    consul_resume = models.TextField(max_length= 500, null=True)
    consul_decisions = models.TextField(max_length= 500, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)  

