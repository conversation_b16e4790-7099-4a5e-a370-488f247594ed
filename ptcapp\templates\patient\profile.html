{% extends 'patient/layout.html' %} 
{% load static %} 
{% load layout %} 
{% block up-style %}
<!-- DataTables -->
<link href="{% static 'libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css' %}" rel="stylesheet" type="text/css" /> 
<style>
    #imgProfil{

        min-width: 300px !important;
        min-height: 300px !important;
        max-width: 300px !important;
        max-height: 300px !important;
    }
</style>
{% endblock up-style %} 
{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white text-left">
                <span class="h2 text-capitalize">Mon profil</span>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8 col-sm-12">
                        <div class="row">
                            <div class="col-md-6 col-sm-12">
                                <h4>Nom & Prénom</h4>
                                <p>{{request.user|auth_fullname}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Adresse</h4>
                                <p>{{profile.address}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Email</h4>
                                <p>{{request.user.email}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h4>Téléphone</h4>
                                <p>{{profile.tel}}</p>
                            </div>
                        </div>
                        <div class="row">
                            <form action="#" method="POST">
                                {% csrf_token %}
                                <div class="row">
                                    <div class="col-md-6 col-sm-12">
                                        <label for="username" class="form-label">Identifiant</label>
                                        <input class="form-control" type="text" name="username" value="{{request.user.username}}" id="username" disabled>
                                        <input class="form-control" type="hidden" name="username" value="{{request.user.username}}" id="username">
                                    </div>
                                    <div class="col-md-6 col-sm-12">
                                        <label for="current_password" class="form-label required">Mot de passe actuel</label>
                                        <input class="form-control" type="password" name="old_password" placeholder="Mot de passe" id="old_password" required>
                                    </div>
                                    <div class="col-md-6 col-sm-12">
                                        <label for="password" class="form-label required">Nouveau mot de passe</label>
                                        <input class="form-control" type="password" name="password" placeholder="Nouveau mot de passe" id="password" required>
                                    </div>
                                    <div class="col-md-6 col-sm-12">
                                        <label for="password_confirmation" class="form-label required">Confirmer nouveau mot de passe</label>
                                        <input class="form-control" type="password" name="password_confirmation" placeholder="Confirmer mot de passe" id="password_confirmation" required>
                                    </div>
                                </div>
                                <div class="row justify-content-center my-4">
                                        {% comment %} <label for="next_appointment" class="form-label text-white">Button d'application</label> {% endcomment %}
                                        <input class="btn btn-success btn-md w-auto" id="modify_credential" type="submit" value="Modifier informations de connexion">
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-12">
                        <div class="d-flex align-items-center justify-content-center">
                            <form name="profile_pic" action="#" method="post" enctype="multipart/form-data">
                                {% csrf_token %}
                                <input type="hidden" name="_method" id="method" value="post">
                                <input type="hidden" name="profile_id" id="profile_id" value="">
                                <img {% if profile.picture == NULL %} src="{% static 'images/default-profile.png' %}" {% else %} src="{% static profile.picture %}" {% endif %} class="rounded-circle w-100 h-100" alt="img-7" id="imgProfil" onclick="openImage()">
                                <input type="file" name="profil" accept="image/*" id="imgInp"  hidden> 
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock content %} 
{% block down-script %}
<script src="//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json"></script>
<!-- Required datatable js -->
<script src="{% static 'libs/datatables.net/js/jquery.dataTables.min.js'%}"></script>
<script src="{% static 'libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js'%}"></script>
<!-- Responsive examples -->
<script src="{% static 'libs/datatables.net-responsive/js/dataTables.responsive.min.js'%}"></script>
<script src="{% static 'libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js'%}"></script>
<!-- Datatable init js -->
{% comment %}name=""
<script src="{% static 'js/pages/datatables.init.js' %}"></script> {% endcomment %}
<script>
    var dt = $("#datatable").DataTable({
        language: {
            url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
        },
        "columnDefs": [{
            "targets": [5, 0],
            "orderable": false
        }],
        order: [
            [1, 'asc']
        ],
        drawCallback: function() {
            $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
        },
    });
    $(function() {
        $('[data-toggle="tooltip"]').tooltip()
    })

    function openImage() {
        document.getElementById("imgInp").click();
    }

    const sendBlob = async function(blobURL, url) {
        
        let blob = await fetch(blobURL).then(r => r.blob());
        
        var form = new FormData();
        form.append('csrfmiddlewaretoken', $('input[name="csrfmiddlewaretoken"]').val());
        form.append('imgInp', blob);
        //console.log(blob);
        $.ajax({
            url: url,
            type: 'POST',
            data: form,
            processData: false,
            contentType: false,
            success: function (response) {
                if(response.success){
                    $('#method').val('put')
                    if($('#method').val() == 'put'){
                        Swal.fire("Mis à jour!", "", "success");
                    }else if($('#method').val() == 'post'){
                        Swal.fire("Création!", "", "success");
                    }
                    window.location.reload();
                }
            },
            error: function () {
                // handle error case here
            }
        });
    }


    $('input[name=profil]').change( function () {
        if($('#method').val() == 'put'){
            var url = "/profile/picture/"+"{{profile.pk}}"
        }else if($('#method').val() == 'post'){
            var url = "/profile/picture/"+"{{profile.pk}}"
        }
        const input = document.querySelector('#imgInp');
        const file = input.files[0];
        const urls = URL.createObjectURL(file);

        sendBlob(urls, url);
    });


    $('#modify_credential').click(function(e){
        e.preventDefault()
        if($("#password").val() && $("#password").val() != $("#password_confirmation").val()){
            Swal.fire({
                position: "top-end",
                text: "Nouveau mot de passe et confirmation non identique.",
                showConfirmButton: !1,
                timer: 1500,
                background: "#f27474"
            });
        }else{
            $.post(
                "{% url 'profile.credential' %}",
                {
                    csrfmiddlewaretoken: $('input[name="csrfmiddlewaretoken"]').val(),
                    old_password : $('#old_password').val(),
                    password : $('#password').val()
                },
                function(response){
                    if(response.success){
                        Swal.fire({
                            position: "top-end",
                            text: "Mot de passe modifié avec succès",
                            showConfirmButton: !1,
                            timer: 1500,
                            background:"rgba(63,255,106,0.69)"
                        });
                        setTimeout( function(){
                            window.location.reload()
                        },1600)
                    }else{
                        Swal.fire({
                            position: "top-end",
                            text: "Mot de passe actuel incorrect",
                            showConfirmButton: !1,
                            timer: 1500,
                            background: "#f27474"
                        });
                    }
                }
            );
        }
    })
</script>
{% endblock down-script %}