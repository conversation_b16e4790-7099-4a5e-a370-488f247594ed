from .. import views
from django.urls import path

urlpatterns = [
    path('', views.views.adminIndex, name='admin.index'),
    path('personnels', views.views.indexFilters, name='admin.indexFilters'),
    path('hopitaux', views.views.hospital, name='admin.hospital'),
    path('services', views.views.service, name='admin.service'),
    path('languages', views.views.language, name='admin.language'),
    path('patients/', views.views.patients, name='admin.patient'),
    path('patients', views.views.adminFilters, name='admin.filters'),
    path('specialite', views.views.speciality, name='admin.speciality'),
    path('profil', views.views.adminProfile, name='admin.profile'),
    path('personnel/create', views.views.createPersonnel, name='admin.personnel.create'),
    path('personnel/edit/<id>', views.views.editPersonnel, name='admin.personnel.edit'),
    path('personnel/show/<id>', views.views.showPersonnel, name='admin.personnel.show'),

    path('patient/create', views.views.adminCreatePatient, name='admin.patient.create'),
    path('patient/edit/<id>', views.views.adminEditPatient, name='admin.patient.edit'),
    path('patient/show/<id>', views.views.adminShowRecord, name='admin.record.show'),
]