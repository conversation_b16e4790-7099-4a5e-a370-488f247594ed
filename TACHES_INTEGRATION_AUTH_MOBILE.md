# Tâches d'Intégration Authentification Mobile Flutter

## 🎯 Objectif

Intégrer l'authentification PTC Care dans votre application Flutter existante **sans modifier l'UI actuelle**, permettant l'interopérabilité entre mobile et web.

## 📋 Plan d'Exécution (Ordre de Priorité)

### Phase 1 : Configuration API (2-3h)
### Phase 2 : Intégration Authentification (3-4h)  
### Phase 3 : Tests et Validation (1-2h)

---

## 🔧 PHASE 1 : Configuration API

### Tâche 1.1 : Ajouter les Dépendances (15 min)

**Fichier :** `pubspec.yaml`

```yaml
dependencies:
  # Ajoutez ces packages (gardez vos packages existants)
  dio: ^5.3.2
  json_annotation: ^4.8.1
  flutter_secure_storage: ^9.0.0
  
dev_dependencies:
  # Pour la génération de code
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
```

**Action :** `flutter pub get`

### Tâche 1.2 : Configuration Serveur Local (10 min)

**Fichier :** `lib/config/api_config.dart` (nouveau)

```dart
class ApiConfig {
  // Configuration pour serveur local Django
  static const String baseUrl = 'http://********:8000'; // Android Emulator
  // static const String baseUrl = 'http://localhost:8000'; // iOS Simulator
  
  // Headers par défaut
  static const Map<String, String> defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };
  
  // Endpoints PTC Care
  static const String loginEndpoint = '/api/login/';
  static const String changePasswordEndpoint = '/api/change-password/';
  static const String createPatientEndpoint = '/api/create-patient/';
  static const String createAgentEndpoint = '/api/create-health-agent/';
  static const String syncDataEndpoint = '/api/sync-mobile-data/';
}
```

### Tâche 1.3 : Modèles de Données API (30 min)

**Fichier :** `lib/models/ptc_auth_models.dart` (nouveau)

```dart
import 'package:json_annotation/json_annotation.dart';

part 'ptc_auth_models.g.dart';

@JsonSerializable()
class PTCLoginRequest {
  final String email;
  final String password;

  PTCLoginRequest({required this.email, required this.password});

  factory PTCLoginRequest.fromJson(Map<String, dynamic> json) =>
      _$PTCLoginRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PTCLoginRequestToJson(this);
}

@JsonSerializable()
class PTCLoginResponse {
  final String status;
  @JsonKey(name: 'user_id')
  final int userId;
  final String username;
  @JsonKey(name: 'profile_id')
  final int profileId;
  final String role; // "admin", "agent", "patient"
  final String name;
  @JsonKey(name: 'hospital_id')
  final int? hospitalId;

  PTCLoginResponse({
    required this.status,
    required this.userId,
    required this.username,
    required this.profileId,
    required this.role,
    required this.name,
    this.hospitalId,
  });

  factory PTCLoginResponse.fromJson(Map<String, dynamic> json) =>
      _$PTCLoginResponseFromJson(json);
  Map<String, dynamic> toJson() => _$PTCLoginResponseToJson(this);
}

@JsonSerializable()
class PTCApiError {
  final String error;
  final String? message;

  PTCApiError({required this.error, this.message});

  factory PTCApiError.fromJson(Map<String, dynamic> json) =>
      _$PTCApiErrorFromJson(json);
  Map<String, dynamic> toJson() => _$PTCApiErrorToJson(this);
}
```

**Action :** `flutter packages pub run build_runner build`

### Tâche 1.4 : Service HTTP PTC Care (45 min)

**Fichier :** `lib/services/ptc_api_service.dart` (nouveau)

```dart
import 'package:dio/dio.dart';
import '../config/api_config.dart';
import '../models/ptc_auth_models.dart';

class PTCApiService {
  late final Dio _dio;

  PTCApiService() {
    _dio = Dio(BaseOptions(
      baseUrl: ApiConfig.baseUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: ApiConfig.defaultHeaders,
    ));

    // Intercepteur pour logs (développement)
    _dio.interceptors.add(LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (object) => print('PTC API: $object'),
    ));
  }

  // Authentification PTC Care
  Future<PTCLoginResponse> login(String email, String password) async {
    try {
      final request = PTCLoginRequest(email: email, password: password);
      final response = await _dio.post(
        ApiConfig.loginEndpoint,
        data: request.toJson(),
      );

      return PTCLoginResponse.fromJson(response.data);
    } on DioException catch (e) {
      if (e.response?.data != null) {
        final error = PTCApiError.fromJson(e.response!.data);
        throw PTCAuthException(error.error, e.response!.statusCode);
      }
      throw PTCAuthException('Erreur de connexion', null);
    }
  }

  // Changement de mot de passe
  Future<bool> changePassword({
    required String email,
    String? currentPassword,
    required String newPassword,
    String? token,
  }) async {
    try {
      final data = {
        'email': email,
        'new_password': newPassword,
        if (currentPassword != null) 'current_password': currentPassword,
        if (token != null) 'token': token,
      };

      final response = await _dio.post(
        ApiConfig.changePasswordEndpoint,
        data: data,
      );

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  // Vérifier la connectivité avec l'API
  Future<bool> checkApiHealth() async {
    try {
      final response = await _dio.get('/api/initial-data/');
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}

// Exception personnalisée
class PTCAuthException implements Exception {
  final String message;
  final int? statusCode;

  PTCAuthException(this.message, this.statusCode);

  @override
  String toString() => 'PTCAuthException: $message (Status: $statusCode)';
}
```

---

## 🔐 PHASE 2 : Intégration Authentification

### Tâche 2.1 : Adapter Votre Modèle Utilisateur Existant (30 min)

**Fichier :** Votre modèle utilisateur existant (ex: `lib/models/user.dart`)

```dart
// AJOUTEZ ces champs à votre classe User existante
class User {
  // Vos champs existants...
  final String? username;
  final String? email;
  
  // NOUVEAUX CHAMPS PTC Care
  final bool isPTCUser;           // Flag pour identifier les utilisateurs PTC
  final String? ptcToken;         // Token d'authentification PTC
  final int? ptcUserId;           // ID utilisateur PTC
  final int? ptcProfileId;        // ID profil PTC
  final String? ptcRole;          // Rôle PTC ("admin", "agent", "patient")
  final int? hospitalId;          // ID hôpital PTC
  
  User({
    // Vos paramètres existants...
    this.isPTCUser = false,
    this.ptcToken,
    this.ptcUserId,
    this.ptcProfileId,
    this.ptcRole,
    this.hospitalId,
  });

  // AJOUTEZ cette méthode de mapping PTC
  factory User.fromPTCResponse(PTCLoginResponse ptcResponse, String email) {
    return User(
      username: ptcResponse.username,
      email: email,
      isPTCUser: true,
      ptcToken: 'Bearer ${ptcResponse.userId}',
      ptcUserId: ptcResponse.userId,
      ptcProfileId: ptcResponse.profileId,
      ptcRole: ptcResponse.role,
      hospitalId: ptcResponse.hospitalId,
      // Mapper vers vos champs existants selon vos besoins
      // ex: role: _mapPTCRole(ptcResponse.role),
    );
  }

  // AJOUTEZ cette méthode pour vérifier les permissions
  bool get canAccessAdminFeatures => isPTCUser && (ptcRole == 'admin' || ptcRole == 'docteur');
  bool get canAccessAgentFeatures => isPTCUser && (ptcRole == 'admin' || ptcRole == 'docteur' || ptcRole == 'assistant');
}
```

### Tâche 2.2 : Adapter Votre Service d'Authentification (1h)

**Fichier :** Votre service auth existant (ex: `lib/services/auth_service.dart`)

```dart
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'ptc_api_service.dart';

class AuthService {
  // Vos propriétés existantes...
  final PTCApiService _ptcApiService = PTCApiService();
  static const _storage = FlutterSecureStorage();
  
  // AJOUTEZ cette méthode d'authentification PTC
  Future<AuthResult> authenticateWithPTC(String email, String password) async {
    try {
      // Vérifier d'abord la connectivité API
      final isApiAvailable = await _ptcApiService.checkApiHealth();
      if (!isApiAvailable) {
        return AuthResult.error('API PTC Care non disponible');
      }

      // Tentative de connexion PTC Care
      final ptcResponse = await _ptcApiService.login(email, password);
      
      // Créer l'utilisateur à partir de la réponse PTC
      final user = User.fromPTCResponse(ptcResponse, email);
      
      // Sauvegarder dans votre système existant
      await _saveUserSession(user);
      
      return AuthResult.success(user);
      
    } on PTCAuthException catch (e) {
      if (e.statusCode == 401) {
        return AuthResult.error('Email ou mot de passe incorrect');
      } else if (e.statusCode == 404) {
        return AuthResult.error('Utilisateur non trouvé dans PTC Care');
      } else {
        return AuthResult.error(e.message);
      }
    } catch (e) {
      return AuthResult.error('Erreur de connexion: $e');
    }
  }

  // MODIFIEZ votre méthode de login existante
  Future<AuthResult> login(String email, String password) async {
    // 1. Essayer d'abord PTC Care
    final ptcResult = await authenticateWithPTC(email, password);
    
    if (ptcResult.isSuccess) {
      return ptcResult;
    }
    
    // 2. Si échec PTC, essayer votre système local existant
    if (ptcResult.error.contains('non trouvé') || ptcResult.error.contains('incorrect')) {
      return await _authenticateLocally(email, password);
    }
    
    // 3. Si erreur API, utiliser uniquement le local
    if (ptcResult.error.contains('non disponible')) {
      return await _authenticateLocally(email, password);
    }
    
    return ptcResult;
  }

  // GARDEZ votre méthode d'authentification locale existante
  Future<AuthResult> _authenticateLocally(String email, String password) async {
    // Votre logique d'authentification locale existante
    // ...
  }

  // AJOUTEZ cette méthode pour le changement de mot de passe PTC
  Future<bool> changePTCPassword({
    required String email,
    String? currentPassword,
    required String newPassword,
    String? token,
  }) async {
    return await _ptcApiService.changePassword(
      email: email,
      currentPassword: currentPassword,
      newPassword: newPassword,
      token: token,
    );
  }
}
```

### Tâche 2.3 : Adapter Votre Écran de Connexion Existant (45 min)

**Fichier :** Votre écran de login existant

```dart
// AJOUTEZ cette méthode à votre écran de connexion existant
class _YourExistingLoginScreenState extends State<YourExistingLoginScreen> {
  
  // AJOUTEZ cet indicateur de mode PTC
  bool _isPTCMode = false;
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // Votre UI existante...
      body: Column(
        children: [
          // Vos widgets existants...
          
          // AJOUTEZ ce switch pour basculer en mode PTC
          Card(
            margin: EdgeInsets.all(16),
            child: SwitchListTile(
              title: Text('Mode PTC Care'),
              subtitle: Text('Authentification avec le serveur PTC Care'),
              value: _isPTCMode,
              onChanged: (value) => setState(() => _isPTCMode = value),
              secondary: Icon(
                _isPTCMode ? Icons.cloud : Icons.offline_pin,
                color: _isPTCMode ? Colors.green : Colors.grey,
              ),
            ),
          ),
          
          // Votre bouton de connexion existant (modifié)
          ElevatedButton(
            onPressed: _isLoading ? null : () => _handleLogin(),
            child: _isLoading 
              ? CircularProgressIndicator()
              : Text(_isPTCMode ? 'Se connecter (PTC Care)' : 'Se connecter (Local)'),
          ),
        ],
      ),
    );
  }

  // MODIFIEZ votre méthode de connexion existante
  Future<void> _handleLogin() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() => _isLoading = true);
    
    final email = _emailController.text.trim();
    final password = _passwordController.text;
    
    AuthResult result;
    
    if (_isPTCMode) {
      // Mode PTC Care uniquement
      result = await _authService.authenticateWithPTC(email, password);
    } else {
      // Mode hybride (PTC + Local)
      result = await _authService.login(email, password);
    }
    
    if (result.isSuccess) {
      _handleLoginSuccess(result.user);
    } else {
      _showError(result.error);
    }
    
    setState(() => _isLoading = false);
  }

  void _handleLoginSuccess(User user) {
    if (user.isPTCUser) {
      // Afficher un message de succès PTC
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Connexion PTC Care réussie - ${user.ptcRole}'),
          backgroundColor: Colors.green,
        ),
      );
    }
    
    // Votre navigation existante
    _navigateToHome();
  }
}
```

### Tâche 2.4 : Adapter Votre Dashboard Existant (30 min)

**Fichier :** Votre écran principal/dashboard

```dart
// AJOUTEZ ces éléments à votre dashboard existant
class _YourExistingDashboardState extends State<YourExistingDashboard> {
  
  @override
  Widget build(BuildContext context) {
    final user = // Récupérer votre utilisateur actuel
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Dashboard'),
        actions: [
          // AJOUTEZ cet indicateur PTC
          if (user.isPTCUser)
            Chip(
              label: Text('PTC'),
              backgroundColor: Colors.green.shade100,
              avatar: Icon(Icons.cloud, size: 16),
            ),
          
          // Vos actions existantes...
        ],
      ),
      body: Column(
        children: [
          // AJOUTEZ cette carte d'information PTC
          if (user.isPTCUser)
            Card(
              margin: EdgeInsets.all(16),
              color: Colors.blue.shade50,
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.verified_user, color: Colors.blue),
                        SizedBox(width: 8),
                        Text(
                          'Compte PTC Care Connecté',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    SizedBox(height: 8),
                    Text('Rôle: ${_getRoleDisplay(user.ptcRole)}'),
                    Text('Username: ${user.username}'),
                    if (user.hospitalId != null)
                      Text('Hôpital ID: ${user.hospitalId}'),
                  ],
                ),
              ),
            ),
          
          // Votre contenu de dashboard existant...
          Expanded(
            child: _buildYourExistingDashboardContent(),
          ),
        ],
      ),
    );
  }

  String _getRoleDisplay(String? role) {
    switch (role) {
      case 'admin': return 'Administrateur';
      case 'docteur': return 'Médecin';
      case 'assistant': return 'Assistant';
      default: return 'Utilisateur';
    }
  }
}
```

---

## 🧪 PHASE 3 : Tests et Validation

### Tâche 3.1 : Tests de Connectivité (30 min)

**Fichier :** `lib/screens/debug/api_test_screen.dart` (nouveau, temporaire)

```dart
import 'package:flutter/material.dart';
import '../../services/ptc_api_service.dart';

class ApiTestScreen extends StatefulWidget {
  @override
  _ApiTestScreenState createState() => _ApiTestScreenState();
}

class _ApiTestScreenState extends State<ApiTestScreen> {
  final _ptcService = PTCApiService();
  String _status = 'Non testé';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Test API PTC Care')),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Statut API: $_status'),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _testApiConnection,
                      child: _isLoading 
                        ? CircularProgressIndicator()
                        : Text('Tester la Connexion'),
                    ),
                  ],
                ),
              ),
            ),
            
            // Formulaire de test de login
            Card(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  children: [
                    TextField(
                      decoration: InputDecoration(labelText: 'Email de test'),
                      controller: _emailController,
                    ),
                    TextField(
                      decoration: InputDecoration(labelText: 'Mot de passe'),
                      controller: _passwordController,
                      obscureText: true,
                    ),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: _testLogin,
                      child: Text('Tester Login'),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testApiConnection() async {
    setState(() => _isLoading = true);
    
    try {
      final isHealthy = await _ptcService.checkApiHealth();
      setState(() {
        _status = isHealthy ? 'API Disponible ✅' : 'API Non Disponible ❌';
      });
    } catch (e) {
      setState(() => _status = 'Erreur: $e');
    }
    
    setState(() => _isLoading = false);
  }

  Future<void> _testLogin() async {
    // Logique de test de login...
  }
}
```

### Tâche 3.2 : Tests de Validation (1h)

**Créer des tests unitaires :**

```dart
// test/auth_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:your_app/services/auth_service.dart';

void main() {
  group('AuthService PTC Integration', () {
    late AuthService authService;

    setUp(() {
      authService = AuthService();
    });

    test('should authenticate with valid PTC credentials', () async {
      // Test avec des identifiants PTC valides
      final result = await authService.authenticateWithPTC(
        '<EMAIL>', 
        'password123'
      );
      
      expect(result.isSuccess, true);
      expect(result.user?.isPTCUser, true);
    });

    test('should fallback to local auth when PTC fails', () async {
      // Test de fallback vers l'auth locale
      final result = await authService.login(
        '<EMAIL>', 
        'localpassword'
      );
      
      // Devrait réussir avec l'auth locale si PTC échoue
      expect(result.isSuccess, true);
    });
  });
}
```

---

## 📡 Configuration Postman pour Tests

### Collection Postman

**Fichier :** `PTC_Care_Mobile_Tests.postman_collection.json`

```json
{
  "info": {
    "name": "PTC Care Mobile Tests",
    "description": "Tests d'intégration pour application mobile Flutter"
  },
  "variable": [
    {
      "key": "base_url",
      "value": "http://localhost:8000"
    },
    {
      "key": "auth_token",
      "value": ""
    }
  ],
  "item": [
    {
      "name": "1. Test Connectivité",
      "request": {
        "method": "GET",
        "header": [],
        "url": {
          "raw": "{{base_url}}/api/initial-data/",
          "host": ["{{base_url}}"],
          "path": ["api", "initial-data", ""]
        }
      }
    },
    {
      "name": "2. Login PTC Care",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"admin123\"\n}"
        },
        "url": {
          "raw": "{{base_url}}/api/login/",
          "host": ["{{base_url}}"],
          "path": ["api", "login", ""]
        }
      },
      "event": [
        {
          "listen": "test",
          "script": {
            "exec": [
              "if (pm.response.code === 200) {",
              "    const response = pm.response.json();",
              "    pm.environment.set('auth_token', 'Bearer ' + response.user_id);",
              "    pm.environment.set('user_id', response.user_id);",
              "}"
            ]
          }
        }
      ]
    },
    {
      "name": "3. Test Authentification Mobile",
      "request": {
        "method": "POST",
        "header": [
          {
            "key": "Content-Type",
            "value": "application/json"
          }
        ],
        "body": {
          "mode": "raw",
          "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"agent123\"\n}"
        },
        "url": {
          "raw": "{{base_url}}/api/login/",
          "host": ["{{base_url}}"],
          "path": ["api", "login", ""]
        }
      }
    }
  ]
}
```

### Variables d'Environnement Postman

```json
{
  "name": "PTC Care Local",
  "values": [
    {
      "key": "base_url",
      "value": "http://localhost:8000",
      "enabled": true
    },
    {
      "key": "auth_token",
      "value": "",
      "enabled": true
    }
  ]
}
```

---

## ✅ Checklist de Validation

### Tests de Connectivité
- [ ] API accessible depuis `http://localhost:8000`
- [ ] Endpoint `/api/initial-data/` répond avec statut 200
- [ ] Endpoint `/api/login/` accessible

### Tests d'Authentification
- [ ] Login avec email PTC réussit
- [ ] Login avec identifiants invalides échoue correctement
- [ ] Fallback vers auth locale fonctionne
- [ ] Token d'authentification généré correctement

### Tests d'Intégration Mobile
- [ ] Utilisateur PTC peut se connecter sur mobile
- [ ] Données utilisateur correctement mappées
- [ ] Interface utilisateur adaptée (sans modification majeure)
- [ ] Indicateurs PTC Care visibles

### Tests de Compatibilité Web-Mobile
- [ ] Utilisateur créé sur mobile peut se connecter sur web
- [ ] Utilisateur créé sur web peut se connecter sur mobile
- [ ] Rôles et permissions cohérents

---

## 🚀 Commandes de Démarrage

```bash
# 1. Installer les dépendances
flutter pub get

# 2. Générer le code
flutter packages pub run build_runner build

# 3. Démarrer l'API Django (dans un terminal séparé)
cd /path/to/ptccare-web
python manage.py runserver

# 4. Lancer l'app Flutter
flutter run

# 5. Tester avec Postman
# Importer la collection et les environnements fournis
```

Cette approche préserve votre UI existante tout en ajoutant la compatibilité PTC Care de manière progressive et non-invasive.
