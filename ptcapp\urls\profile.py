from ..views import profile
from django.urls import path

urlpatterns = [
    path('', profile.create_view, name='profile.create'),
    path('patient/create', profile.createPatient, name='profile.patient.create'),
    # path('', profile.list_view, name='index' ),
    path('<id>', profile.detail_view, name='details' ),
    path('assistant/<id>', profile.attach_assistant ),
    #path('assistant/detach/<id>', profile.detach_assistant),
    path('picture/<id>', profile.set_picture ),
    path('delete/<id>', profile.delete_view, name='profile.delete' ),
    path('update/<id>', profile.update_view, name='profile.update' ),
    path('patient/update/<id>', profile.updatePatient, name='profile.patient.update' ),
    path('newpassword/', profile.change_credential, name='profile.credential' ),
]