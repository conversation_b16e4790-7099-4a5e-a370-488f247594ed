{% extends 'doctor/layout.html' %} 
{% load static %}
{% load layout %}

{% block up-style %}
    <!-- DataTables -->
    <link href="{% static 'libs/select2/css/select2.min.css' %}" rel="stylesheet" type="text/css" />
    <link href="{% static 'libs/dropzone/min/dropzone.min.css' %}" rel="stylesheet" type="text/css" />
    <style>
        fieldset{
            border: 1px solid !important;
            padding: 10px !important;
            height: 100% !important;
        }
        fieldset div label {
            width: 50% !important;
        }
        legend{
            padding-right: 10px !important;
            padding-left: 10px !important;
            display: inline-block;
            position: relative;
            bottom: 30px;
            background-color: white;
            width: fit-content !important;
        }

        label.required::after{
            content: "*";
            color: red;
            padding-left: 3px
        }
    </style>
{% endblock up-style %}

{% block content %}
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white text-left">
                    <span class="h2 text-capitalize">Modification grossesse</span>
                </div>
                <div class="card-body">
                    <form action="{% url 'pregnancy.update' id=pregnancy.id %}" method="POST">
                        {% csrf_token %}
                        {% comment %} <div class="row">
                            <div class="d-flex  justify-content-end">
                                <div class="form-check mx-2">
                                    <label for="current_doc" class="form-check-label required">Dossier actuel</label>
                                    <input class="form-check-input" type="radio" name="group" value="current_doc" id="current_doc" checked>
                                </div>
                                <div class="form-check mx-2">
                                    <label for="new_doc" class="form-check-label required">Nouveau Dossier</label>
                                    <input class="form-check-input" type="radio" name="group" value="new_doc" id="new_doc">
                                </div>
                            </div>
                        </div> {% endcomment %}
                        <div class="row">
                            <div class="col-md-6 col-sm-12 mt-4">
                                <fieldset class="h-100">
                                    <legend>Période de gestation</legend>
                                    {% comment %} <div class="d-flex align-items-center my-3">
                                        <label for="num_record" class="form-label required">Numéro du dossier</label>
                                        <input class="form-control" type="text" name="num_record" value="DOS-47852" id="num_record" required="required" disabled>
                                        <input class="form-control" type="hidden" name="current_num_record" value="DOS-47852" id="current_num_record" required="required">
                                    </div> {% endcomment %}
                                    {% comment %} <div class="d-flex align-items-center my-3">
                                        <label for="pregnancy_patient" class="form-label required">Sélectionner patient</label>
                                        <select class="select2 form-control" name="appointment_patient" id="appointment_patient" required="required">*
                                            {% for patient in patients %}
                                            {% if patient %}
                                            <option value="{{patient.id}}" {% if appointment.patient.id == patient.id %}selected{% endif %}>{{patient.user|auth_fullname}}</option>
                                            {% endif %}
                                            {% endfor %}
                                        </select>
                                    </div> {% endcomment %}
                                    <div class="d-flex align-items-center my-3">
                                        <label for="state" class="form-label required">Situation de la grossesse</label>
                                        <select class="select2 form-control" name="state" id="state" required="required">*           
                                            <option value="ended" selected>A terme</option>
                                            <option value="ongoing" {% if pregnancy.state == "ongoing" %}selected{% endif %}>En cours</option>
                                            <option value="interrupted" {% if pregnancy.state == "interrupted" %}selected{% endif %}>Interrompue</option>
                                        </select>
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="priority" class="form-label required">Priorité de la grossesse</label>
                                        <select class="select2 form-control" name="priority" id="priority" required="required">       
                                            <option value="low" {% if pregnancy.situation == "low" %}selected{% endif %}>Basse</option>
                                            <option value="middle" {% if pregnancy.situation == "middle" %}selected{% endif %}>Moyenne</option>
                                            <option value="high" {% if pregnancy.situation == "high" %}selected{% endif %}>Haute</option>
                                        </select>
                                    </div>
                                    {% comment %} <div class="d-flex align-items-center my-3">
                                        <label for="illness" class="form-label required">Maux ressentis</label>
                                        <input class="form-control" type="text" name="illness" placeholder="Maux ressentis" id="illness" required="required" va>
                                    </div> {% endcomment %}
                                    <div class="d-flex align-items-center my-3">
                                        <label for="start_date" class="form-label required">Date de début</label>
                                        <input class="form-control" type="date" name="start_date" placeholder="Date d'inscriptation" id="start_date" required value="{{pregnancy.start_date|date:"Y-m-d"}}">
                                    </div>
                                    <div class="d-flex align-items-start my-3">
                                        <label class="mb-1">Autrs informations</label>
                                        <textarea id="textarea" class="form-control" maxlength="" name="description" rows="3" placeholder="Autres informations">{{pregnancy.description}}</textarea>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="col-md-6 col-sm-12 mt-4">
                                <fieldset>
                                    <legend>Accouchement</legend>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="end_date" class="form-label">Date d'accouchement</label>
                                        <input class="form-control" type="date" name="end_date" placeholder="Date d'accouchement" id="end_date" value="{{pregnancy.end_date|date:"Y-m-d"}}">
                                    </div>
                                    <div class="d-flex align-items-center my-3">
                                        <label for="end_time" class="form-label">Heure d'accouchement</label>
                                        <input class="form-control" type="time" name="end_time" placeholder="Date d'accouchement" id="end_time" value="">
                                    </div>
                                    <div class="d-flex align-items-start my-3">
                                        <label class="mb-1">Nombre d'enfants</label>
                                        <input class="form-control" type="number" name="child_number" placeholder="Nombre d'enfants" id="child_number" value="{{pregnancy.child_number}}">
                                        {% comment %} <textarea id="textarea" class="form-control" maxlength="" name="child_number" rows="3" placeholder="Données significatives relevées">{{pregnancy.consul_data}}</textarea> {% endcomment %}
                                    </div>
                                    <div class="d-flex align-items-start my-3">
                                        <label class="mb-1">Etats de(s) enfants(s)</label>
                                        <textarea id="textarea" class="form-control" maxlength="" name="children_state" rows="3" placeholder="Etats de(s) enfants(s)"></textarea>
                                    </div>
                                </fieldset>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 col-sm-12 mt-4">
                                <fieldset>
                                    <legend>Informations</legend>
                                    <div class="d-flex align-items-start my-3">
                                        <label class="mb-1">Cause d'interruption de la grossesse</label>
                                        <textarea id="textarea" class="form-control" maxlength="" name="interruption_cause" rows="3" placeholder="Cause d'interruption de la grossesse"></textarea>
                                    </div>
                                    <div class="d-flex align-items-start my-3">
                                        <label class="mb-1">Etats de la mère</label>
                                        <textarea id="textarea" class="form-control" maxlength="" name="mother_state" rows="3" placeholder="Etats de(s) enfants(s)"></textarea>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="col-md-6 col-sm-12 mt-4">
                                <fieldset>
                                    <legend>Enfants</legend>
                                    <div class="d-flex align-items-start my-3">
                                        <label class="mb-1">Sélectionner les enfants</label>
                                        <select class="select2 form-control" name="priority" id="priority" multiple="multiple" required="required">
                                            {% comment %} {% for child in childs  %}
                                                <option value="{{child.id}}" >{{child.user|auth_fullname}}</option>
                                            {% endfor %} {% endcomment %}
                                        </select>
                                    </div>
                                    <span class="text-danger"><i class=" ri-information-fill"></i> S'assurer de créer au préalable les enfants concernés par la grossesse.</span> <br>
                                    <span class="text-danger"><i class=" ri-information-fill"></i> S'assurer de lier les enfants concernés par la grossesse.</span>
                                </fieldset>
                            </div>
                        </div>
                        {% comment %} <div class="row mt-2">
                            <div class="col-12">
                                <div class="dropzone">
                                    <div class="fallback">
                                        <input name="file" type="file" multiple="multiple">
                                    </div>
                                    <div class="dz-message needsclick">
                                        <div class="mb-3">
                                            <i class="display-4 text-muted ri-upload-cloud-2-line"></i>
                                        </div>
                                        
                                        <h4>Télécharger les documents</h4>
                                    </div>
                                </div>
                            </div>
                        </div> {% endcomment %}
                        <div class="row mt-2">
                            <div class="col">
                                <input class="btn btn-success w-auto" type="submit" value="Sauvegarder">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
{% endblock content %}

{% block down-script %}
    <script src="{% static 'libs/select2/js/select2.min.js'%}"></script>
    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        $('.select2').select2()
        $('.remove').on('click', function(e){
            e.preventDefault()
            $('.other-informations-body').children().last().remove();
        })

        $('#new_doc').click(function(){
            $('#num_record').val('DOS-')
            $('#num_record').attr('disabled', false)
        })

        $('#current_doc').click(function(){
            $('#num_record').val($('#current_num_record').val())
            $('#num_record').attr('disabled', true)
        })


    </script>
{% endblock down-script %}