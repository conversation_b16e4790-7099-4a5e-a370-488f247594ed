{
  "info": {
    "name": "PTC Care API Collection",
    "description": "Collection complète pour tester tous les endpoints API de PTC Care avec système de notification email",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json",
    "version": "1.0.0"
  },
  "variable": [
    {
      "key": "base_url",
      "value": "http://localhost:8000",
      "type": "string"
    },
    {
      "key": "user_id",
      "value": "",
      "type": "string"
    },
    {
      "key": "auth_token",
      "value": "",
      "type": "string"
    }
  ],
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{user_id}}",
        "type": "string"
      }
    ]
  },
  "event": [
    {
      "listen": "prerequest",
      "script": {
        "type": "text/javascript",
        "exec": [
          "// Script global de pré-requête",
          "console.log('Executing request to: ' + pm.request.url);"
        ]
      }
    },
    {
      "listen": "test",
      "script": {
        "type": "text/javascript",
        "exec": [
          "// Script global de test",
          "pm.test('Response time is less than 5000ms', function () {",
          "    pm.expect(pm.response.responseTime).to.be.below(5000);",
          "});",
          "",
          "pm.test('Response has valid JSON', function () {",
          "    pm.response.to.be.json;",
          "});"
        ]
      }
    }
  ],
  "item": [
    {
      "name": "🔐 Authentication",
      "item": [
        {
          "name": "Login - Email",
          "event": [
            {
              "listen": "test",
              "script": {
                "type": "text/javascript",
                "exec": [
                  "pm.test('Login successful', function () {",
                  "    pm.response.to.have.status(200);",
                  "    var jsonData = pm.response.json();",
                  "    pm.expect(jsonData.status).to.eql('success');",
                  "    ",
                  "    // Sauvegarder les variables d'authentification",
                  "    if (jsonData.user_id) {",
                  "        pm.collectionVariables.set('user_id', jsonData.user_id);",
                  "        pm.collectionVariables.set('auth_token', 'Bearer ' + jsonData.user_id);",
                  "        console.log('User ID saved: ' + jsonData.user_id);",
                  "    }",
                  "});",
                  "",
                  "pm.test('Response contains required fields', function () {",
                  "    var jsonData = pm.response.json();",
                  "    pm.expect(jsonData).to.have.property('user_id');",
                  "    pm.expect(jsonData).to.have.property('username');",
                  "    pm.expect(jsonData).to.have.property('role');",
                  "});"
                ]
              }
            }
          ],
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"admin123\"\n}"
            },
            "url": {
              "raw": "{{base_url}}/api/login/",
              "host": ["{{base_url}}"],
              "path": ["api", "login", ""]
            },
            "description": "Test de connexion avec email"
          }
        },
        {
          "name": "Login - Username",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"email\": \"ADM-12345678\",\n  \"password\": \"admin123\"\n}"
            },
            "url": {
              "raw": "{{base_url}}/api/login/",
              "host": ["{{base_url}}"],
              "path": ["api", "login", ""]
            },
            "description": "Test de connexion avec username"
          }
        },
        {
          "name": "Change Password - Current Password",
          "event": [
            {
              "listen": "test",
              "script": {
                "type": "text/javascript",
                "exec": [
                  "pm.test('Password change successful', function () {",
                  "    pm.response.to.have.status(200);",
                  "    var jsonData = pm.response.json();",
                  "    pm.expect(jsonData.status).to.eql('success');",
                  "});"
                ]
              }
            }
          ],
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"current_password\": \"TempPassword123\",\n  \"new_password\": \"NewSecurePassword123!\"\n}"
            },
            "url": {
              "raw": "{{base_url}}/api/change-password/",
              "host": ["{{base_url}}"],
              "path": ["api", "change-password", ""]
            },
            "description": "Changement de mot de passe avec mot de passe actuel"
          }
        },
        {
          "name": "Change Password - Token",
          "request": {
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"token\": \"your-reset-token-here\",\n  \"new_password\": \"NewSecurePassword123!\"\n}"
            },
            "url": {
              "raw": "{{base_url}}/api/change-password/",
              "host": ["{{base_url}}"],
              "path": ["api", "change-password", ""]
            },
            "description": "Changement de mot de passe avec token"
          }
        }
      ]
    },
    {
      "name": "👥 User Management",
      "item": [
        {
          "name": "Create Health Agent - Doctor",
          "event": [
            {
              "listen": "test",
              "script": {
                "type": "text/javascript",
                "exec": [
                  "pm.test('Agent created successfully', function () {",
                  "    pm.response.to.have.status(200);",
                  "    var jsonData = pm.response.json();",
                  "    pm.expect(jsonData.status).to.eql('success');",
                  "});",
                  "",
                  "pm.test('Email notification sent', function () {",
                  "    var jsonData = pm.response.json();",
                  "    pm.expect(jsonData).to.have.property('email_sent');",
                  "    pm.expect(jsonData.email_sent).to.be.true;",
                  "});",
                  "",
                  "pm.test('Username format is correct', function () {",
                  "    var jsonData = pm.response.json();",
                  "    pm.expect(jsonData.username).to.match(/^DOC-\\d{8}$/);",
                  "});"
                ]
              }
            }
          ],
          "request": {
            "auth": {
              "type": "bearer",
              "bearer": [
                {
                  "key": "token",
                  "value": "{{user_id}}",
                  "type": "string"
                }
              ]
            },
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"firstname\": \"Dr. Jean\",\n  \"lastname\": \"Dupont\",\n  \"tel\": \"+22912345678\",\n  \"email\": \"<EMAIL>\",\n  \"role\": \"docteur\",\n  \"sexe\": \"M\",\n  \"address\": \"123 Rue Médicale, Cotonou\",\n  \"hospital_id\": 1,\n  \"service_id\": 1,\n  \"speciality_id\": 1\n}"
            },
            "url": {
              "raw": "{{base_url}}/api/create-health-agent/",
              "host": ["{{base_url}}"],
              "path": ["api", "create-health-agent", ""]
            },
            "description": "Création d'un médecin avec notification email automatique"
          }
        },
        {
          "name": "Create Health Agent - Assistant",
          "request": {
            "auth": {
              "type": "bearer",
              "bearer": [
                {
                  "key": "token",
                  "value": "{{user_id}}",
                  "type": "string"
                }
              ]
            },
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"firstname\": \"Marie\",\n  \"lastname\": \"Assistant\",\n  \"tel\": \"+22912345679\",\n  \"email\": \"<EMAIL>\",\n  \"role\": \"assistant\",\n  \"sexe\": \"F\",\n  \"address\": \"456 Avenue Médicale, Cotonou\",\n  \"hospital_id\": 1,\n  \"service_id\": 1,\n  \"speciality_id\": 1\n}"
            },
            "url": {
              "raw": "{{base_url}}/api/create-health-agent/",
              "host": ["{{base_url}}"],
              "path": ["api", "create-health-agent", ""]
            },
            "description": "Création d'un assistant médical"
          }
        },
        {
          "name": "Create Patient - Adult",
          "event": [
            {
              "listen": "test",
              "script": {
                "type": "text/javascript",
                "exec": [
                  "pm.test('Patient created successfully', function () {",
                  "    pm.response.to.have.status(200);",
                  "    var jsonData = pm.response.json();",
                  "    pm.expect(jsonData.status).to.eql('success');",
                  "});",
                  "",
                  "pm.test('Email notification sent', function () {",
                  "    var jsonData = pm.response.json();",
                  "    pm.expect(jsonData.email_sent).to.be.true;",
                  "});",
                  "",
                  "pm.test('Username format is correct', function () {",
                  "    var jsonData = pm.response.json();",
                  "    pm.expect(jsonData.username).to.match(/^PAT-\\d{8}$/);",
                  "});"
                ]
              }
            }
          ],
          "request": {
            "auth": {
              "type": "bearer",
              "bearer": [
                {
                  "key": "token",
                  "value": "{{user_id}}",
                  "type": "string"
                }
              ]
            },
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"firstname\": \"Marie\",\n  \"lastname\": \"Kouassi\",\n  \"tel\": \"+22987654321\",\n  \"email\": \"<EMAIL>\",\n  \"sexe\": \"F\",\n  \"birth_date\": \"1990-05-15\",\n  \"address\": \"456 Avenue de la Paix, Cotonou\",\n  \"language_id\": 1,\n  \"occupation\": \"Enseignante\",\n  \"assurance\": \"CNSS\",\n  \"is_child\": false,\n  \"is_pregnant\": false\n}"
            },
            "url": {
              "raw": "{{base_url}}/api/create-patient/",
              "host": ["{{base_url}}"],
              "path": ["api", "create-patient", ""]
            },
            "description": "Création d'un patient adulte avec notification email"
          }
        },
        {
          "name": "Create Patient - Pregnant",
          "request": {
            "auth": {
              "type": "bearer",
              "bearer": [
                {
                  "key": "token",
                  "value": "{{user_id}}",
                  "type": "string"
                }
              ]
            },
            "method": "POST",
            "header": [
              {
                "key": "Content-Type",
                "value": "application/json"
              }
            ],
            "body": {
              "mode": "raw",
              "raw": "{\n  \"firstname\": \"Fatou\",\n  \"lastname\": \"Enceinte\",\n  \"tel\": \"+22987654322\",\n  \"email\": \"<EMAIL>\",\n  \"sexe\": \"F\",\n  \"birth_date\": \"1995-03-20\",\n  \"address\": \"789 Rue de la Maternité, Cotonou\",\n  \"language_id\": 1,\n  \"occupation\": \"Commerçante\",\n  \"is_child\": false,\n  \"is_pregnant\": true,\n  \"pregnancy_situation\": \"Normal\",\n  \"pregnancy_description\": \"Première grossesse\",\n  \"pregnancy_term\": \"Premier trimestre\",\n  \"pregnancy_start_date\": \"2024-01-01\",\n  \"mother_state\": \"Bonne santé\"\n}"
            },
            "url": {
              "raw": "{{base_url}}/api/create-patient/",
              "host": ["{{base_url}}"],
              "path": ["api", "create-patient", ""]
            },
            "description": "Création d'une patiente enceinte"
          }
        }
      ]
    }
