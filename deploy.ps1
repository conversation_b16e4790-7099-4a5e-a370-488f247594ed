# Script PowerShell pour déployer PTCCare sur Heroku
# Usage: .\deploy.ps1 -AppName "ptccare-app-2025"

param(
    [Parameter(Mandatory=$true)]
    [string]$AppName,
    
    [Parameter(Mandatory=$false)]
    [string]$AdminEmail = "<EMAIL>",
    
    [Parameter(Mandatory=$false)]
    [string]$AdminPassword = "admin123"
)

Write-Host "🚀 Déploiement de PTCCare sur Heroku" -ForegroundColor Green
Write-Host "App Name: $AppName" -ForegroundColor Yellow

# Vérifier si Heroku CLI est installé
try {
    heroku --version | Out-Null
    Write-Host "✅ Heroku CLI détecté" -ForegroundColor Green
} catch {
    Write-Host "❌ Heroku CLI non trouvé. Veuillez l'installer d'abord." -ForegroundColor Red
    exit 1
}

# Vérifier si Git est initialisé
if (-not (Test-Path ".git")) {
    Write-Host "📁 Initialisation de Git..." -ForegroundColor Yellow
    git init
    git add .
    git commit -m "Initial commit - PTC Care application"
}

# Créer l'application Heroku
Write-Host "🏗️ Création de l'application Heroku..." -ForegroundColor Yellow
try {
    heroku create $AppName
    Write-Host "✅ Application $AppName créée" -ForegroundColor Green
} catch {
    Write-Host "⚠️ L'application existe peut-être déjà, continuons..." -ForegroundColor Yellow
}

# Ajouter les add-ons
Write-Host "🔧 Configuration des add-ons..." -ForegroundColor Yellow
heroku addons:create heroku-postgresql:essential-0 -a $AppName
heroku addons:create heroku-redis:mini -a $AppName

# Configuration des variables d'environnement
Write-Host "⚙️ Configuration des variables d'environnement..." -ForegroundColor Yellow
$SecretKey = -join ((1..50) | ForEach {Get-Random -input ([char[]]([char]'a'..[char]'z') + ([char[]]([char]'A'..[char]'Z')) + 0..9)})

heroku config:set SECRET_KEY="$SecretKey" -a $AppName
heroku config:set DEBUG=False -a $AppName
heroku config:set ADMIN_EMAIL="$AdminEmail" -a $AppName
heroku config:set ADMIN_PASSWORD="$AdminPassword" -a $AppName
heroku config:set DEFAULT_FROM_EMAIL="<EMAIL>" -a $AppName
heroku config:set SMS_ENABLED=False -a $AppName

# Ajouter Heroku comme remote
Write-Host "🔗 Configuration du remote Git..." -ForegroundColor Yellow
heroku git:remote -a $AppName

# Déploiement
Write-Host "🚀 Déploiement en cours..." -ForegroundColor Yellow
git push heroku main

# Post-déploiement
Write-Host "🔧 Configuration post-déploiement..." -ForegroundColor Yellow
heroku run python manage.py migrate -a $AppName
heroku run python manage.py collectstatic --noinput -a $AppName

# Démarrer les workers
Write-Host "👷 Démarrage des workers..." -ForegroundColor Yellow
heroku ps:scale web=1 worker=1 -a $AppName

# Afficher les informations de l'application
Write-Host "📊 Informations de l'application:" -ForegroundColor Green
heroku info -a $AppName

Write-Host "🎉 Déploiement terminé avec succès!" -ForegroundColor Green
Write-Host "🌐 URL de l'application: https://$AppName.herokuapp.com" -ForegroundColor Cyan
Write-Host "🔐 Admin: $AdminEmail / $AdminPassword" -ForegroundColor Cyan
Write-Host "📱 API Mobile: https://$AppName.herokuapp.com/api/mobile/" -ForegroundColor Cyan

# Ouvrir l'application
$response = Read-Host "Voulez-vous ouvrir l'application dans le navigateur? (y/N)"
if ($response -eq "y" -or $response -eq "Y") {
    heroku open -a $AppName
}
