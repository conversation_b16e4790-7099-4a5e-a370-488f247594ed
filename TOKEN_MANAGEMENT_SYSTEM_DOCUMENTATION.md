# Documentation - Système de Gestion des Tokens PTC Care

## 🏗️ Architecture du Système de Tokens

Le système PTC Care utilise **deux modèles complémentaires** pour gérer l'authentification et les changements de mot de passe obligatoires.

## 📊 Modèles de Base de Données

### 1. **PasswordResetToken** - Stockage des Tokens

```python
class PasswordResetToken(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='password_reset_token')
    token = models.CharField(max_length=64, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)
    force_password_change = models.BooleanField(default=True)
```

**Table** : `ptcapp_password_reset_token`

**Champs** :
- `user` : Relation OneToOne avec User (un token par utilisateur)
- `token` : Token unique de 64 caractères
- `created_at` : Date de création automatique
- `expires_at` : Date d'expiration (48h par défaut)
- `is_used` : Booléen indiquant si le token a été utilisé
- `force_password_change` : Booléen pour forcer le changement

### 2. **UserPasswordStatus** - Statut des Utilisateurs

```python
class UserPasswordStatus(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='password_status')
    must_change_password = models.BooleanField(default=False)
    password_changed_at = models.DateTimeField(null=True, blank=True)
    first_login_completed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

**Table** : `ptcapp_user_password_status`

**Champs** :
- `user` : Relation OneToOne avec User
- `must_change_password` : Booléen pour forcer le changement
- `password_changed_at` : Date du dernier changement
- `first_login_completed` : Booléen première connexion
- `created_at/updated_at` : Timestamps automatiques

## 🔄 Cycle de Vie des Tokens

### **Phase 1 : Création d'Utilisateur**

#### 1.1 Endpoints de Création
- `POST /api/create-health-agent/` (Médecins, Assistants)
- `POST /api/create-patient/` (Patients)

#### 1.2 Processus de Création
```python
# 1. Création de l'utilisateur Django
user = User.objects.create_user(
    username=generated_username,
    email=email,
    password=temporary_password
)

# 2. Création du statut de mot de passe
UserPasswordStatus.objects.create(
    user=user,
    must_change_password=True  # Force le changement
)

# 3. Envoi email avec création automatique du token
EmailNotificationService.send_welcome_email(user, temporary_password, role)
```

#### 1.3 Création Automatique du Token
```python
# Dans EmailNotificationService.send_welcome_email()
token, created = PasswordResetToken.objects.get_or_create(
    user=user,
    defaults={'force_password_change': True}
)

# URL générée : /change-password/{token.token}/
password_change_url = f"{base_url}/change-password/{token.token}/"
```

### **Phase 2 : Demande de Réinitialisation**

#### 2.1 Endpoint de Demande
- `POST /api/request-password-reset/`

#### 2.2 Processus de Réinitialisation
```python
# 1. Invalider anciens tokens
PasswordResetToken.objects.filter(
    user=user,
    is_used=False,
    expires_at__gt=timezone.now()
).update(is_used=True)

# 2. Créer nouveau token
token = generate_secure_token()  # 64 caractères sécurisés
reset_token = PasswordResetToken.objects.create(
    user=user,
    token=token,
    expires_at=timezone.now() + timedelta(hours=48),
    is_used=False
)

# 3. Envoyer email de réinitialisation
EmailNotificationService.send_password_reset_email(user, reset_token)
```

### **Phase 3 : Utilisation du Token**

#### 3.1 Endpoints d'Utilisation
- `GET /change-password/{token}/` (Interface web)
- `POST /api/change-password/` (API avec token)

#### 3.2 Processus de Validation
```python
# Dans change_password_api()
try:
    reset_token = PasswordResetToken.objects.get(
        token=token,
        is_used=False,
        expires_at__gt=timezone.now()
    )
    
    # Valider et utiliser le token
    if reset_token.is_valid():
        # Changer le mot de passe
        user.set_password(new_password)
        user.save()
        
        # Marquer le token comme utilisé
        reset_token.mark_as_used()
        
        # Mettre à jour le statut utilisateur
        user.password_status.mark_password_changed()
        
except PasswordResetToken.DoesNotExist:
    return JsonResponse({'error': 'Token invalide'}, status=400)
```

## 🔗 Relations entre les Modèles

### **Diagramme de Relations**

```
User (Django Auth)
├── OneToOne → PasswordResetToken
│   ├── token (64 chars)
│   ├── expires_at (48h)
│   ├── is_used (bool)
│   └── force_password_change (bool)
│
├── OneToOne → UserPasswordStatus
│   ├── must_change_password (bool)
│   ├── password_changed_at (datetime)
│   └── first_login_completed (bool)
│
└── OneToOne → Profile
    ├── firstname, lastname
    ├── tel, email
    └── role, hospital, etc.
```

### **Relations Fonctionnelles**

#### 1. **User ↔ PasswordResetToken**
```python
# Accès depuis User
user.password_reset_token.token
user.password_reset_token.is_valid()

# Accès depuis Token
token.user.username
token.user.email
```

#### 2. **User ↔ UserPasswordStatus**
```python
# Accès depuis User
user.password_status.must_change_password
user.password_status.mark_password_changed()

# Vérification middleware
if user.password_status.must_change_password:
    # Rediriger vers changement
```

#### 3. **Synchronisation des États**
```python
# Quand token utilisé
reset_token.mark_as_used()  # is_used=True, force_password_change=False
user.password_status.mark_password_changed()  # must_change_password=False
```

## 📍 Endpoints et Gestion des Tokens

### **Tableau Récapitulatif**

| Endpoint | Action Token | Modèle Utilisé | Description |
|----------|--------------|----------------|-------------|
| `POST /api/create-health-agent/` | **Création** | PasswordResetToken + UserPasswordStatus | Token automatique pour nouvel agent |
| `POST /api/create-patient/` | **Création** | PasswordResetToken + UserPasswordStatus | Token automatique pour nouveau patient |
| `POST /api/request-password-reset/` | **Création** | PasswordResetToken | Nouveau token pour réinitialisation |
| `GET /change-password/{token}/` | **Validation** | PasswordResetToken | Interface web de changement |
| `POST /api/change-password/` | **Utilisation** | PasswordResetToken + UserPasswordStatus | API de changement avec token |
| `POST /api/login/` | **Vérification** | UserPasswordStatus | Vérification changement obligatoire |

### **Détail des Actions par Endpoint**

#### 1. **Création d'Utilisateur** (`/api/create-*`)
```python
# Actions automatiques :
1. User.objects.create_user()
2. UserPasswordStatus.objects.create(must_change_password=True)
3. PasswordResetToken.objects.get_or_create() # Dans send_welcome_email
4. Envoi email avec lien token
```

#### 2. **Demande de Réinitialisation** (`/api/request-password-reset/`)
```python
# Actions sécurisées :
1. Vérification rate limiting
2. Invalidation anciens tokens (is_used=True)
3. Création nouveau token sécurisé
4. Envoi email de réinitialisation
```

#### 3. **Changement de Mot de Passe** (`/api/change-password/`)
```python
# Actions de finalisation :
1. Validation token (non utilisé, non expiré)
2. Changement mot de passe utilisateur
3. reset_token.mark_as_used()
4. user.password_status.mark_password_changed()
5. Envoi email de confirmation
```

## 🔒 Sécurité et Validation

### **Génération Sécurisée des Tokens**

#### Méthode 1 : Modèle PasswordResetToken
```python
@staticmethod
def generate_token():
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(64))
```

#### Méthode 2 : Endpoint request-password-reset
```python
def generate_secure_token():
    return secrets.token_urlsafe(48)  # 64 caractères en base64url
```

### **Validation des Tokens**

#### Méthodes de Validation
```python
# Dans le modèle
def is_valid(self):
    return not self.is_used and not self.is_expired()

def is_expired(self):
    return timezone.now() > self.expires_at

# Utilisation
if reset_token.is_valid():
    # Token utilisable
else:
    # Token invalide/expiré
```

### **Invalidation Automatique**

#### 1. **Expiration Temporelle**
- **Durée** : 48 heures par défaut
- **Configuration** : `PASSWORD_RESET_TOKEN_EXPIRY_HOURS`
- **Vérification** : Automatique à chaque utilisation

#### 2. **Usage Unique**
```python
def mark_as_used(self):
    self.is_used = True
    self.force_password_change = False
    self.save()
```

#### 3. **Invalidation Préventive**
```python
# Lors de nouvelle demande de réinitialisation
PasswordResetToken.objects.filter(
    user=user,
    is_used=False,
    expires_at__gt=timezone.now()
).update(is_used=True)
```

## 🔄 Middleware et Intégration

### **Middleware de Changement Forcé**

Le middleware vérifie automatiquement le statut :

```python
# Vérification automatique
if hasattr(request.user, 'password_status'):
    if request.user.password_status.must_change_password:
        # Redirection vers changement obligatoire
        return redirect('/change-password/')
```

### **URLs Exemptées**
- `/logout`
- `/change-password/`
- `/api/change-password/`
- `/static/`
- `/admin/`

## 📊 Monitoring et Maintenance

### **Requêtes Utiles**

#### 1. **Tokens Actifs**
```sql
SELECT u.username, prt.token, prt.created_at, prt.expires_at
FROM ptcapp_password_reset_token prt
JOIN auth_user u ON prt.user_id = u.id
WHERE prt.is_used = FALSE AND prt.expires_at > NOW();
```

#### 2. **Utilisateurs Devant Changer leur Mot de Passe**
```sql
SELECT u.username, u.email, ups.created_at
FROM ptcapp_user_password_status ups
JOIN auth_user u ON ups.user_id = u.id
WHERE ups.must_change_password = TRUE;
```

#### 3. **Tokens Expirés à Nettoyer**
```sql
SELECT COUNT(*) FROM ptcapp_password_reset_token
WHERE expires_at < NOW() OR is_used = TRUE;
```

### **Tâches de Maintenance**

#### 1. **Nettoyage Automatique**
```python
# Commande Django à créer
def clean_expired_tokens():
    expired_count = PasswordResetToken.objects.filter(
        models.Q(expires_at__lt=timezone.now()) | models.Q(is_used=True)
    ).delete()
    return expired_count
```

#### 2. **Monitoring des Tokens**
```python
# Métriques recommandées
- Nombre de tokens actifs
- Taux d'utilisation des tokens
- Temps moyen entre création et utilisation
- Tokens expirés sans utilisation
```

---

**Résumé** : Le système utilise deux modèles complémentaires (`PasswordResetToken` et `UserPasswordStatus`) pour gérer de manière sécurisée les changements de mot de passe obligatoires et les réinitialisations, avec une intégration complète dans le workflow de création d'utilisateurs et d'authentification.
