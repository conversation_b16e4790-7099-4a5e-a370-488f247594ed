from django.db import models
from .profile import Profile
from .pregnancy import Pregnancy

class MotherPregnancy(models.Model):
    mother = models.ForeignKey(Profile, null = True, on_delete=models.SET_NULL)
    pregnancy = models.ForeignKey(Pregnancy, null = True, on_delete=models.SET_NULL)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    