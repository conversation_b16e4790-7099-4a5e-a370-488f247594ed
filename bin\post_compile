#!/usr/bin/env bash
# Script post-compilation pour Heroku

echo "-----> Collecte des fichiers statiques"
python manage.py collectstatic --noinput

echo "-----> Application des migrations"
python manage.py migrate --noinput

echo "-----> Création du superutilisateur si nécessaire"
python manage.py shell << EOF
from django.contrib.auth.models import User
from ptcapp.models import Profile, Hospital, Service, Speciality, Language
from django.contrib.auth.models import Group
import os

# Créer le superutilisateur admin si il n'existe pas
if not User.objects.filter(username='admin').exists():
    admin_user = User.objects.create_superuser(
        username='admin',
        email=os.environ.get('ADMIN_EMAIL', '<EMAIL>'),
        password=os.environ.get('ADMIN_PASSWORD', 'admin123'),
        first_name='Administrateur',
        last_name='PTC Care'
    )
    
    # Créer les groupes s'ils n'existent pas
    admin_group, created = Group.objects.get_or_create(name='admin')
    admin_user.groups.add(admin_group)
    
    print("Superutilisateur admin créé avec succès")
else:
    print("Superutilisateur admin existe déjà")
EOF

echo "-----> Déploiement terminé avec succès"
