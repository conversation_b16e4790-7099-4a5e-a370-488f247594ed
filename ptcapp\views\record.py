from django.shortcuts import redirect, render,get_object_or_404,HttpResponseRedirect

# relative import of forms
from ptcapp.models import Record
from ptcapp.forms.record import RecordForm


def create_view(request):
    context = {}
    form = RecordForm(request.POST or None)
    if form.is_valid():
        form.save()
        return HttpResponseRedirect("/record")

    context['form'] = form
    return render(request, "record/form.html", context)



def list_view(request):
    context ={}

    context["dataset"] = Record.objects.all()
    return render(request, "record/list.html", context)


def detail_view(request, id):
    
    context ={}

    context["data"] = Record.objects.get(id = id)

    return render(request, "record/detail.html", context)


def update_view(request, id):

    context ={}

    obj = get_object_or_404(Record, id = id)

    form = RecordForm(request.POST, instance = obj)

    if form.is_valid():
        form.save()
        return HttpResponseRedirect("/")

    context["form"] = form

    return render(request, "record/update.html", context)
    

def delete_view(request, id):
    context ={}

    obj = get_object_or_404(Record, id = id)

    if request.method =="POST":
        obj.delete()
        return HttpResponseRedirect("/")

    return render(request, "record/delete.html", context)
