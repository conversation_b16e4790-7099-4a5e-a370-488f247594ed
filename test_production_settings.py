#!/usr/bin/env python
"""
Script de test pour vérifier les paramètres de production
"""
import os
import sys

# Ajouter le répertoire du projet au path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Configuration Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ptccare.settings_production')

try:
    import django
    django.setup()
    
    from django.conf import settings
    from django.core.management import execute_from_command_line
    
    print("✅ Configuration de production chargée avec succès")
    print(f"📊 DEBUG: {settings.DEBUG}")
    print(f"🔐 SECRET_KEY: {'*' * 20} (masqué)")
    print(f"🏠 ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
    print(f"🗄️ Base de données: {settings.DATABASES['default']['ENGINE']}")
    
    # Test de vérification Django
    print("\n🔍 Exécution de la vérification Django...")
    execute_from_command_line(['manage.py', 'check'])
    print("✅ Vérification Django réussie")
    
except Exception as e:
    print(f"❌ Erreur lors du test: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

print("\n🎉 Test de configuration de production terminé avec succès !")
