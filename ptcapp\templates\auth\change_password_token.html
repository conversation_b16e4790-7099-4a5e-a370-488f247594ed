{% load static %}

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Définir votre mot de passe - PTC Care</title>
    <!-- App favicon -->
    <link rel="shortcut icon" href="{% static 'favicon.ico' %}">
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/app.min.css' %}">
    <link rel="stylesheet" href="{% static 'css/login.css' %}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f6fbff;
        }

        /* Styles spécifiques pour le changement de mot de passe */
        .password-requirements {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .requirement {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .requirement i {
            margin-right: 10px;
            width: 16px;
        }

        .requirement.valid {
            color: #28a745;
        }

        .requirement.invalid {
            color: #dc3545;
        }

        .input-group-password {
            position: relative;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #6c757d;
            z-index: 10;
        }

        .user-info {
            background-color: #e8f5e8;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #28a745;
        }

        .form-section h1 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .btn-primary:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .expiry-info {
            text-align: center;
            margin-top: 15px;
            font-size: 0.875rem;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="log-container">
        <div class="log-section mobile-hide">
            <img src="{% static 'images/log-img.jpg' %}" alt="PTC Care">
        </div>
        <div class="log-section">
            <img src="{% static 'images/logo-light.png' %}" class="logo" alt="PTC Care Logo">
            <div class="form-section">
                <h1>Définir votre mot de passe</h1>

                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <div class="user-info">
                    <h6><i class="fas fa-user me-2"></i>Informations du compte</h6>
                    <p class="mb-1"><strong>Email :</strong> {{ user.email }}</p>
                    <p class="mb-0"><strong>Nom d'utilisateur :</strong> {{ user.username }}</p>
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Première connexion :</strong> Choisissez un mot de passe sécurisé pour protéger votre compte.
                </div>
            
                <form method="post" id="changePasswordForm">
                    {% csrf_token %}

                    <div class="form-group">
                        <label class="form-label" for="new_password">
                            <i class="fas fa-lock me-2"></i>Nouveau mot de passe
                        </label>
                        <div class="input-group-password">
                            <input class="form-control" id="new_password" name="new_password" type="password" required>
                            <span class="password-toggle" onclick="togglePassword('new_password')">
                                <i class="fas fa-eye"></i>
                            </span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="confirm_password">
                            <i class="fas fa-check-double me-2"></i>Confirmer le mot de passe
                        </label>
                        <div class="input-group-password">
                            <input class="form-control" id="confirm_password" name="confirm_password" type="password" required>
                            <span class="password-toggle" onclick="togglePassword('confirm_password')">
                                <i class="fas fa-eye"></i>
                            </span>
                        </div>
                    </div>
                
                    <div class="password-requirements">
                        <h6><i class="fas fa-shield-alt me-2"></i>Exigences du mot de passe :</h6>
                        <div class="requirement" id="length-req">
                            <i class="fas fa-times"></i>
                            Au moins 8 caractères
                        </div>
                        <div class="requirement" id="uppercase-req">
                            <i class="fas fa-times"></i>
                            Au moins une lettre majuscule
                        </div>
                        <div class="requirement" id="lowercase-req">
                            <i class="fas fa-times"></i>
                            Au moins une lettre minuscule
                        </div>
                        <div class="requirement" id="number-req">
                            <i class="fas fa-times"></i>
                            Au moins un chiffre
                        </div>
                        <div class="requirement" id="special-req">
                            <i class="fas fa-times"></i>
                            Au moins un caractère spécial (!@#$%^&*)
                        </div>
                        <div class="requirement" id="match-req">
                            <i class="fas fa-times"></i>
                            Les mots de passe correspondent
                        </div>
                    </div>

                    <div class="form-group text-center">
                        <input class="btn btn-primary my-3" type="submit" value="Définir mon mot de passe" id="submitBtn" disabled>
                    </div>
                </form>

                <div class="expiry-info">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        Ce lien expire dans {{ reset_token.expires_at|timeuntil }}
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- JAVASCRIPT -->
    <script src="{% static 'js/bootstrap.bundle.min.js' %}"></script>
    <script src="{% static 'js/app.js' %}"></script>

    <script>
        function togglePassword(fieldId) {
            const field = document.getElementById(fieldId);
            const icon = field.parentElement.querySelector('.password-toggle i');

            if (field.type === 'password') {
                field.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                field.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
        
        function validatePassword() {
            const password = document.getElementById('new_password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            // Validation des exigences
            const requirements = {
                'length-req': password.length >= 8,
                'uppercase-req': /[A-Z]/.test(password),
                'lowercase-req': /[a-z]/.test(password),
                'number-req': /\d/.test(password),
                'special-req': /[!@#$%^&*(),.?":{}|<>]/.test(password),
                'match-req': password === confirmPassword && password.length > 0
            };
            
            let allValid = true;
            
            for (const [reqId, isValid] of Object.entries(requirements)) {
                const element = document.getElementById(reqId);
                const icon = element.querySelector('i');
                
                if (isValid) {
                    element.classList.add('valid');
                    element.classList.remove('invalid');
                    icon.classList.remove('fa-times');
                    icon.classList.add('fa-check');
                } else {
                    element.classList.add('invalid');
                    element.classList.remove('valid');
                    icon.classList.remove('fa-check');
                    icon.classList.add('fa-times');
                    allValid = false;
                }
            }
            
            // Activer/désactiver le bouton
            document.getElementById('submitBtn').disabled = !allValid;
        }
        
        // Écouter les changements dans les champs de mot de passe
        document.getElementById('new_password').addEventListener('input', validatePassword);
        document.getElementById('confirm_password').addEventListener('input', validatePassword);

        // Validation initiale
        validatePassword();
    </script>
</body>
</html>
