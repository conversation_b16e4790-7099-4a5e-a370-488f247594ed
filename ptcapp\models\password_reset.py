from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta
import secrets
import string


class PasswordResetToken(models.Model):
    """
    Modèle pour gérer les tokens de changement de mot de passe obligatoire
    pour les nouveaux utilisateurs.
    """
    user = models.OneToOneField(
        User, 
        on_delete=models.CASCADE,
        related_name='password_reset_token'
    )
    token = models.CharField(max_length=64, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    is_used = models.BooleanField(default=False)
    force_password_change = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'ptcapp_password_reset_token'
        verbose_name = 'Token de changement de mot de passe'
        verbose_name_plural = 'Tokens de changement de mot de passe'
    
    def save(self, *args, **kwargs):
        if not self.token:
            self.token = self.generate_token()
        if not self.expires_at:
            from django.conf import settings
            expiry_hours = getattr(settings, 'PASSWORD_RESET_TOKEN_EXPIRY_HOURS', 48)
            self.expires_at = timezone.now() + timedelta(hours=expiry_hours)
        super().save(*args, **kwargs)
    
    @staticmethod
    def generate_token():
        """Génère un token sécurisé de 64 caractères."""
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(64))
    
    def is_expired(self):
        """Vérifie si le token a expiré."""
        return timezone.now() > self.expires_at
    
    def is_valid(self):
        """Vérifie si le token est valide (non utilisé et non expiré)."""
        return not self.is_used and not self.is_expired()
    
    def mark_as_used(self):
        """Marque le token comme utilisé."""
        self.is_used = True
        self.force_password_change = False
        self.save()
    
    def __str__(self):
        return f"Token pour {self.user.username} - {'Valide' if self.is_valid() else 'Invalide'}"


class UserPasswordStatus(models.Model):
    """
    Modèle pour suivre le statut de changement de mot de passe des utilisateurs.
    """
    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='password_status'
    )
    must_change_password = models.BooleanField(default=False)
    password_changed_at = models.DateTimeField(null=True, blank=True)
    first_login_completed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'ptcapp_user_password_status'
        verbose_name = 'Statut de mot de passe utilisateur'
        verbose_name_plural = 'Statuts de mot de passe utilisateur'
    
    def mark_password_changed(self):
        """Marque le mot de passe comme changé."""
        self.must_change_password = False
        self.password_changed_at = timezone.now()
        self.first_login_completed = True
        self.save()
    
    def require_password_change(self):
        """Force le changement de mot de passe."""
        self.must_change_password = True
        self.first_login_completed = False
        self.save()
    
    def __str__(self):
        status = "Doit changer" if self.must_change_password else "OK"
        return f"{self.user.username} - {status}"
