#!/bin/bash
# Script de tâches de release pour Heroku

echo "Exécution des tâches de release..."

# Application des migrations
echo "Application des migrations..."
python manage.py migrate --noinput

# Collecte des fichiers statiques
echo "Collecte des fichiers statiques..."
python manage.py collectstatic --noinput

# Création des données de base si nécessaire
echo "Création des données de base..."
python manage.py shell << EOF
from django.contrib.auth.models import Group
from ptcapp.models import Hospital, Service, Speciality, Language

# Créer les groupes d'utilisateurs
groups = ['admin', 'docteur', 'assistant', 'patient']
for group_name in groups:
    group, created = Group.objects.get_or_create(name=group_name)
    if created:
        print(f"Groupe {group_name} créé")

# Créer les langues de base
languages_data = [
    {'name': 'Français', 'path': 'fr'},
    {'name': 'Fon', 'path': 'fon'},
    {'name': 'Yoruba', 'path': 'yo'},
    {'name': '<PERSON><PERSON><PERSON>', 'path': 'en'}
]

for lang_data in languages_data:
    language, created = Language.objects.get_or_create(
        name=lang_data['name'],
        defaults=lang_data
    )
    if created:
        print(f"Langue {language.name} créée")

print("Données de base créées avec succès")
EOF

echo "Tâches de release terminées avec succès"
