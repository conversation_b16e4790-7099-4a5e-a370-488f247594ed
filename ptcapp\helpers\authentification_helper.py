from django.http import HttpResponseForbidden
from django.shortcuts import redirect

def authenticated_user(user):
    return user.is_authenticated


def go_home(user):
    if user.groups.filter(name='patient'):
        return redirect('/patient/')
    if user.groups.filter(name='assistant'):
        return redirect('/assistant/')
    if user.groups.filter(name='docteur'):
        return redirect('/docteur/')
    if user.groups.filter(name='admin') or user.is_staff == 1:
        return redirect('/admin/')

def is_admin(user):
    return user.groups.filter(name='admin') or user.is_staff == 1

def is_doctor(user):
    return user.groups.filter(name='docteur') 

def is_assistant(user):
    return user.groups.filter(name='assistant') 

def is_patient(user):
    return user.groups.filter(name='patient')