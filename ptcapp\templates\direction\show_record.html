{% extends 'direction/layout.html' %} {% load static %} {% load layout %} {% block up-style %}
<!-- DataTables -->
<link href="{% static 'libs/select2/css/select2.min.css' %}" rel="stylesheet" type="text/css" />
<link href="{% static 'libs/datatables.net-bs4/css/dataTables.bootstrap4.min.css' %}" rel="stylesheet" type="text/css" /> 
{% endblock up-style %} 
{% block action_button %}
<a href="javascript:;" id="addAppointment" 
data-bs-toggle="modal" 
data-bs-target=".bs-example-modal-center"  class="btn btn-primary mx-3">Ajouter une consultation</a>
{% endblock action_button %}
{% block content %}

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between">
                    <span class="h2 text-left text-capitalize"><PERSON><PERSON><PERSON> de {{profile.lastname}} {{profile.firstname}}</span>
                    <div class="col-md-6 col-sm-12">
                        <div class="d-flex justify-content-end">
                            <select class="w-50 form-control select2" name="associe_doctor" id="associe_doctor" >
                                {% for docteur in docteurs %}                                              
                                <option value="{{docteur.pk}}" {% if docteur.pk == profile.map_doctor_patient.patient %} selected {% endif %}>{{docteur.user|auth_fullname}}</option>                                                                                                   
                                {% endfor %}
                            </select>
                            <button class="btn btn-primary mx-3">Associé</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8 col-sm-12">
                        <div class="row">
                            <h4 class="text-capitalize"> identification </h4>
                            <div class="col-md-6 col-sm-12">
                                <h6>Nom complet</h6>
                                <p>{{profile.lastname}} {{profile.firstname}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h6>Sexe</h6>
                                <p>{{profile.sexe}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h6>Date de naissance</h6>
                                <p>{{profile.birth_date}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h6>Numéro du dossier</h6>
                                <p>???</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h6>Signes particuliers</h6>
                                <p>{{profile.special_marks}}</p>
                            </div>
                        </div>
                        <div class="row">
                            <h4 class="text-capitalize"> Informations administratives </h4>
                            <div class="col-md-6 col-sm-12">
                                <h6>Adresse</h6>
                                <p>{{profile.address}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h6>Téléphone</h6>
                                <p>{{profile.tel}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h6>Profession</h6>
                                <p>{{profile.occupation}}</p>
                            </div>
                            <div class="col-md-6 col-sm-12">
                                <h6>Assurance</h6>
                                <p>{{profile.assurance}}</p>
                            </div>
                        </div>
                        <div class="row">
                            <h4 class="text-capitalize"> Données d'alerte </h4>
                            <div class="col-md-12 col-sm-12">
                                <h6>Allergies / intolérances médicamenteuses</h6>
                                <p>{{profile.allergies}}</p>
                            </div>
                        </div>
                        <div class="row">
                            <h4 class="text-capitalize"> Histoire médicale actualisée et facteurs de santé </h4>
                            <div class="col-md-12 col-sm-12">
                                <h6>Antécédents personnels</h6>
                                <p>{{profile.personal_history}}</p>
                            </div>
                            <div class="col-md-12 col-sm-12">
                                <h6>Antécédents familiaux</h6>
                                <p>{{profile.family_history}}</p>
                            </div>                          
                            <div class="col-md-12 col-sm-12">
                                <h6>Vaccinations et autres actions de prévention et de dépistage</h6>
                                <p>{{profile.vaccinations}}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 col-sm-12">
                        <h4>Consultations</h4>
                        <table id="datatable" class="table table-bordered table-striped dt-responsive nowrap" style="border-collapse: collapse; border-spacing: 0; width: 100%">
                            <thead>
                                <tr>
                                    <th>Nom du médécin</th>
                                    <th>Date de rencontre</th>
                                    <th>Action</th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr>
                                    <td>Jessica DOE</td>
                                    <td>2011/04/25</td>
                                    <td><a href="{% url 'patient.consultation.show' id=148 %}" data-toggle="tooltip" data-placement="right" title="Détails"><i class="ri-eye-line" ></i></a></td>
                                </tr>
                                <tr>
                                    <td>John GRICH</td>
                                    <td>2011/07/25</td>
                                    <td><a href="{% url 'patient.consultation.show' id=148 %}" data-toggle="tooltip" data-placement="right" title="Détails"><i class="ri-eye-line" ></i></a></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade bs-example-modal-center" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modal-title">Créer une consultation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form action="#" method="post">
                    <div class="row">
                        <div class="d-none justify-content-end">
                            <div class="form-check mx-2">
                                <label for="current_doc" class="form-check-label required">Dossier actuel</label>
                                <input class="form-check-input" type="radio" name="group" value="current_doc" id="current_doc" checked>
                            </div>
                            <div class="form-check mx-2">
                                <label for="new_doc" class="form-check-label required">Nouveau Dossier</label>
                                <input class="form-check-input" type="radio" name="group" value="new_doc" id="new_doc">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 col-sm-12 mt-4">
                            {% comment %} <fieldset class="h-100"> {% endcomment %}
                                {% comment %} <legend>Informations Générales</legend> {% endcomment %}
                                <input type="hidden" name="_method" id="method" value="post">
                                <div class="my-3">
                                    <label for="num_record" class="form-label required">Numéro du dossier</label>
                                    <input class="form-control" type="text" name="num_record" value="DOS-{{id}}" id="num_record" required="required" disabled>
                                    <input class="form-control" type="hidden" name="current_num_record" value="DOS-{{id}}" id="current_num_record" required="required">
                                </div>
                                <div class="my-3">
                                    <label for="type_appointment" class="form-label required">Type de consultation</label>
                                    <input class="form-control" type="text" name="type_appointment" placeholder="Type de consultation" id="type_appointment" required="required">
                                </div>
                                <div class="my-3">
                                    <label for="illness" class="form-label required">Maux ressentis</label>
                                    <input class="form-control" type="text" name="illness" placeholder="Maux ressentis" id="illness" required="required">
                                </div>
                            {% comment %} </fieldset> {% endcomment %}
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 col-sm-12">
                            <label for="consul_date" class="form-label required">Date de la Consultaion</label>
                            <input class="form-control" type="date" name="consul_date" placeholder="Date d'inscriptation" id="consul_date" required>
                        </div>
                        <div class="col-md-6 col-sm-12">
                            <label for="consul_time" class="form-label required">Heure de la Consultaion</label>
                            <input class="form-control" type="time" name="consul_time" placeholder="Date d'inscriptation" id="consul_time" required>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col">
                            <input class="btn btn-success w-auto" id="form-submit" type="submit" value="Sauvegarder">
                        </div>
                    </div>
                </form>
            </div>
        </div><!-- /.modal-content -->
    </div><!-- /.modal-dialog -->
</div><!-- /.modal -->
{% endblock content %} {% block down-script %}
<script src="{% static 'libs/select2/js/select2.min.js'%}"></script>
    <script src="//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json"></script>
    <!-- Required datatable js -->
    <script src="{% static 'libs/datatables.net/js/jquery.dataTables.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-bs4/js/dataTables.bootstrap4.min.js'%}"></script>
    <!-- Responsive examples -->
    <script src="{% static 'libs/datatables.net-responsive/js/dataTables.responsive.min.js'%}"></script>
    <script src="{% static 'libs/datatables.net-responsive-bs4/js/responsive.bootstrap4.min.js'%}"></script>
    <!-- Datatable init js -->
    {% comment %} <script src="{% static 'js/pages/datatables.init.js' %}"></script> {% endcomment %}
    <script>
        $('.select2').select2()
        var dt = $("#datatable").DataTable({
            searching: false,
            language: {
                url: '//cdn.datatables.net/plug-ins/1.11.5/i18n/fr-FR.json'
            },
            order : [[2, 'desc']],
            drawCallback: function() {
                $(".dataTables_paginate > .pagination").addClass("pagination-rounded");
            },
        });
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })

        $('#new_doc').click(function(){
            $('#num_record').val('DOS-')
            $('#num_record').attr('disabled', false)
        })

        $('#current_doc').click(function(){
            $('#num_record').val($('#current_num_record').val())
            $('#num_record').attr('disabled', true)
        })


        $('#form-submit').click(function(e){
            e.preventDefault()
            $('.bs-example-modal-center').modal('toggle');
            Swal.fire("Création!", "", "success");
        })
    </script>
{% endblock down-script %}