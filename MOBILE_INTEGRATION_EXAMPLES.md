# Exemples d'Intégration Mobile - Login avec Token

## 📱 Guide d'Intégration pour Applications Mobiles

L'endpoint `POST /api/login/` a été modifié pour inclure automatiquement les informations de changement de mot de passe et le token de réinitialisation. Voici comment l'intégrer dans vos applications mobiles.

## 🔄 Nouveau Workflow de Connexion

### **Avant (Ancien Workflow)**
```
1. POST /api/login/ → Obtenir user_id
2. Vérifier si changement requis (middleware ou autre appel)
3. Si requis → POST /api/request-password-reset/
4. Attendre email → Extraire token du lien
5. Rediriger vers changement de mot de passe
```

### **Maintenant (Nouveau Workflow)**
```
1. POST /api/login/ → Obtenir toutes les infos nécessaires
2. Si must_change_password = true ET password_reset_token présent
   → Rediriger directement vers changement de mot de passe
3. Sinon → Connexion normale
```

## 📋 Format de Réponse de l'API

### **Réponse avec Token (Nouveau Utilisateur)**
```json
{
  "status": "success",
  "user_id": 123,
  "username": "PAT-12345678",
  "profile_id": 456,
  "role": "patient",
  "name": "Jean Dupont",
  "hospital_id": 1,
  "service_id": 2,
  "speciality_id": null,
  "must_change_password": true,
  "password_reset_token": "abc123def456ghi789jkl012mno345pqr678stu901vwx234yz"
}
```

### **Réponse sans Token (Utilisateur Normal)**
```json
{
  "status": "success",
  "user_id": 124,
  "username": "DOC-87654321",
  "profile_id": 457,
  "role": "agent",
  "name": "Dr. Marie Martin",
  "hospital_id": 1,
  "service_id": 3,
  "speciality_id": 5,
  "must_change_password": false
}
```

## 🔧 Implémentations par Plateforme

### **Android (Kotlin)**

#### Service de Connexion
```kotlin
data class LoginRequest(
    val email: String,
    val password: String
)

data class LoginResponse(
    val status: String,
    val user_id: Int,
    val username: String,
    val profile_id: Int,
    val role: String,
    val name: String,
    val hospital_id: Int?,
    val service_id: Int?,
    val speciality_id: Int?,
    val must_change_password: Boolean,
    val password_reset_token: String? = null // Optionnel
)

class AuthService {
    suspend fun login(email: String, password: String): LoginResult {
        try {
            val response = apiClient.post<LoginResponse>(
                "/api/login/",
                LoginRequest(email, password)
            )
            
            return when {
                response.must_change_password && response.password_reset_token != null -> {
                    LoginResult.PasswordChangeRequired(response, response.password_reset_token)
                }
                response.must_change_password && response.password_reset_token == null -> {
                    LoginResult.TokenExpiredOrMissing(response)
                }
                else -> {
                    LoginResult.Success(response)
                }
            }
        } catch (e: Exception) {
            return LoginResult.Error(e.message ?: "Erreur de connexion")
        }
    }
}

sealed class LoginResult {
    data class Success(val user: LoginResponse) : LoginResult()
    data class PasswordChangeRequired(val user: LoginResponse, val token: String) : LoginResult()
    data class TokenExpiredOrMissing(val user: LoginResponse) : LoginResult()
    data class Error(val message: String) : LoginResult()
}
```

#### Activité de Connexion
```kotlin
class LoginActivity : AppCompatActivity() {
    private val authService = AuthService()
    
    private fun handleLogin(email: String, password: String) {
        lifecycleScope.launch {
            when (val result = authService.login(email, password)) {
                is LoginResult.Success -> {
                    // Connexion normale
                    navigateToMainActivity(result.user)
                }
                
                is LoginResult.PasswordChangeRequired -> {
                    // Redirection vers changement de mot de passe avec token
                    navigateToPasswordChange(result.token, result.user)
                }
                
                is LoginResult.TokenExpiredOrMissing -> {
                    // Demander un nouveau token
                    showPasswordResetDialog(result.user.user_id)
                }
                
                is LoginResult.Error -> {
                    showError(result.message)
                }
            }
        }
    }
    
    private fun navigateToPasswordChange(token: String, user: LoginResponse) {
        val intent = Intent(this, PasswordChangeActivity::class.java).apply {
            putExtra("token", token)
            putExtra("user_id", user.user_id)
            putExtra("username", user.username)
        }
        startActivity(intent)
        finish()
    }
}
```

### **iOS (Swift)**

#### Service de Connexion
```swift
struct LoginRequest: Codable {
    let email: String
    let password: String
}

struct LoginResponse: Codable {
    let status: String
    let user_id: Int
    let username: String
    let profile_id: Int
    let role: String
    let name: String
    let hospital_id: Int?
    let service_id: Int?
    let speciality_id: Int?
    let must_change_password: Bool
    let password_reset_token: String?
}

enum LoginResult {
    case success(LoginResponse)
    case passwordChangeRequired(LoginResponse, String)
    case tokenExpiredOrMissing(LoginResponse)
    case error(String)
}

class AuthService {
    func login(email: String, password: String) async -> LoginResult {
        do {
            let request = LoginRequest(email: email, password: password)
            let response: LoginResponse = try await apiClient.post("/api/login/", body: request)
            
            switch (response.must_change_password, response.password_reset_token) {
            case (true, let token?) where !token.isEmpty:
                return .passwordChangeRequired(response, token)
            case (true, _):
                return .tokenExpiredOrMissing(response)
            default:
                return .success(response)
            }
        } catch {
            return .error(error.localizedDescription)
        }
    }
}
```

#### Contrôleur de Connexion
```swift
class LoginViewController: UIViewController {
    private let authService = AuthService()
    
    @IBAction func loginButtonTapped(_ sender: UIButton) {
        guard let email = emailTextField.text,
              let password = passwordTextField.text else { return }
        
        Task {
            let result = await authService.login(email: email, password: password)
            
            await MainActor.run {
                switch result {
                case .success(let user):
                    navigateToMainScreen(user: user)
                    
                case .passwordChangeRequired(let user, let token):
                    navigateToPasswordChange(token: token, user: user)
                    
                case .tokenExpiredOrMissing(let user):
                    showPasswordResetAlert(userId: user.user_id)
                    
                case .error(let message):
                    showError(message: message)
                }
            }
        }
    }
    
    private func navigateToPasswordChange(token: String, user: LoginResponse) {
        let storyboard = UIStoryboard(name: "Main", bundle: nil)
        let passwordChangeVC = storyboard.instantiateViewController(withIdentifier: "PasswordChangeViewController") as! PasswordChangeViewController
        
        passwordChangeVC.token = token
        passwordChangeVC.userId = user.user_id
        passwordChangeVC.username = user.username
        
        navigationController?.pushViewController(passwordChangeVC, animated: true)
    }
}
```

### **React Native (JavaScript)**

#### Service de Connexion
```javascript
class AuthService {
  async login(email, password) {
    try {
      const response = await fetch(`${API_BASE_URL}/api/login/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Erreur de connexion');
      }

      const userData = await response.json();

      // Analyser la réponse
      if (userData.must_change_password) {
        if (userData.password_reset_token) {
          return {
            type: 'PASSWORD_CHANGE_REQUIRED',
            user: userData,
            token: userData.password_reset_token,
          };
        } else {
          return {
            type: 'TOKEN_EXPIRED_OR_MISSING',
            user: userData,
          };
        }
      } else {
        return {
          type: 'SUCCESS',
          user: userData,
        };
      }
    } catch (error) {
      return {
        type: 'ERROR',
        message: error.message,
      };
    }
  }
}
```

#### Composant de Connexion
```javascript
import React, { useState } from 'react';
import { View, TextInput, Button, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';

const LoginScreen = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const navigation = useNavigation();
  const authService = new AuthService();

  const handleLogin = async () => {
    const result = await authService.login(email, password);

    switch (result.type) {
      case 'SUCCESS':
        // Sauvegarder les données utilisateur
        await AsyncStorage.setItem('user', JSON.stringify(result.user));
        navigation.navigate('MainTabs');
        break;

      case 'PASSWORD_CHANGE_REQUIRED':
        // Rediriger vers changement de mot de passe avec token
        navigation.navigate('PasswordChange', {
          token: result.token,
          userId: result.user.user_id,
          username: result.user.username,
        });
        break;

      case 'TOKEN_EXPIRED_OR_MISSING':
        // Proposer de demander un nouveau token
        Alert.alert(
          'Changement de mot de passe requis',
          'Votre token a expiré. Voulez-vous en demander un nouveau ?',
          [
            { text: 'Annuler', style: 'cancel' },
            { 
              text: 'Demander un nouveau token', 
              onPress: () => requestNewToken(result.user.user_id) 
            },
          ]
        );
        break;

      case 'ERROR':
        Alert.alert('Erreur', result.message);
        break;
    }
  };

  return (
    <View>
      <TextInput
        placeholder="Email"
        value={email}
        onChangeText={setEmail}
        keyboardType="email-address"
      />
      <TextInput
        placeholder="Mot de passe"
        value={password}
        onChangeText={setPassword}
        secureTextEntry
      />
      <Button title="Se connecter" onPress={handleLogin} />
    </View>
  );
};
```

## 🔗 Construction de l'URL de Changement

### **Format de l'URL**
```
{BASE_URL}/change-password/{token}/
```

### **Exemple**
```
https://ptccare.com/change-password/abc123def456ghi789jkl012mno345pqr678stu901vwx234yz/
```

### **Utilisation dans l'App Mobile**
```javascript
// Construction de l'URL pour WebView ou navigateur externe
const passwordChangeUrl = `${BASE_URL}/change-password/${token}/`;

// Ouvrir dans WebView
navigation.navigate('WebView', { url: passwordChangeUrl });

// Ou ouvrir dans le navigateur externe
Linking.openURL(passwordChangeUrl);
```

## 🛡️ Gestion des Erreurs

### **Cas d'Erreur Possibles**
1. **Token expiré** : `must_change_password = true` mais pas de `password_reset_token`
2. **Token utilisé** : Même situation que ci-dessus
3. **Pas de statut** : `must_change_password` absent ou `null`

### **Gestion Recommandée**
```javascript
const handlePasswordChangeRequired = (userData) => {
  if (userData.password_reset_token) {
    // Token disponible, redirection directe
    navigateToPasswordChange(userData.password_reset_token);
  } else {
    // Token manquant/expiré, demander un nouveau
    showTokenRequestDialog(userData.user_id);
  }
};
```

## 📊 Avantages de la Nouvelle Implémentation

### **Pour les Développeurs**
- ✅ **Un seul appel API** au lieu de deux
- ✅ **Logique simplifiée** de gestion des états
- ✅ **Moins de code** à maintenir
- ✅ **Meilleure performance** (moins de requêtes réseau)

### **Pour les Utilisateurs**
- ✅ **Expérience plus fluide** (pas d'attente d'email)
- ✅ **Redirection automatique** vers le bon écran
- ✅ **Moins d'étapes** dans le processus
- ✅ **Feedback immédiat** sur le statut du compte

---

**L'intégration mobile est maintenant simplifiée et plus efficace grâce à l'inclusion automatique du token de réinitialisation dans la réponse de connexion !** 🚀
